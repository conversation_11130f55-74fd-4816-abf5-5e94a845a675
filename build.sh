#!/bin/bash

# 清理并打包项目，跳过测试
echo "开始打包前端..."
rm -rf src/main/resources/static/*
cd fox_front
npm run build
cp dist/* ../src/main/resources/static -r
cd ..
echo "打包前端完成"
echo "开始打包后端..."
mvn clean package -DskipTests -Dmaven.build.timestamp
echo "打包后端完成"

# 获取当前时间作为标签
TIMESTAMP=$(date +%Y%m%d%H%M%S)

# 构建在线授权版本的 Docker 镜像
echo "构建在线授权版本镜像..."
docker build --build-arg SYSTEM_VER=$TIMESTAMP \
            --build-arg AUTH_MODE=online \
            -t xiaomifengd/chatgpt-share-server-fox:${TIMESTAMP}-online \
            -t xiaomifengd/chatgpt-share-server-fox:online \
            .

# 构建离线授权版本的 Docker 镜像
echo "构建离线授权版本镜像..."
docker build --build-arg SYSTEM_VER=$TIMESTAMP \
            --build-arg AUTH_MODE=offline \
            -t xiaomifengd/chatgpt-share-server-fox:${TIMESTAMP}-offline \
            -t xiaomifengd/chatgpt-share-server-fox:offline \
            .

## 推送镜像（如果需要）
# docker push xiaomifengd/chatgpt-share-server-fox:${TIMESTAMP}-online
# docker push xiaomifengd/chatgpt-share-server-fox:online
# docker push xiaomifengd/chatgpt-share-server-fox:${TIMESTAMP}-offline
# docker push xiaomifengd/chatgpt-share-server-fox:offline
