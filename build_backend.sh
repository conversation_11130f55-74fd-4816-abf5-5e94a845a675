
echo "开始打包后端..."
mvn clean package -DskipTests -Dmaven.build.timestamp
echo "打包后端完成"

# 获取当前时间作为标签
TIMESTAMP=$(date +%Y%m%d%H%M%S)

# 构建 Docker 镜像，带上时间戳和 latest 标签
docker build --build-arg SYSTEM_VER=$TIMESTAMP  . -t xiaomifengd/chatgpt-share-server-fox:$TIMESTAMP -t xiaomifengd/chatgpt-share-server-fox:latest

## 推送带有时间戳的镜像
#docker push xiaomifengd/chatgpt-share-server-fox:$TIMESTAMP
#
## 推送 latest 标签的镜像
#docker push xiaomifengd/chatgpt-share-server-fox:latest
