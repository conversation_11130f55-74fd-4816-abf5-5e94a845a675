spring.datasource.url=${SPRING_DATASOURCE_URL}
spring.datasource.username=${SPRING_DATASOURCE_USERNAME}
spring.datasource.password=${SPRING_DATASOURCE_PASSWORD}
spring.data.redis.port=${SPRING_REDIS_PORT}
spring.data.redis.host=${SPRING_REDIS_HOST}
chat-share-server.apiauth=${APIAUTH}
system.ver=${system.ver}
app.file-storage.base-path=/data/upload
app.file-storage.file-path=/data/file
chatshare-server.host=chatgpt-share-server
claudeshare-server.host=http://dddd-share-server:8001
grokshare-server.host=http://grok-share-server:8001
spring.flyway.validate-on-migrate=false
admin.password=${ADMIN_PASSWORD:null}