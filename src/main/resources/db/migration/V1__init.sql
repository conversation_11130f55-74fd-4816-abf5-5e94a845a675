CREATE TABLE `chatgpt_config`
(
    `id`         bigint                                                    NOT NULL AUTO_INCREMENT COMMENT '??',
    `createTime` datetime(3)                                               NOT NULL COMMENT '????',
    `updateTime` datetime(3)                                               NOT NULL COMMENT '????',
    `deleted_at` datetime(3) DEFAULT NULL,
    `key`        longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'key',
    `value`      longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '?',
    `remark`     longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '??',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC;
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (1, '2024-10-31 15:42:31.481', '2024-10-31 15:42:31.481', null, 'smtpHost', '', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (2, '2024-10-31 15:42:31.481', '2024-10-31 15:42:31.481', null, 'smtpPort', '465', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (3, '2024-10-31 15:42:31.481', '2024-10-31 15:42:31.481', null, 'senderEmail', '', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (4, '2024-10-31 15:42:31.481', '2024-10-31 15:42:31.481', null, 'emailPassword', '', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (5, '2024-10-31 15:42:31.481', '2024-10-31 15:42:31.481', null, 'emailWhiteList', 'qq.com,foxmail.com', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (6, '2024-10-31 15:44:13.566', '2024-10-31 15:44:13.566', null, 'canRegister', 'true', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (7, '2024-10-31 15:44:13.566', '2024-10-31 15:44:13.566', null, 'registerGiftPlus', 'true', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (8, '2024-10-31 15:44:13.566', '2024-10-31 15:44:13.566', null, 'canLoginMulti', 'false', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (9, '2024-10-31 15:44:13.566', '2024-10-31 15:44:13.566', null, 'registerUseTime', '10', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (10, '2024-10-31 15:44:13.566', '2024-10-31 15:44:13.566', null, 'registerGiftTime', '10', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (11, '2024-10-31 15:44:13.566', '2024-10-31 15:44:13.566', null, 'useLimitPer', '1h', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (12, '2024-10-31 15:44:13.566', '2024-10-31 15:44:13.566', null, 'useLimit', '10', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (13, '2024-10-31 15:44:13.566', '2024-10-31 15:44:13.566', null, 'loginExpire', '24', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (14, '2024-10-31 22:14:17.038', '2024-10-31 22:14:17.038', null, 'showVersion', 'false', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (15, '2024-10-31 22:14:17.038', '2024-10-31 22:14:17.038', null, 'systemName', 'chatgpt', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (16, '2024-10-31 22:14:17.038', '2024-10-31 22:14:17.038', null, 'logRetentionDays', '30', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (17, '2024-10-31 22:15:27.273', '2024-10-31 22:15:27.273', null, 'claudeUrl', '', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (18, '2024-10-31 22:15:27.273', '2024-10-31 22:15:27.273', null, 'noteSite', '', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (19, '2024-10-31 22:15:27.273', '2024-10-31 22:15:27.273', null, 'issuingCardSite', '', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (20, '2024-10-31 22:19:13.975', '2024-10-31 22:19:13.975', null, 'systemLogo',
        '', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (21, '2024-11-05 10:14:24.332', '2024-11-05 10:14:24.332', null, 'nodeFreeSize', '0', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (22, '2024-11-05 10:14:24.332', '2024-11-05 10:14:24.332', null, 'virtualPlusSize', '1', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (23, '2024-11-05 10:14:24.332', '2024-11-05 10:14:24.332', null, 'nodeFreeName', '免费节点', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (24, '2024-11-05 10:14:24.332', '2024-11-05 10:14:24.332', null, 'node4oName', '4o节点', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (25, '2024-11-05 10:14:24.332', '2024-11-05 10:14:24.332', null, 'nodePlusName', 'plus节点', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (26, '2024-11-05 10:14:24.332', '2024-11-05 10:14:24.332', null, 'nodeClaudeName', 'claude节点', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (27, '2024-11-11 14:28:11.364', '2024-11-11 14:28:11.364', null, 'O1PREVIEWPer', '1w', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (28, '2024-11-11 14:28:11.364', '2024-11-11 14:28:11.364', null, 'O1PREVIEWLimit', '5', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (29, '2024-11-11 14:28:11.364', '2024-11-11 14:28:11.364', null, 'O1MINIPer', '1d', null);
INSERT INTO cool.chatgpt_config (id, createTime, updateTime, deleted_at, `key`, value, remark)
VALUES (30, '2024-11-11 14:28:11.364', '2024-11-11 14:28:11.364', null, 'O1MINILimit', '5', null);



alter table chatgpt_user
    add column plusExpireTime datetime(3),
    add column email          varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    add column password       varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    add column isAdmin        tinyint(1),
    add column `limit`        int          DEFAULT 20,
    add column per            varchar(191) default '',
    add column user_type      int(11)      default 1 comment '0:体验用户,1:普通用户,2:普通会员,3:高级会员',
    add column enable_claude  tinyint(1)   default 0 comment '是否支持claude',
    add column status         int(11)      default 0 comment '-1:封禁,0:正常',
    add column invite_code    varchar(64)  default '' comment '邀请码',
    add column invite_by      varchar(64)  default '' comment '邀请人';
INSERT INTO cool.chatgpt_user (id, createTime, updateTime, deleted_at, userToken, expireTime, isPlus, remark,
                             plusExpireTime, email, password, isAdmin, `limit`, per, user_type,
                               enable_claude, status, invite_code)
VALUES (1, '2024-10-23 10:23:44.603', '2024-10-23 10:23:44.603', null, 'admin', '9999-01-01 00:00:00.000', 1, null,
         '9999-01-01 00:00:00.000', '<EMAIL>', 'JwMMPxLt73SCG+beaTmYl95CY5sZ1ST4g+9qhKIwjMI=', 1, 20, '1h', 3,
        1, 0, '');
CREATE TABLE invitation_records
(
    id              INT AUTO_INCREMENT PRIMARY KEY,
    inviter_name    VARCHAR(100) NOT NULL COMMENT '邀请人名称',
    invitee_name    VARCHAR(100) NOT NULL COMMENT '被邀人名称',
    invitation_time DATETIME     NOT NULL COMMENT '邀请时间',
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);
CREATE TABLE system_notification
(
    id         BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    content    TEXT     NOT NULL COMMENT '通知内容(HTML)',
    type       TINYINT  NOT NULL COMMENT '通知类型:1-首页通知,2-站内通知,3-站内提醒',
    status     TINYINT  NOT NULL DEFAULT 1 COMMENT '状态:0-关闭,1-开启',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='系统通知表';
CREATE TABLE forbidden_words
(
    id         bigint AUTO_INCREMENT PRIMARY KEY,                              -- 主键
    word       VARCHAR(255) NOT NULL,                                          -- 违禁词
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,                            -- 记录创建时间
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP -- 最后更新时间
);
CREATE TABLE `sales_plan`
(
    `id`                  INT AUTO_INCREMENT PRIMARY KEY,                                               -- 售卖计划唯一ID
    `name`                VARCHAR(64)    NOT NULL,
    `amount`              DECIMAL(10, 2) NOT NULL,                                                      -- 金额，精度为10位整数和2位小数
    `valid_days`          INT            NOT NULL,                                                      -- 有效天数
    `membership_type`     VARCHAR(50),                                                                  -- 会员类型
    `is_display_on_front` BOOLEAN        NOT NULL DEFAULT 1,                                            -- 前台是否展示，默认为展示(1:展示, 0:不展示)
    `per`                 VARCHAR(16),                                                                  -- 速率周期，如"天", "周", "月"
    `limit`               int(11)        not null,                                                      -- 速率，精度为10位整数和2位小数
    `order`               int(11),
    `tags`                varchar(1024),
    `created_at`          TIMESTAMP               DEFAULT CURRENT_TIMESTAMP,                            -- 创建时间，默认当前时间
    `updated_at`          TIMESTAMP               DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP -- 修改时间，更新时自动更新时间
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
CREATE TABLE `coupon`
(
    `id`                  INT AUTO_INCREMENT PRIMARY KEY,                                            -- 优惠券唯一ID
    `coupon_code`         VARCHAR(50) NOT NULL UNIQUE,                                               -- 优惠代码，唯一
    `discount_amount`     DECIMAL(10, 2),                                                            -- 优惠金额，精度为10位整数和2位小数
    `discount_percentage` DECIMAL(5, 2),                                                             -- 优惠折扣，精度为5位整数和2位小数
    `sales_plan`          varchar(64) NOT NULL DEFAULT '',                                           -- 订阅
    `expiration_time`     TIMESTAMP,                                                                 -- 优惠券过期时间
    `remark`              VARCHAR(255),
    `created_at`          TIMESTAMP            DEFAULT CURRENT_TIMESTAMP,                            -- 创建时间，默认当前时间
    `updated_at`          TIMESTAMP            DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP -- 修改时间，更新时自动更新时间
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
CREATE TABLE payment_methods
(
    id           INT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    name         VARCHAR(50) NOT NULL COMMENT '名称',
    payment_type VARCHAR(50) NOT NULL COMMENT '支付方式',
    appid        VARCHAR(100) COMMENT '应用ID',
    appkey       VARCHAR(100) COMMENT '应用密钥',
    callback_url VARCHAR(255) COMMENT '回调地址',
    payment_url  VARCHAR(255) COMMENT '支付地址',
    is_enabled   TINYINT(1) DEFAULT 1 COMMENT '是否启用，1表示启用，0表示禁用',
    created_at   TIMESTAMP  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at   TIMESTAMP  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间'
) COMMENT ='支付方式表';
CREATE TABLE orders
(
    id           INT AUTO_INCREMENT PRIMARY KEY,                                               -- 主键ID
    amount       DECIMAL(10, 2) NOT NULL,                                                      -- 订单金额
    username     VARCHAR(50)    NOT NULL,                                                      -- 用户名
    trade_no     VARCHAR(50)    NOT NULL UNIQUE,                                               -- 内部订单号
    out_trade_no VARCHAR(50)             DEFAULT NULL,                                         -- 外部订单号
    status       int(11)        NOT NULL DEFAULT 0,                                            -- 订单状态 0-待支付，1-已支付
    sales_plan   VARCHAR(100)   NOT NULL,                                                      -- 对应套餐
    coupon_code  VARCHAR(50)             DEFAULT NULL,                                         -- 优惠券
    buy_info     varchar(1024)  not null default '',                                           -- 购买信息
    remarks      varchar(255)            DEFAULT NULL,                                         -- 备注
    created_at   TIMESTAMP               DEFAULT CURRENT_TIMESTAMP,                            -- 创建时间
    updated_at   TIMESTAMP               DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP -- 修改时间
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
