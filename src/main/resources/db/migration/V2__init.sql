create table cool.claude_session
(
    id           bigint auto_increment
        primary key,
    carid        varchar(255) null,
    email        varchar(255) null,
    account_type int          null,
    enabled      tinyint      null,
    session      varchar(255) null,
    remarks      varchar(255) null,
    createdAt    timestamp    null,
    updatedAt    timestamp    null
);

create table cool.activation_code
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    activation_code varchar(255)                       not null comment '激活码',
    sales_plan_id   bigint                             not null comment '订阅ID',
    sales_plan_name varchar(255)                       null comment '订阅名称',
    status          tinyint  default 0                 not null comment '状态：0=未使用，1=已使用',
    used_by         varchar(255)                       null comment '兑换人',
    used_at         datetime                           null comment '兑换时间',
    expiration_time datetime                           null comment '到期时间',
    remark          varchar(1024)                      null comment '备注',
    created_at      datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updated_at      datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint activation_code
        unique (activation_code)
)
    comment '激活码管理表';

