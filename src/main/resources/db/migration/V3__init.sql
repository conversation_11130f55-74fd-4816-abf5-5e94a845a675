
CREATE TABLE user_group (
                            id BIGINT PRIMARY KEY AUTO_INCREMENT,
                            name VA<PERSON>HA<PERSON>(100) NOT NULL,
                        title VARCHAR(100) NOT NULL,
                            description VARCHAR(255),
                            is_deleted BOOLEAN DEFAULT FALSE,
                            available_nodes VARCHAR(255),
                            auto_mini_share_gpt4_limit BOOLEAN DEFAULT TRUE,
                            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE group_rate_limit (
                                  id BIGINT PRIMARY KEY AUTO_INCREMENT,
                                  group_id BIGINT NOT NULL,
                                  model VARCHAR(100) NOT NULL,
                                  rate INT NOT NULL,
                                  `period` VARCHAR(50) NOT NULL,
                                  multiplier INT DEFAULT 1,
                                  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
insert into  user_group (id,name,title, description, available_nodes)
values (1, 'free','免费用户', '注册时候的默认免费分组', 'free');
insert into  user_group (id,name,title, description, available_nodes)
values (2, 'general', '普通会员', '普通会员', 'free,4o');

insert into  user_group (id,name,title, description, available_nodes)
values (3, 'plus', '高级会员', '高级会员', 'free,4o,plus');
ALTER TABLE chatgpt_user
    ADD COLUMN group_id BIGINT NOT NULL DEFAULT 1;
