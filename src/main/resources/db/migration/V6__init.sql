create table if not exists user_rate_limit
(
    id         bigint auto_increment
        primary key,
    user_token   varchar(64)                             not null,
    model      varchar(100)                       not null,
    rate       int                                not null,
    `period` varchar (50) not null,
    multiplier int      default 1                 null,
    created_at datetime default CURRENT_TIMESTAMP not null,
    updated_at datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
);

insert into user_group (id, name, title, description, available_nodes)
values (-1, 'visitor', '游客', '未登录用户', 'free,4o,plus');