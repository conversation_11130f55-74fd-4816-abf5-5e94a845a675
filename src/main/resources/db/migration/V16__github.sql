CREATE TABLE `oauth2_client` (
    `id` int primary key  auto_increment,
                                 `client_id` VARCHAR(100) NOT NULL COMMENT '客户端唯一标识',
                                 `client_secret` VARCHAR(200) NOT NULL COMMENT '客户端密钥（加密存储）',
                                 `scope` VARCHAR(500) NOT NULL COMMENT '权限范围，多个用空格分隔',
                                 `type` VARCHAR(64) NOT NULL COMMENT 'oauth2类型，github',
    `redirect_uri` varchar(500),
                                 `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OAuth2客户端配置表';
