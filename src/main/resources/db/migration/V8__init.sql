create table image_generation_records
(
    id                       bigint auto_increment comment '主键ID'
        primary key,
    operation_type           varchar(16)                          not null comment '操作类型：GENERATE-生成图像, EDIT-编辑图像',
    prompt                   text                                 null comment '提示词',
    model                    varchar(128)                         null comment '使用的模型',
    requested_count          int                                  null comment '请求的图片数量',
    size                     varchar(32)                          null comment '图片尺寸',
    quality                  varchar(32)                          null comment '图片质量',
    source_image_files       text                                 null comment '源图片文件名（JSON数组）',
    mask_image_file          varchar(255)                         null comment '遮罩图片文件名',
    generated_images         text                                 null comment '生成的图片URL或Base64数据（JSON数组）',
    openai_created_timestamp bigint                               null comment 'OpenAI返回的创建时间戳',
    created_at               datetime   default CURRENT_TIMESTAMP null comment '记录创建时间',
    updated_at               datetime   default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '记录更新时间',
    is_successful            tinyint(1)                           null comment '操作是否成功',
    error_message            text                                 null comment '错误信息（如果有）',
    is_deleted               tinyint(1) default 0                 null comment '逻辑删除标记',
    user_token               varchar(128)                         null comment '用户token',
    status                   int        default 0                 null comment '状态：1-生成中，2-生成完成，3-生成失败'
)
    comment '图像生成记录实体' charset = utf8mb4;

create table image_storage
(
    id                bigint auto_increment comment '主键ID'
        primary key,
    record_id         bigint                               null comment '关联的记录ID',
    image_type        varchar(16)                          not null comment '图片类型：SOURCE-源图片, MASK-遮罩图片, GENERATED-生成的图片',
    original_filename varchar(255)                         null comment '原始文件名',
    stored_filename   varchar(255)                         null,
    file_size         bigint                               null comment '文件大小（字节）',
    content_type      varchar(128)                         null comment '文件类型（MIME类型）',
    image_url         varchar(512)                         null comment '图片URL（对于生成的图片）',
    md5_hash          varchar(64)                          null comment 'MD5哈希值',
    created_at        datetime   default CURRENT_TIMESTAMP null comment '记录创建时间',
    updated_at        datetime   default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '记录更新时间',
    is_deleted        tinyint(1) default 0                 null comment '逻辑删除标记'
)
    comment '图片存储实体' charset = utf8mb4;


alter table image_generation_records add column  point int default 0 comment '消耗积分';

INSERT INTO chatgpt_config (createTime, updateTime, deleted_at, `key`, value, remark)
VALUES ('2024-11-11 14:28:11.364', '2024-11-11 14:28:11.364', null, 'draw_model', 'gpt-image-1', null);
INSERT INTO chatgpt_config (createTime, updateTime, deleted_at, `key`, value, remark)
VALUES ('2024-11-11 14:28:11.364', '2024-11-11 14:28:11.364', null, 'register_gift_points', '30', null);
INSERT INTO chatgpt_config (createTime, updateTime, deleted_at, `key`, value, remark)
VALUES ('2024-11-11 14:28:11.364', '2024-11-11 14:28:11.364', null, 'draw_consume_points', '10', null);
-- 创建用户积分记录表
CREATE TABLE `user_points_record` (
                                      `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `user_token` varchar(128) NOT NULL COMMENT '用户Token',
                                      `points_amount` int NOT NULL COMMENT '积分数量，正数表示获得积分，负数表示消耗积分',
                                      `record_type` tinyint NOT NULL COMMENT '记录类型：1-获得积分，2-消耗积分',
                                      `source_type` tinyint NOT NULL COMMENT '积分来源/消耗类型：1-注册奖励，2-管理员新增，3-邀请奖励，4-图片生成预消耗，5-图片生成失败退还，6-积分购买',
                                      `source_id` varchar(64) DEFAULT NULL COMMENT '关联的业务ID，如订单ID、图片生成记录ID等',
                                      `description` varchar(255) DEFAULT NULL COMMENT '描述信息',
                                      `balance_after` int NOT NULL COMMENT '变动后的积分余额',
                                      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `is_deleted` tinyint(1) DEFAULT '0' COMMENT '逻辑删除标记',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_user_points_record_user_token` (`user_token`),
                                      KEY `idx_user_points_record_created_at` (`created_at`),
                                      KEY `idx_user_points_record_record_type` (`record_type`),
                                      KEY `idx_user_points_record_source_type` (`source_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户积分记录表';

-- 为现有用户添加初始积分记录（可选）
-- INSERT INTO `user_points_record` (`user_token`, `points_amount`, `record_type`, `source_type`, `description`, `balance_after`, `created_at`)
-- SELECT userToken, 100, 1, 1, '初始积分', 100, NOW() FROM draw_user WHERE deleted_at IS NULL;
INSERT INTO chatgpt_config (createTime, updateTime, deleted_at, `key`, value, remark)
VALUES ('2024-11-11 14:28:11.364', '2024-11-11 14:28:11.364', null, 'imageRetentionDays', '0', null);
INSERT INTO chatgpt_config (createTime, updateTime, deleted_at, `key`, value, remark)
VALUES ('2024-11-11 14:28:11.364', '2024-11-11 14:28:11.364', null, 'imageQuality', 'high', null);
