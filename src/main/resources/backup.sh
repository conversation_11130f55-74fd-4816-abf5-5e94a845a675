#!/bin/bash
# 设置变量
CONTAINER_NAME="chatgpt-share-expander-mysql-1"
DB_NAME="cool"
BACKUP_DIR="/root/mysql/backup"
DATE=$(date +\%F-\%H-\%M-\%S)
BACKUP_FILE="${BACKUP_DIR}/${DB_NAME}-${DATE}.sql"
BUCKET_NAME="hangzhou-1"
# 创建备份目录（如果不存在）
mkdir -p $BACKUP_DIR

# 运行备份命令
docker exec $CONTAINER_NAME /usr/bin/mysqldump -u root --password=123456 $DB_NAME > $BACKUP_FILE

# 检查备份是否成功
if [ $? -eq 0 ]; then
  echo "Backup successful: $BACKUP_FILE"
else
  echo "Backup failed"
fi

# 删除超过 3 天的备份文件
find $BACKUP_DIR -type f -name "${DB_NAME}-*.sql" -mtime +3 -exec rm -f {} \;


# 上传到oss
ossutil -c ~/ossconfig cp $BACKUP_FILE oss://${BUCKET_NAME}/$BACKUP_FILE
echo "upload to oss success"