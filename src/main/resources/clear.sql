drop table flyway_schema_history;
drop table chatgpt_user;
CREATE TABLE `chatgpt_user`
(
    `id`         bigint unsigned                     NOT NULL AUTO_INCREMENT,
    `createTime` datetime(3)                         NOT NULL COMMENT '创建时间',
    `updateTime` datetime(3)                         NOT NULL COMMENT '更新时间',
    `deleted_at` datetime(3) DEFAULT NULL,
    `userToken`  longtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'UserToken',
    `expireTime` datetime(3)                         NOT NULL COMMENT '过期时间',
    `isPlus`     tinyint(1)  DEFAULT '0' COMMENT 'PLUS',
    `remark`     longtext COLLATE utf8mb4_unicode_ci COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_chatgpt_user_deleted_at` (`deleted_at`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
drop table chatgpt_config;
drop table invitation_records;

drop table system_notification;
drop table forbidden_words;
drop table sales_plan;
drop table coupon;
drop table payment_methods;