<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 定义日志文件的路径 -->
    <property name="LOG_PATH" value="logs"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 文件输出，支持滚动 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 日志文件的位置 -->
        <file>${LOG_PATH}/app.log</file>

        <!-- 滚动策略：按日期每天生成一个新的日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 定义日志文件的名称模式，使用日期作为后缀 -->
            <fileNamePattern>${LOG_PATH}/app.%d{yyyy-MM-dd}.log</fileNamePattern>

            <!-- 保留的最大历史文件数，例如：保留30天的日志 -->
            <maxHistory>30</maxHistory>

            <!-- 设置日志文件的总大小限制，例如：1GB -->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 设置日志级别为INFO，同时输出到控制台和文件 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>
</configuration>
