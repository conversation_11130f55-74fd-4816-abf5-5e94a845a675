<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kedish.xyhelper_fox.repo.mapper.ChatGptUserMapper">


    <select id="getTotalCount" resultType="java.lang.Integer">
        select count(1) from chatgpt_user
        where deleted_at is null
    </select>
    <select id="getNewCount" resultType="java.lang.Integer">
        select count(1) from chatgpt_user
        where deleted_at is null
        and createTime between #{startTime} and #{endTime}

    </select>
    <select id="selectByGroupId" resultType="com.kedish.xyhelper_fox.repo.model.ChatgptUser">
        SELECT 
            id as id,
            createTime as createTime,
            updateTime as updateTime,
            deleted_at as deletedAt,
            userToken as userToken,
            expireTime as expireTime,
            isPlus as isPlus,
            remark as remark,
            plusExpireTime as plusExpireTime,
            email as email,
            password as password,
            isAdmin as isAdmin,
            `limit` as `limit`,
            per as per,
            user_type as userType,
            enable_claude as enableClaude,
            status as status,
            invite_code as inviteCode,
            invite_by as inviteBy,
            group_id as groupId
        FROM chatgpt_user 
        WHERE group_id = #{groupId}
        AND deleted_at is null
    </select>
    <select id="getTotalCountBefore" resultType="java.lang.Integer">
        select count(1) from chatgpt_user
        where deleted_at is null
        and createTime &lt;= #{endTime}
    </select>
    <select id="getCountByUserType" resultType="java.lang.Integer">
        select count(1) from chatgpt_user
        where deleted_at is null
        and user_type = #{userType}
    </select>
    <select id="getDailyNewUsers" resultType="java.util.Map">
        SELECT 
            DATE(createTime) as date, 
            COUNT(1) as count
        FROM chatgpt_user
        WHERE deleted_at is null
        AND createTime >= #{startTime}
        GROUP BY DATE(createTime)
        ORDER BY date
    </select>
</mapper>
