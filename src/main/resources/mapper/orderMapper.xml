<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kedish.xyhelper_fox.repo.mapper.OrderMapper">



    <select id="queryOrderByTimeRange" resultType="com.kedish.xyhelper_fox.repo.model.Order">
        select 
            id as id,
            amount as amount,
            username as username,
            trade_no as tradeNo,
            out_trade_no as outTradeNo,
            status as status,
            sales_plan as salesPlan,
            coupon_code as couponCode,
            buy_info as buyInfo,
            remarks as remarks,
            created_at as createdAt,
            updated_at as updatedAt
        from orders
        where created_at between #{startTime} and #{endTime}
        order by created_at asc
    </select>

    <select id="queryOrderByCreateTime" resultType="com.kedish.xyhelper_fox.repo.model.Order">
        select 
            id as id,
            amount as amount,
            username as username,
            trade_no as tradeNo,
            out_trade_no as outTradeNo,
            status as status,
            sales_plan as salesPlan,
            coupon_code as couponCode,
            buy_info as buyInfo,
            remarks as remarks,
            created_at as createdAt,
            updated_at as updatedAt
        from orders
        where created_at between #{startTime} and #{endTime}
        order by created_at desc
    </select>

    <select id="queryPaidOrders" resultType="com.kedish.xyhelper_fox.repo.model.Order">
        select 
            id as id,
            amount as amount,
            username as username,
            trade_no as tradeNo,
            out_trade_no as outTradeNo,
            status as status,
            sales_plan as salesPlan,
            coupon_code as couponCode,
            buy_info as buyInfo,
            remarks as remarks,
            created_at as createdAt,
            updated_at as updatedAt
        from orders
        where status = 1
        order by created_at desc
    </select>

    <select id="queryPaidOrdersByTimeRange" resultType="com.kedish.xyhelper_fox.repo.model.Order">
        select 
            id as id,
            amount as amount,
            username as username,
            trade_no as tradeNo,
            out_trade_no as outTradeNo,
            status as status,
            sales_plan as salesPlan,
            coupon_code as couponCode,
            buy_info as buyInfo,
            remarks as remarks,
            created_at as createdAt,
            updated_at as updatedAt
        from orders
        where status = 1
        and created_at between #{startTime} and #{endTime}
        order by created_at desc
    </select>

</mapper>
