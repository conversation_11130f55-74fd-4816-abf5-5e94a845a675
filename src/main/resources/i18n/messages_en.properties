# English language file
# Common messages
common.success=Operation successful
common.fail=Operation failed
common.error=System error
common.param.error=Parameter error
common.unauthorized=Unauthorized access
common.forbidden=Insufficient permissions
common.confirm=Confirm
common.cancel=Cancel
common.save=Save
common.delete=Delete
common.edit=Edit
common.add=Add
common.search=Search
common.reset=Reset
common.submit=Submit
common.loading=Loading...
common.close=Close
common.refresh=Refresh
common.export=Export
common.import=Import
common.upload=Upload
common.download=Download
common.preview=Preview
common.settings=Settings
common.help=Help
common.about=About
common.version=Version
common.language=Language
common.theme=Theme

# User related
user.login.success=Login successful
user.login.fail=Login failed
user.logout.success=Logout successful
user.register.success=Registration successful
user.register.fail=Registration failed
user.password.change.success=Password changed successfully
user.password.change.fail=Password change failed
user.not.found=User not found
user.token.expired=Login expired
user.not.admin=Not an administrator, no permission to operate
user.username=Username
user.password=Password
user.email=Email
user.phone=Phone
user.profile=Profile
user.avatar=Avatar
user.nickname=Nickname
user.invalid.credentials=Invalid username or password
user.account.locked=Account has been locked
user.account.expired=Account has expired
user.permission.denied=Permission denied

# Configuration related
config.get.fail=Failed to get configuration
config.update.success=Configuration updated successfully
config.update.fail=Configuration update failed
config.not.found=Configuration not found
config.invalid.value=Invalid configuration value

# Email related
email.send.success=Email sent successfully
email.send.fail=Email sending failed
email.code.invalid=Invalid verification code
email.code.expired=Verification code expired
email.code.sent=Verification code sent
email.invalid.format=Invalid email format

# File related
file.upload.success=File uploaded successfully
file.upload.fail=File upload failed
file.not.found=File not found
file.size.exceeded=File size exceeds limit
file.type.not.supported=File type not supported
file.download.success=File downloaded successfully
file.download.fail=File download failed

# System related
system.maintenance=System under maintenance
system.busy=System busy, please try again later
system.error=System error
system.timeout=Request timeout
system.service.unavailable=Service unavailable

# Car related
car.not.found=Car not found
car.access.denied=Car access denied
car.limit.exceeded=Car usage limit exceeded
car.status.invalid=Invalid car status

# Drawing related
draw.generation.success=Image generated successfully
draw.generation.fail=Image generation failed
draw.limit.exceeded=Drawing limit exceeded
draw.prompt.invalid=Invalid drawing prompt
draw.style.not.supported=Drawing style not supported

# Activation code related
activation.code.invalid=Invalid activation code
activation.code.used=Activation code already used
activation.code.expired=Activation code expired
activation.code.exchange.success=Activation code exchanged successfully
activation.code.not.found=Activation code not found
activation.code.insufficient.balance=Insufficient balance

# Internationalization related
i18n.language.switch.success=Language switched successfully
i18n.language.switch.fail=Language switch failed
i18n.language.not.supported=Language not supported
i18n.message.not.found=Message not found

# Validation related
validation.required=This field is required
validation.email=Please enter a valid email address
validation.phone=Please enter a valid phone number
validation.password=Password must be at least 6 characters
validation.confirm.password=Password confirmation does not match
validation.min.length=Minimum {0} characters required
validation.max.length=Maximum {0} characters allowed
validation.numeric=Please enter a number
validation.url=Please enter a valid URL
validation.date=Please enter a valid date
validation.time=Please enter a valid time

# Pagination related
pagination.total=Total {0} items
pagination.page=Page {0}
pagination.page.size={0} items per page
pagination.goto=Go to
pagination.prev=Previous
pagination.next=Next
pagination.first=First
pagination.last=Last

# API related
api.rate.limit.exceeded=API rate limit exceeded
api.quota.exceeded=API quota exhausted
api.key.invalid=Invalid API key
api.service.error=API service error
api.timeout=API request timeout

# Payment related
payment.success=Payment successful
payment.fail=Payment failed
payment.pending=Payment processing
payment.cancelled=Payment cancelled
payment.refunded=Refunded
payment.amount.invalid=Invalid payment amount
payment.method.not.supported=Payment method not supported

# Car fleet related
car.no.permission=No permission to use this node
car.plus.expired=Your premium membership has expired, please renew to continue using
car.general.expired=Your regular membership has expired, please renew to continue using

# User registration and login related
user.register.disabled=System registration is disabled
user.email.code.invalid=Invalid email verification code
user.token.exists=UserToken already exists
user.email.exists=Email already exists
user.old.password.invalid=Invalid old password
user.points.insufficient=Insufficient user points

# Coupon related
coupon.discount.zero=Coupon discount cannot be zero for both amount and percentage
coupon.code.exists=Coupon code already exists
coupon.not.found=Coupon not found
coupon.expired=Coupon has expired
coupon.not.available=Coupon is not available
