# \u901a\u7528\u6d88\u606f
common.success=\u64cd\u4f5c\u6210\u529f
common.fail=\u64cd\u4f5c\u5931\u8d25
common.error=\u7cfb\u7edf\u9519\u8bef
common.param.error=\u53c2\u6570\u9519\u8bef
common.unauthorized=\u672a\u767b\u5f55
common.forbidden=\u6743\u9650\u4e0d\u8db3
common.confirm=\u786e\u8ba4
common.cancel=\u53d6\u6d88
common.save=\u4fdd\u5b58
common.delete=\u5220\u9664
common.edit=\u7f16\u8f91
common.add=\u6dfb\u52a0
common.search=\u641c\u7d22
common.reset=\u91cd\u7f6e
common.submit=\u63d0\u4ea4
common.loading=\u52a0\u8f7d\u4e2d...
common.close=\u5173\u95ed
common.refresh=\u5237\u65b0
common.export=\u5bfc\u51fa
common.import=\u5bfc\u5165
common.upload=\u4e0a\u4f20
common.download=\u4e0b\u8f7d
common.preview=\u9884\u89c8
common.settings=\u8bbe\u7f6e
common.help=\u5e2e\u52a9
common.about=\u5173\u4e8e
common.version=\u7248\u672c
common.language=\u8bed\u8a00
common.theme=\u4e3b\u9898

# \u7528\u6237\u76f8\u5173
user.login.success=\u767b\u5f55\u6210\u529f
user.login.fail=\u767b\u5f55\u5931\u8d25
user.logout.success=\u767b\u51fa\u6210\u529f
user.register.success=\u6ce8\u518c\u6210\u529f
user.register.fail=\u6ce8\u518c\u5931\u8d25
user.password.change.success=\u5bc6\u7801\u4fee\u6539\u6210\u529f
user.password.change.fail=\u5bc6\u7801\u4fee\u6539\u5931\u8d25
user.not.found=\u7528\u6237\u4e0d\u5b58\u5728
user.token.expired=\u767b\u5f55\u8fc7\u671f
user.not.admin=\u4e0d\u662f\u7ba1\u7406\u5458\uff0c\u65e0\u6743\u64cd\u4f5c
user.username=\u7528\u6237\u540d
user.password=\u5bc6\u7801
user.email=\u90ae\u7bb1
user.phone=\u624b\u673a\u53f7
user.profile=\u4e2a\u4eba\u4fe1\u606f
user.avatar=\u5934\u50cf
user.nickname=\u6635\u79f0
user.invalid.credentials=\u7528\u6237\u540d\u6216\u5bc6\u7801\u9519\u8bef
user.account.locked=\u8d26\u53f7\u5df2\u88ab\u9501\u5b9a
user.account.expired=\u8d26\u53f7\u5df2\u8fc7\u671f
user.permission.denied=\u6743\u9650\u4e0d\u8db3

# \u914d\u7f6e\u76f8\u5173
config.get.fail=\u83b7\u53d6\u914d\u7f6e\u5931\u8d25
config.update.success=\u914d\u7f6e\u66f4\u65b0\u6210\u529f
config.update.fail=\u914d\u7f6e\u66f4\u65b0\u5931\u8d25
config.not.found=\u914d\u7f6e\u4e0d\u5b58\u5728
config.invalid.value=\u914d\u7f6e\u503c\u65e0\u6548

# \u90ae\u4ef6\u76f8\u5173
email.send.success=\u90ae\u4ef6\u53d1\u9001\u6210\u529f
email.send.fail=\u90ae\u4ef6\u53d1\u9001\u5931\u8d25
email.code.invalid=\u9a8c\u8bc1\u7801\u65e0\u6548
email.code.expired=\u9a8c\u8bc1\u7801\u5df2\u8fc7\u671f
email.code.sent=\u9a8c\u8bc1\u7801\u5df2\u53d1\u9001
email.invalid.format=\u90ae\u7bb1\u683c\u5f0f\u9519\u8bef

# \u6587\u4ef6\u76f8\u5173
file.upload.success=\u6587\u4ef6\u4e0a\u4f20\u6210\u529f
file.upload.fail=\u6587\u4ef6\u4e0a\u4f20\u5931\u8d25
file.not.found=\u6587\u4ef6\u4e0d\u5b58\u5728
file.size.exceeded=\u6587\u4ef6\u5927\u5c0f\u8d85\u8fc7\u9650\u5236
file.type.not.supported=\u4e0d\u652f\u6301\u7684\u6587\u4ef6\u7c7b\u578b
file.download.success=\u6587\u4ef6\u4e0b\u8f7d\u6210\u529f
file.download.fail=\u6587\u4ef6\u4e0b\u8f7d\u5931\u8d25

# \u7cfb\u7edf\u76f8\u5173
system.maintenance=\u7cfb\u7edf\u7ef4\u62a4\u4e2d
system.busy=\u7cfb\u7edf\u7e41\u5fd9\uff0c\u8bf7\u7a0d\u540e\u518d\u8bd5
system.error=\u7cfb\u7edf\u9519\u8bef
system.timeout=\u8bf7\u6c42\u8d85\u65f6
system.service.unavailable=\u670d\u52a1\u4e0d\u53ef\u7528

# \u8f66\u8f86\u76f8\u5173
car.not.found=\u8f66\u8f86\u4e0d\u5b58\u5728
car.access.denied=\u65e0\u6743\u8bbf\u95ee\u8f66\u8f86
car.limit.exceeded=\u8f66\u8f86\u4f7f\u7528\u6b21\u6570\u8d85\u8fc7\u9650\u5236
car.status.invalid=\u8f66\u8f86\u72b6\u6001\u65e0\u6548

# \u7ed8\u56fe\u76f8\u5173
draw.generation.success=\u56fe\u7247\u751f\u6210\u6210\u529f
draw.generation.fail=\u56fe\u7247\u751f\u6210\u5931\u8d25
draw.limit.exceeded=\u7ed8\u56fe\u6b21\u6570\u8d85\u8fc7\u9650\u5236
draw.prompt.invalid=\u7ed8\u56fe\u63d0\u793a\u8bcd\u65e0\u6548
draw.style.not.supported=\u4e0d\u652f\u6301\u7684\u7ed8\u56fe\u98ce\u683c

# \u6fc0\u6d3b\u7801\u76f8\u5173
activation.code.invalid=\u6fc0\u6d3b\u7801\u65e0\u6548
activation.code.used=\u6fc0\u6d3b\u7801\u5df2\u4f7f\u7528
activation.code.expired=\u6fc0\u6d3b\u7801\u5df2\u8fc7\u671f
activation.code.exchange.success=\u6fc0\u6d3b\u7801\u5151\u6362\u6210\u529f
activation.code.not.found=\u6fc0\u6d3b\u7801\u4e0d\u5b58\u5728
activation.code.insufficient.balance=\u4f59\u989d\u4e0d\u8db3

# \u56fd\u9645\u5316\u76f8\u5173
i18n.language.switch.success=\u8bed\u8a00\u5207\u6362\u6210\u529f
i18n.language.switch.fail=\u8bed\u8a00\u5207\u6362\u5931\u8d25
i18n.language.not.supported=\u4e0d\u652f\u6301\u7684\u8bed\u8a00
i18n.message.not.found=\u6d88\u606f\u4e0d\u5b58\u5728

# \u9a8c\u8bc1\u76f8\u5173
validation.required=\u8be5\u5b57\u6bb5\u4e3a\u5fc5\u586b\u9879
validation.email=\u8bf7\u8f93\u5165\u6709\u6548\u7684\u90ae\u7bb1\u5730\u5740
validation.phone=\u8bf7\u8f93\u5165\u6709\u6548\u7684\u624b\u673a\u53f7\u7801
validation.password=\u5bc6\u7801\u81f3\u5c11\u4e3a6\u4f4d
validation.confirm.password=\u4e24\u6b21\u8f93\u5165\u7684\u5bc6\u7801\u4e0d\u4e00\u81f4
validation.min.length=\u6700\u5c11\u9700\u8981{0}\u4e2a\u5b57\u7b26
validation.max.length=\u6700\u591a\u5141\u8bb8{0}\u4e2a\u5b57\u7b26
validation.numeric=\u8bf7\u8f93\u5165\u6570\u5b57
validation.url=\u8bf7\u8f93\u5165\u6709\u6548\u7684URL
validation.date=\u8bf7\u8f93\u5165\u6709\u6548\u7684\u65e5\u671f
validation.time=\u8bf7\u8f93\u5165\u6709\u6548\u7684\u65f6\u95f4

# \u5206\u9875\u76f8\u5173
pagination.total=\u5171 {0} \u6761
pagination.page=\u7b2c {0} \u9875
pagination.page.size=\u6bcf\u9875 {0} \u6761
pagination.goto=\u8df3\u8f6c\u81f3
pagination.prev=\u4e0a\u4e00\u9875
pagination.next=\u4e0b\u4e00\u9875
pagination.first=\u9996\u9875
pagination.last=\u672b\u9875

# API\u76f8\u5173
api.rate.limit.exceeded=API\u8c03\u7528\u9891\u7387\u8d85\u9650
api.quota.exceeded=API\u914d\u989d\u5df2\u7528\u5c3d
api.key.invalid=API\u5bc6\u94a5\u65e0\u6548
api.service.error=API\u670d\u52a1\u9519\u8bef
api.timeout=API\u8bf7\u6c42\u8d85\u65f6

# \u652f\u4ed8\u76f8\u5173
payment.success=\u652f\u4ed8\u6210\u529f
payment.fail=\u652f\u4ed8\u5931\u8d25
payment.pending=\u652f\u4ed8\u5904\u7406\u4e2d
payment.cancelled=\u652f\u4ed8\u5df2\u53d6\u6d88
payment.refunded=\u5df2\u9000\u6b3e
payment.amount.invalid=\u652f\u4ed8\u91d1\u989d\u65e0\u6548
payment.method.not.supported=\u4e0d\u652f\u6301\u7684\u652f\u4ed8\u65b9\u5f0f

# \u8f66\u961f\u76f8\u5173
car.no.permission=\u6ca1\u6709\u8be5\u8282\u70b9\u7684\u4f7f\u7528\u6743\u9650
car.plus.expired=\u60a8\u7684\u9ad8\u7ea7\u4f1a\u5458\u5df2\u8fc7\u671f\uff0c\u8bf7\u7eed\u8d39\u540e\u518d\u4f7f\u7528
car.general.expired=\u60a8\u7684\u666e\u901a\u4f1a\u5458\u5df2\u8fc7\u671f\uff0c\u8bf7\u7eed\u8d39\u540e\u518d\u4f7f\u7528

# \u7528\u6237\u6ce8\u518c\u767b\u5f55\u76f8\u5173
user.register.disabled=\u7cfb\u7edf\u4e0d\u5141\u8bb8\u6ce8\u518c
user.email.code.invalid=\u90ae\u7bb1\u9a8c\u8bc1\u7801\u9519\u8bef
user.token.exists=userToken\u5df2\u5b58\u5728
user.email.exists=\u90ae\u7bb1\u5df2\u5b58\u5728
user.old.password.invalid=\u65e7\u5bc6\u7801\u9519\u8bef
user.points.insufficient=\u7528\u6237\u79ef\u5206\u4e0d\u8db3

# \u4f18\u60e0\u5238\u76f8\u5173
coupon.discount.zero=\u4f18\u60e0\u5238\u6298\u6263\u4e0d\u80fd\u540c\u65f6\u4e3a0
coupon.code.exists=\u4f18\u60e0\u5238\u7f16\u7801\u5df2\u5b58\u5728
coupon.not.found=\u4f18\u60e0\u5238\u4e0d\u5b58\u5728
coupon.expired=\u4f18\u60e0\u5238\u5df2\u8fc7\u671f
coupon.not.available=\u4f18\u60e0\u5238\u4e0d\u53ef\u7528
