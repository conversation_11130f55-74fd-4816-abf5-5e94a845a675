package com.kedish.xyhelper_fox.pay.epay;

import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Service
public class EpayClient {

    @Resource
    private RestTemplate restTemplate;


    private static LinkedMultiValueMap<String, Object> treeMap2LinkedMultiValueMap(Map<String, Object> map) {
        LinkedMultiValueMap<String, Object> linkedMultiValueMap = new LinkedMultiValueMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            linkedMultiValueMap.add(entry.getKey(), entry.getValue());
        }
        return linkedMultiValueMap;
    }

    // 支付接口
    public PayResponse pay(Map<String, Object> params, String host) {
        String url = host + "/mapi.php"; // 替换为实际 URL
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<LinkedMultiValueMap<String, Object>> entity = new HttpEntity<>(treeMap2LinkedMultiValueMap(params), headers);
//        ResponseEntity<PayResponse> response = restTemplate.exchange(url, HttpMethod.POST, entity, PayResponse.class);

        ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        if (exchange.getStatusCode().equals(HttpStatus.OK)) {
            String body = exchange.getBody();
            PayResponse payResponse = JSON.parseObject(body, PayResponse.class);
            return payResponse;
        }
        return null;
    }

    // 查询订单接口
    public OrderResponse queryOrder(Map<String, Object> params, String host) {
        String url = host + "/api.php"; // 替换为实际 URL
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept", "text/html;charset=utf-8");

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(params, headers);

        ResponseEntity<OrderResponse> response = restTemplate.exchange(url, HttpMethod.POST, entity, OrderResponse.class);

        return response.getBody();
    }

    // 查询批量订单接口
    public BatchOrderResponse queryBatchOrder(Map<String, Object> params, String host) {
        String url = host + "/api.php"; // 替换为实际 URL
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept", "text/html;charset=utf-8");

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(params, headers);

        ResponseEntity<BatchOrderResponse> response = restTemplate.exchange(url, HttpMethod.POST, entity, BatchOrderResponse.class);

        return response.getBody();
    }
}
