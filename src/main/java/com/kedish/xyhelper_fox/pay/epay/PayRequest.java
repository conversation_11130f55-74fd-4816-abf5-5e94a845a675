package com.kedish.xyhelper_fox.pay.epay;

import lombok.Data;

import java.util.TreeMap;

@Data
public class PayRequest {
    /**
     * 支付方式,枚举PayTypeEnum；alipay:支付宝,tenpay:财付通,qqpay:QQ钱包,wxpay:微信支付
     */
    private String type;

    /**
     * 交易单号
     */
    private String outTradeNo;

    private String method;

    private String clientip;


    /**
     * 异步通知地址
     */
    private String notifyUrl;

    /**
     * 跳转通知地址
     */
    private String returnUrl;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 金额
     */
    private String money;

    /**
     * 来源网站名称
     */
    private String sitename;

    public TreeMap<String, Object> toMap() {
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("type", type);
        map.put("out_trade_no", outTradeNo);
        map.put("notify_url", notifyUrl);
        map.put("return_url", returnUrl);
        map.put("name", name);
        map.put("money", money);
        map.put("sitename", sitename);
        map.put("clientip", clientip);
        return map;
    }
}
