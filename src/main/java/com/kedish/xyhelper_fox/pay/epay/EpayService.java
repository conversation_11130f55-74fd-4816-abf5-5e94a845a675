package com.kedish.xyhelper_fox.pay.epay;

import com.kedish.xyhelper_fox.repo.model.PaymentMethod;
import jakarta.annotation.Resource;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.stream.Collectors;


/**
 * epay相关service
 *
 * <AUTHOR>
 * @Date 2023/10/20 16:22
 */
@Service
public class EpayService {


    @Resource
    private EpayClient epayClient;


//    public static void main(String[] args) {
//        EpayService epayService = new EpayService("http://localhost:8080/", 1000, "4H8248NMv543jNc3H7V6hmJOZKHHVMA3");
//        PayRequest payRequest = new PayRequest();
//        payRequest.setMoney("1");
//        payRequest.setName("测试商品1");
//        payRequest.setType("wxpay");
//        payRequest.setOutTradeNo("admin" + System.currentTimeMillis());
//        payRequest.setSitename("云龙新软");
//        payRequest.setNotifyUrl("https://www.usergpt.top");
//        payRequest.setReturnUrl("https://www.usergpt.top");
//        payRequest.setMethod("web");
//        payRequest.setClientip("*************");
//
//        epayService.payUrl(payRequest);
//    }

    /**
     * 发起交易请求
     *
     * @param payRequest 请求参数
     * <AUTHOR>
     * @date 2023/10/20 16:25
     **/
    public PayResponse pay(PayRequest payRequest, PaymentMethod paymentMethod) {
        TreeMap<String, Object> map = payRequest.toMap();
        map.put("pid", paymentMethod.getAppid());
        toSign(map, paymentMethod);
        return epayClient.pay(map, paymentMethod.getPaymentUrl());
    }


    /**
     * 异步通知回调
     *
     * @param map 请求参数
     * @return 跳转的utl地址
     * <AUTHOR>
     * @date 2023/10/20 16:25
     **/
//    public PayNotifyResponse asyncNotify(Map<String, Object> map) {
//        if (!checkSign(map)) {
//            throw new PayException("验签错误");
//        }
//        PayNotifyResponse payNotifyResponse = new PayNotifyResponse();
//        payNotifyResponse.setPid(Optional.ofNullable(map.get("pid")).map(Object::toString).map(Integer::parseInt).orElse(null))
//                .setTradeNo(Optional.ofNullable(map.get("trade_no")).map(Object::toString).orElse(null))
//                .setOutTradeNo(Optional.ofNullable(map.get("out_trade_no")).map(Object::toString).orElse(null))
//                .setType(Optional.ofNullable(map.get("type")).map(Object::toString).orElse(null))
//                .setName(Optional.ofNullable(map.get("name")).map(Object::toString).orElse(null))
//                .setMoney(Optional.ofNullable(map.get("money")).map(Object::toString).orElse(null))
//                .setTradeStatus(Optional.ofNullable(map.get("trade_status")).map(Object::toString).orElse(null));
//        return payNotifyResponse;
//    }

    /**
     * 查询订单
     *
     * @param outTradeNo 商户订单号
     * <AUTHOR>
     * @date 2023/10/20 16:25
     **/
    public OrderResponse queryOrder(String outTradeNo, PaymentMethod paymentMethod) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("act", "order");
        map.put("pid", paymentMethod.getAppid());
        map.put("key", paymentMethod.getAppkey());
        map.put("out_trade_no", outTradeNo);
        return epayClient.queryOrder(map, paymentMethod.getPaymentUrl());
    }

    /**
     * 批量查询订单
     *
     * @param limit 返回的订单数量，最大50
     * <AUTHOR>
     * @date 2023/10/20 16:25
     **/
    public BatchOrderResponse queryBatchOrder(String limit, PaymentMethod paymentMethod) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("act", "orders");
        map.put("pid", paymentMethod.getAppid());
        map.put("key", paymentMethod.getAppkey());
        map.put("limit", limit);
        return epayClient.queryBatchOrder(map, paymentMethod.getPaymentUrl());
    }

    /**
     * 数据加签
     *
     * @param map 数据集合
     * <AUTHOR>
     * @date 2023/10/21 18:24
     **/
    public void toSign(Map<String, Object> map, PaymentMethod paymentMethod) {
        boolean isTreeMap = map instanceof TreeMap;
        if (!isTreeMap) {
            map = new TreeMap<>(map);
        }
        String paramString = map.entrySet()
                .stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        paramString = paramString + paymentMethod.getAppkey();
        // 计算MD5哈希值
        String sign = DigestUtils.md5Hex(paramString);
        map.put("sign", sign);
        map.put("sign_type", "MD5");
    }

    /**
     * 数据签名验证
     *
     * @param map 数据集合
     * <AUTHOR>
     * @date 2023/10/21 18:24
     **/
    public boolean checkSign(Map<String, Object> map, PaymentMethod paymentMethod) {
        boolean isTreeMap = map instanceof TreeMap;
        if (!isTreeMap) {
            map = new TreeMap<>(map);
        }
        map.remove("sign_type");
        Object checkSign = map.remove("sign");

        String paramString = map.entrySet()
                .stream()
                .filter(entry -> entry.getValue() != null && entry.getValue() != "")
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        paramString = paramString + paymentMethod.getAppkey();
        // 计算MD5哈希值
        String sign = DigestUtils.md5Hex(paramString);
        return Objects.equals(checkSign, sign);
    }

}
