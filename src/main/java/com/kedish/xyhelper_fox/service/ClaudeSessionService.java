package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.model.req.AddClaudeSessionReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.repo.mapper.ClaudeSessionMapper;
import com.kedish.xyhelper_fox.repo.model.ClaudeSession;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class ClaudeSessionService {

    @Resource
    private ClaudeSessionMapper claudeSessionMapper;

    public void saveOrUpdate(AddClaudeSessionReq req) {


        if (req.getId() != null) {
            ClaudeSession claudeSession = claudeSessionMapper.selectById(req.getId());

            claudeSession.setCarID(req.getCarID());
            claudeSession.setEmail(req.getEmail());
            claudeSession.setAccountType(req.getAccountType());
            claudeSession.setEnabled(req.getEnabled());
            claudeSession.setSession(req.getSession());
            claudeSession.setRemarks(req.getRemarks());
            claudeSession.setUpdatedAt(LocalDateTime.now());
            claudeSessionMapper.updateById(claudeSession);
        } else {
            ClaudeSession claudeSession = new ClaudeSession();
            claudeSession.setCarID(req.getCarID());
            claudeSession.setEmail(req.getEmail());
            claudeSession.setAccountType(req.getAccountType());
            claudeSession.setEnabled(req.getEnabled());
            claudeSession.setSession(req.getSession());
            claudeSession.setRemarks(req.getRemarks());
            claudeSession.setCreatedAt(LocalDateTime.now());
            claudeSession.setUpdatedAt(LocalDateTime.now());
            claudeSessionMapper.insert(claudeSession);
        }
    }

    public Page<ClaudeSession> page(PageQueryReq req) {
        Page<ClaudeSession> page = new Page<>(req.getPageNum(), req.getPageSize());
        if (req.getSortField() != null && req.getSortOrder() != null) {
            page.setOrders(List.of(req.getSortOrder().equals("asc") ?
                    OrderItem.asc(req.getSortField()) :
                    OrderItem.desc(req.getSortField())));
        }
        QueryWrapper<ClaudeSession> queryWrapper = new QueryWrapper<>();

        if (StringUtils.hasLength(req.getQuery()))
            queryWrapper.like("remark", req.getQuery())
                    .or()
                    .like("email", req.getQuery())
                    .or()
                    .like("session", req.getQuery());
        return claudeSessionMapper.selectPage(page, queryWrapper);
    }

    public void delete(List<Long> id) {
        claudeSessionMapper.deleteByIds(id);
    }

    public ClaudeSession getByCarID(String chooseCarId) {

        QueryWrapper<ClaudeSession> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("carid", chooseCarId);
        return claudeSessionMapper.selectOne(queryWrapper);
    }
}
