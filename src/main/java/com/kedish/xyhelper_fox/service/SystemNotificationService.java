package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.model.req.AddNotificationReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.repo.mapper.SystemNotificationMapper;
import com.kedish.xyhelper_fox.repo.model.SystemNotification;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Service
public class SystemNotificationService {

    @Resource
    private SystemNotificationMapper systemNotificationMapper;


    public Page<SystemNotification> getPage(PageQueryReq req) {
        Page<SystemNotification> page = new Page<>(req.getPageNum(), req.getPageSize());
        return systemNotificationMapper.selectPage(page, null);
    }

    public void addOrUpdate(AddNotificationReq req) {
        if (req.getId() != null) {
            SystemNotification systemNotification = systemNotificationMapper.selectById(req.getId());
            systemNotification.setContent(req.getContent());
            systemNotification.setType(req.getType());
            systemNotification.setStatus(req.getStatus());
            systemNotification.setContentType(req.getContentType());
            systemNotification.setUpdatedAt(LocalDateTime.now());
            systemNotificationMapper.updateById(systemNotification);
        } else {
            SystemNotification systemNotification = new SystemNotification();
            systemNotification.setContent(req.getContent());
            systemNotification.setType(req.getType());
            systemNotification.setStatus(req.getStatus());
            systemNotification.setContentType(req.getContentType());
            systemNotification.setCreatedAt(LocalDateTime.now());
            systemNotification.setUpdatedAt(LocalDateTime.now());
            systemNotificationMapper.insert(systemNotification);
        }

    }

    public void delete(Long id) {
        systemNotificationMapper.deleteById(id);
    }

    public List<SystemNotification> getLatestNotification(String typeList) {

        QueryWrapper<SystemNotification> queryWrapper = new QueryWrapper<>();
        if (StringUtils.hasLength(typeList)) {
            queryWrapper.in("type", Arrays.stream(typeList.split(","))
                    .map(Integer::parseInt).toList());
        }
        queryWrapper.eq("status", 1);
        return systemNotificationMapper.selectList(queryWrapper);
    }
}
