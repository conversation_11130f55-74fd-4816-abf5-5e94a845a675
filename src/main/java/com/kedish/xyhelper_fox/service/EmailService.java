package com.kedish.xyhelper_fox.service;

import com.alibaba.fastjson2.util.DateUtils;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.repo.model.Order;
import com.kedish.xyhelper_fox.utils.EmailUtils;
import jakarta.annotation.Resource;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import static com.kedish.xyhelper_fox.constant.Constant.REDIS_EMAIL_CODE_PREFIX;

@Service
public class EmailService {

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private RedissonClient redissonClient;

    public static String generateRandomNumber(int length) {
        String digits = "0123456789";
        StringBuilder result = new StringBuilder();
        Random random = new Random();

        for (int i = 0; i < length; i++) {
            int index = random.nextInt(digits.length());
            result.append(digits.charAt(index));
        }

        return result.toString();
    }

    public static String generateRandomPassword(int length) {
        String characters = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        StringBuilder result = new StringBuilder();
        Random random = new Random();

        for (int i = 0; i < length; i++) {
            int index = random.nextInt(characters.length());
            result.append(characters.charAt(index));
        }

        return result.toString();
    }


    public void testSendEmail() {
        Map<String, String> configMap = chatGptConfigService.getConfigMap(Arrays.asList("systemName", "smtpHost", "smtpPort", "senderEmail", "emailPassword"));

        if (!StringUtils.hasLength(configMap.get("senderEmail"))) {
            throw new FoxException("请先配置邮箱信息");
        }
        if (!StringUtils.hasLength(configMap.get("smtpHost")) || !StringUtils.hasLength(configMap.get("smtpHost")) || !StringUtils.hasLength(configMap.get("emailPassword"))) {
            throw new FoxException("请先配置邮箱信息");
        }

        String companyName = configMap.get("systemName");
        EmailUtils.sendEmail(configMap.get("senderEmail"), "测试发送邮件功能",
                TEST_HTML,
                "text/html; charset=utf-8",
                EmailUtils.EmailConfig
                        .builder()
                        .from(configMap.get("systemName"))
                        .host(configMap.get("smtpHost"))
                        .port(configMap.get("smtpPort"))
                        .username(configMap.get("senderEmail"))
                        .password(configMap.get("emailPassword"))
                        .build());
    }

    public void sendForgetEmail(String email) {
        Map<String, String> configMap = chatGptConfigService.getConfigMap(Arrays.asList("systemName", "smtpHost",
                "emailWhiteList", "smtpPort", "senderEmail", "emailPassword"));

        if (!StringUtils.hasLength(configMap.get("senderEmail"))) {
            throw new FoxException("请先配置邮箱信息");
        }
        if (!StringUtils.hasLength(configMap.get("smtpHost")) || !StringUtils.hasLength(configMap.get("smtpHost")) || !StringUtils.hasLength(configMap.get("emailPassword"))) {
            throw new FoxException("请先配置邮箱信息");
        }
        String emailWhiteList = configMap.get("emailWhiteList");
        checkWhiteList(email, emailWhiteList);

        String password = generateRandomPassword(6);
        String companyName = configMap.get("systemName");
        EmailUtils.sendEmail(email, "重置密码",
                String.format(FORGET_PASSWORD_HTML, companyName, password),
                "text/html; charset=utf-8",
                EmailUtils.EmailConfig
                        .builder()
                        .from(configMap.get("systemName"))
                        .host(configMap.get("smtpHost"))
                        .port(configMap.get("smtpPort"))
                        .username(configMap.get("senderEmail"))
                        .password(configMap.get("emailPassword"))
                        .build());
    }

    public void checkWhiteList(String email, String emailWhiteList) {
        boolean inWhiteList = false;
        if (StringUtils.hasLength(emailWhiteList)) {
            String[] emailsSuffixes = emailWhiteList.split(",");
            for (String emailsSuffix : emailsSuffixes) {
                if (email.endsWith(emailsSuffix)) {
                    inWhiteList = true;
                    break;
                }
            }
        } else {
            inWhiteList = true;
        }
        if (!inWhiteList) {
            throw new FoxException("邮箱不在白名单内，无法发送");
        }
    }

    public void sendPurchase(Order order) {
        Map<String, String> configMap = chatGptConfigService.getConfigMap(Arrays.asList("systemName", "smtpHost",
                "emailWhiteList", "smtpPort", "senderEmail", "emailPassword"));
        String senderEmail = configMap.get("senderEmail");

        if (senderEmail != null) {

            EmailUtils.sendEmail(senderEmail, "支付通知",
                    String.format(PURCHASE_HTML, order.getSalesPlan(), order.getAmount(), order.getUsername(),
                            DateUtils.format(order.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"), order.getCouponCode()),

                    "text/html; charset=utf-8",
                    EmailUtils.EmailConfig
                            .builder()
                            .from(configMap.get("systemName"))
                            .host(configMap.get("smtpHost"))
                            .port(configMap.get("smtpPort"))
                            .username(senderEmail)
                            .password(configMap.get("emailPassword"))
                            .build()
            );
        }
    }

    public void sendCode(String email, String type) {
        Map<String, String> configMap = chatGptConfigService.getConfigMap(Arrays.asList("systemName", "smtpHost",
                "emailWhiteList", "smtpPort", "senderEmail", "emailPassword"));

        if (!StringUtils.hasLength(configMap.get("senderEmail"))) {
            throw new FoxException("请先配置邮箱信息");
        }
        if (!StringUtils.hasLength(configMap.get("smtpHost")) || !StringUtils.hasLength(configMap.get("smtpHost")) || !StringUtils.hasLength(configMap.get("emailPassword"))) {
            throw new FoxException("请先配置邮箱信息");
        }
        String emailWhiteList = configMap.get("emailWhiteList");
        checkWhiteList(email, emailWhiteList);

        String companyName = configMap.get("systemName");
        String code = generateRandomNumber(6);
        RBucket<String> bucket = redissonClient.getBucket(REDIS_EMAIL_CODE_PREFIX + email);
        bucket.set(code, 600L, TimeUnit.SECONDS);
        String content = "";
        if (type.equals("注册验证码")) {
            content = String.format(codeHtml, companyName, code);
        } else if (type.equals("忘记密码")) {
            content = String.format(FORGET_PASSWORD_HTML, companyName, code);
        }

        EmailUtils.sendEmail(email, type,
                content,
                "text/html; charset=utf-8",
                EmailUtils.EmailConfig
                        .builder()
                        .from(configMap.get("systemName"))
                        .host(configMap.get("smtpHost"))
                        .port(configMap.get("smtpPort"))
                        .username(configMap.get("senderEmail"))
                        .password(configMap.get("emailPassword"))
                        .build());
    }

    static String PURCHASE_HTML = """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Payment Notification</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        background-color: #f4f4f4;
                        margin: 0;
                        padding: 20px;
                    }
                    .email-container {
                        max-width: 600px;
                        margin: 0 auto;
                        background-color: #ffffff;
                        border: 1px solid #dddddd;
                        padding: 20px;
                        border-radius: 8px;
                    }
                    h1 {
                        color: #333333;
                    }
                    p {
                        font-size: 16px;
                        color: #555555;
                        margin: 8px 0;
                    }
                    .details {
                        background-color: #f8f9fa;
                        padding: 15px;
                        border-radius: 6px;
                        margin: 15px 0;
                    }
                    .amount {
                        font-size: 24px;
                        font-weight: bold;
                        color: #2a9d8f;
                        margin: 10px 0;
                    }
                    .label {
                        font-weight: bold;
                        color: #666666;
                        min-width: 120px;
                        display: inline-block;
                    }
                    .footer {
                        font-size: 12px;
                        color: #aaaaaa;
                        text-align: center;
                        margin-top: 20px;
                    }
                    .divider {
                        border-bottom: 1px solid #eeeeee;
                        margin: 10px 0;
                    }
                </style>
            </head>
            <body>
                <div class="email-container">
                    <h1>新订单支付通知</h1>
                    <p>系统收到了一笔新的支付订单，详细信息如下：</p>
                   \s
                    <div class="details">
                        <p><span class="label">商品名称：</span>%s</p>
                        <div class="divider"></div>
                       \s
                        <p><span class="label">支付金额：</span></p>
                        <div class="amount">￥%s</div>
                        <div class="divider"></div>
                       \s
                        <p><span class="label">购买人：</span>%s</p>
                        <div class="divider"></div>
                       \s
                        <p><span class="label">购买时间：</span>%s</p>
                        <div class="divider"></div>
                       \s
                        <p><span class="label">使用折扣券：</span>%s</p>
                    </div>
                   \s
                    <p>请及时处理此订单。</p>
                   \s
                    <div class="footer">
                        此邮件为系统自动发送，请勿直接回复。
                    </div>
                </div>
            </body>
            </html>
            """;

    static String codeHtml = """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Verification Code</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        background-color: #f4f4f4;
                        margin: 0;
                        padding: 20px;
                    }
                    .email-container {
                        max-width: 600px;
                        margin: 0 auto;
                        background-color: #ffffff;
                        border: 1px solid #dddddd;
                        padding: 20px;
                        border-radius: 8px;
                    }
                    h1 {
                        color: #333333;
                    }
                    p {
                        font-size: 16px;
                        color: #555555;
                    }
                    .code {
                        font-size: 24px;
                        font-weight: bold;
                        color: #2a9d8f;
                        margin: 20px 0;
                    }
                    .footer {
                        font-size: 12px;
                        color: #aaaaaa;
                        text-align: center;
                        margin-top: 20px;
                    }
                </style>
            </head>
            <body>
                <div class="email-container">
                    <h1>%s</h1>
                    <p>欢迎注册我们的网站，请输入验证码以完成注册流程</p>
                    <div class="code">%s</div>
                    <p><strong>注意，验证码的有效期为10分钟</strong></p>
                    <p>如果您没有尝试注册，可以忽略本邮件</p>
                        
                </div>
            </body>
            </html>
                        
            """;
    static String FORGET_PASSWORD_HTML = """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Verification Code</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        background-color: #f4f4f4;
                        margin: 0;
                        padding: 20px;
                    }
                    .email-container {
                        max-width: 600px;
                        margin: 0 auto;
                        background-color: #ffffff;
                        border: 1px solid #dddddd;
                        padding: 20px;
                        border-radius: 8px;
                    }
                    h1 {
                        color: #333333;
                    }
                    p {
                        font-size: 16px;
                        color: #555555;
                    }
                    .code {
                        font-size: 24px;
                        font-weight: bold;
                        color: #2a9d8f;
                        margin: 20px 0;
                    }
                    .footer {
                        font-size: 12px;
                        color: #aaaaaa;
                        text-align: center;
                        margin-top: 20px;
                    }
                </style>
            </head>
            <body>
                <div class="email-container">
                    <h1>%s</h1>
                    <p>您正在重置密码，请输入验证码以完成重置流程</p>
                    <div class="code">%s</div>
                    <p><strong>注意，验证码的有效期为10分钟</strong></p>
                    <p>如果您没有尝试此功能，可以忽略本邮件</p>
                        
                </div>
            </body>
            </html>
                        
            """;
    static String TEST_HTML = """
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>测试邮件</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        background-color: #f4f4f4;
                        margin: 0;
                        padding: 0;
                    }
                    .email-container {
                        max-width: 600px;
                        margin: 40px auto;
                        background-color: #ffffff;
                        border-radius: 8px;
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                        overflow: hidden;
                    }
                    .header {
                        background-color: #0073e6;
                        color: #ffffff;
                        padding: 20px;
                        text-align: center;
                        font-size: 24px;
                    }
                    .content {
                        padding: 20px;
                        color: #333333;
                        line-height: 1.6;
                    }
                    .content p {
                        margin-bottom: 15px;
                    }
                    .button {
                        text-align: center;
                        margin-top: 20px;
                    }
                    .button a {
                        text-decoration: none;
                        background-color: #0073e6;
                        color: #ffffff;
                        padding: 10px 20px;
                        border-radius: 5px;
                    }
                    .footer {
                        background-color: #f4f4f4;
                        padding: 10px;
                        text-align: center;
                        font-size: 12px;
                        color: #777777;
                    }
                </style>
            </head>
            <body>
                <div class="email-container">
                    <div class="header">
                        这是一个测试邮件
                    </div>
                    <div class="content">
                        <p>你好，</p>
                        <p>这是一封用于测试的邮件。通过这封邮件，我们可以验证邮件功能是否正常运作。</p>
                        <p>请检查以下信息是否正确，并确保邮件的内容显示如预期。</p>
                        <div class="button">
                            <a href="#">点击这里了解更多</a>
                        </div>
                    </div>
                    <div class="footer">
                        &copy; 2024 测试公司 - 这封邮件仅用于测试目的。
                    </div>
                </div>
            </body>
            </html>
                        
            """;

}
