package com.kedish.xyhelper_fox.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kedish.xyhelper_fox.oauth2.GoogleTokenResponse;
import com.kedish.xyhelper_fox.oauth2.GoogleUser;
import com.kedish.xyhelper_fox.repo.model.Oauth2Client;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

@Service
public class GoogleService {

    private final String tokenUrl = "https://oauth2.googleapis.com/token";
    private final String userInfoUrl = "https://www.googleapis.com/oauth2/v2/userinfo";

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    // 使用授权码获取访问令牌
    public String getAccessToken(String code, Oauth2Client oauth2Client) throws IOException {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("client_id", oauth2Client.getClientId());
        map.add("client_secret", oauth2Client.getClientSecret());
        map.add("code", code);
        map.add("redirect_uri", oauth2Client.getRedirectUri());
        map.add("grant_type", "authorization_code");

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
        String response = restTemplate.postForObject(tokenUrl, request, String.class);

        GoogleTokenResponse tokenResponse = objectMapper.readValue(response, GoogleTokenResponse.class);
        return tokenResponse.getAccessToken();
    }

    // 使用访问令牌获取用户信息
    public GoogleUser getUserInfo(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + accessToken);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        return restTemplate.exchange(userInfoUrl, org.springframework.http.HttpMethod.GET, entity, GoogleUser.class).getBody();
    }
}
