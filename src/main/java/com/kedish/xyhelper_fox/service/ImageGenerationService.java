package com.kedish.xyhelper_fox.service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.model.req.ImageEditRequest;
import com.kedish.xyhelper_fox.model.req.ImageGenerationRequest;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.ImageGenerationResponse;
import com.kedish.xyhelper_fox.repo.mapper.ImageGenerationRecordMapper;
import com.kedish.xyhelper_fox.repo.mapper.UserPointsRecordMapper;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.repo.model.ImageGenerationRecord;
import com.kedish.xyhelper_fox.repo.model.ImageStorage;
import com.kedish.xyhelper_fox.repo.model.UserPointsRecord;
import com.kedish.xyhelper_fox.security.UserContext;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 图像生成服务类
 */
@Service
@Slf4j
public class ImageGenerationService {


    private final RestTemplate restTemplate;
    private final ImageGenerationRecordMapper recordMapper;
    private final ImageStorageService imageStorageService;
    private final LocalCache localCache;
    private final ChatgptUserService chatgptUserService;
    private final FileStorageService fileStorageService;


    public ImageGenerationService(RestTemplate restTemplate,
                                  ImageGenerationRecordMapper recordMapper,
                                  ImageStorageService imageStorageService,
                                  LocalCache localCache,
                                  ChatgptUserService chatgptUserService,
                                  FileStorageService fileStorageService) {
        this.restTemplate = restTemplate;
        this.recordMapper = recordMapper;
        this.imageStorageService = imageStorageService;
        this.localCache = localCache;
        this.chatgptUserService = chatgptUserService;
        this.fileStorageService = fileStorageService;
    }

    public ImageGenerationResponse mockGenerateImage(ImageGenerationRequest request) {
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        ImageGenerationResponse response = new ImageGenerationResponse();
        response.setCreated(System.currentTimeMillis() / 1000);
        response.setData(new ArrayList<>());

        ImageGenerationResponse.ImageData imageData = new ImageGenerationResponse.ImageData();
        imageData.setUrl("http://localhost:6956/api/download-image/11");
        response.getData().add(imageData);
        return response;
    }

    public ImageGenerationResponse mockEditImage(ImageEditRequest request) {
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        ImageGenerationResponse response = new ImageGenerationResponse();
        response.setCreated(System.currentTimeMillis() / 1000);
        response.setData(new ArrayList<>());

        ImageStorage imageStorage = imageStorageService
                .selectById(11L);
        ImageGenerationResponse.ImageData imageData = new ImageGenerationResponse.ImageData();
        imageData.setUrl("http://localhost:6956/api/download-image/11");
        response.getData().add(imageData);
        return response;
    }

    /**
     * 定期删除过期的图像生成记录和相关文件
     * 每天凌晨3点执行
     */
    @Scheduled(cron = "0 0 3 * * ?")
    @Transactional
    public void cleanupOldImageRecords() {
        log.info("开始清理过期的图像生成记录...");

        // 默认5天，可以从配置中读取
        int retentionDays = 0;
        Map<String, String> configMap = localCache.getConfigMap();
        String configRetentionDays = configMap.get("imageRetentionDays");
        if (StringUtils.hasLength(configRetentionDays)) {
            try {
                retentionDays = Integer.parseInt(configRetentionDays);
            } catch (NumberFormatException e) {
                log.warn("配置的imageRetentionDays不是有效数字，使用默认值: {}", retentionDays);
                return;
            }
        }
        if (retentionDays <= 0) {
            return;
        }

        log.info("清理{}\u5929前的图像生成记录", retentionDays);

        // 查询过期的记录
        List<ImageGenerationRecord> oldRecords = recordMapper.findRecordsOlderThanDays(retentionDays);
        log.info("找到{}\u6761过期记录", oldRecords.size());

        int deletedRecords = 0;
        int deletedFiles = 0;

        for (ImageGenerationRecord record : oldRecords) {
            try {
                // 查找该记录关联的所有图片存储记录
                QueryWrapper<ImageStorage> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("record_id", record.getId());
                queryWrapper.eq("is_deleted", 0); // 只查询未删除的
                List<ImageStorage> imageStorages = imageStorageService.list(queryWrapper);

                // 删除相关的文件和目录
                for (ImageStorage storage : imageStorages) {
                    if (storage.getImageUrl() != null) {
                        // 删除文件
                        boolean deleted = fileStorageService.deleteFile(storage.getImageUrl());
                        if (deleted) {
                            deletedFiles++;
                        }
                    }

                    // 逻辑删除存储记录
                    imageStorageService.removeById(storage.getId());
                }

                // 如果有recordId，尝试删除该记录的目录
                if (record.getId() != null) {
                    // 根据文件命名规则，尝试删除记录目录
                    // 文件路径格式为：年月/日/recordId/
                    // 我们只需要删除recordId目录
                    LocalDateTime createdAt = record.getCreatedAt();
                    if (createdAt != null) {
                        String datePath = createdAt.format(DateTimeFormatter.ofPattern("yyyyMM/dd"));
                        String recordPath = datePath + "/" + record.getId();
                        fileStorageService.deleteDirectory(recordPath);
                    }
                }

                // 逻辑删除记录
                recordMapper.deleteById(record.getId());
                deletedRecords++;

            } catch (Exception e) {
                log.error("删除记录时出错，ID: {}", record.getId(), e);
            }
        }

        log.info("清理完成，删除了{}\u6761记录，{}\u4e2a文件", deletedRecords, deletedFiles);
    }

    /**
     * 生成图像
     */
//    @Transactional
    public ImageGenerationResponse generateImage(ImageGenerationRequest request) {
        String apiKey = localCache.getConfigMap().get("draw_api_token");
        String apiUrl = localCache.getConfigMap().get("draw_api_base_url");
        String draw_model = localCache.getConfigMap().getOrDefault("draw_model", "gpt-image-1");
        if (StringUtils.isEmpty(apiKey) || StringUtils.isEmpty(apiUrl)) {
            throw new FoxException("请先配置draw_api_token和draw_api_base_url");
        }
        Integer drawPoint = Integer.valueOf(localCache.getConfigMap().getOrDefault("draw_consume_points", "10"));

        ChatgptUser user = UserContext.getUser();
        request.setModel(draw_model);
        ImageGenerationRecord record = ImageGenerationRecord.builder()
                .operationType(ImageGenerationRecord.OperationType.GENERATE)
                .prompt(request.getPrompt())
                .model(request.getModel())
                .requestedCount(request.getN())
                .size(request.getSize())
                .userToken(user.getUserToken())
                .status(1)
                .point(drawPoint)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        //消耗积分
        chatgptUserService.addUserLimit(user.getUserToken(), -drawPoint, UserPointsRecord.SourceType.IMAGE_GEN_CONSUME);
        // 保存记录
        record.setSuccessful(true);
        recordMapper.insert(record);

        boolean generateFail = false;
        String errorMessage = null;
        try {

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);

            HttpEntity<ImageGenerationRequest> entity = new HttpEntity<>(request, headers);

            String url = apiUrl + "/v1/images/generations";
            ImageGenerationResponse response = restTemplate.postForObject(url, entity, ImageGenerationResponse.class);


            // 保存生成的图片
            List<Long> generatedImageIds = new ArrayList<>();
            for (ImageGenerationResponse.ImageData imageData : response.getData()) {
                ImageStorage imageStorage;
                if (imageData.getB64_json() != null && !imageData.getB64_json().isEmpty()) {
                    // 如果有base64数据，使用它
                    imageStorage = imageStorageService.saveGeneratedImage(
                            record.getId(),
                            imageData.getB64_json()
                    );
                } else if (imageData.getUrl() != null && !imageData.getUrl().isEmpty()) {
                    // 如果没有base64数据但有URL，从URL下载图片
                    imageStorage = imageStorageService.saveImageFromUrl(
                            record.getId(),
                            imageData.getUrl()
                    );
                } else {
                    // 如果既没有base64数据也没有URL，抛出异常
                    throw new RuntimeException("API返回的图像数据中既没有b64_json也没有url");
                }
                generatedImageIds.add(imageStorage.getId());
            }
            record.setGeneratedImages(JSON.toJSONString(generatedImageIds
                    .stream()
                    .map(String::valueOf).toList()));
            record.setSuccessful(true);
            record.setOpenaiCreatedTimestamp(response.getCreated());
            record.setStatus(2);
            record.setUpdatedAt(LocalDateTime.now());
            recordMapper.updateById(record);

            return response;
        } catch (HttpClientErrorException.BadRequest e) {
            record.setSuccessful(false)
                    .setErrorMessage(e.getMessage())
                    .setStatus(3);
            record.setUpdatedAt(LocalDateTime.now());
            recordMapper.updateById(record);
            //回退积分
            chatgptUserService.addUserLimit(user.getUserToken(), drawPoint, UserPointsRecord.SourceType.IMAGE_GEN_REFUND);
            log.error("生成图像失败: " + e.getMessage());

            if (e.getMessage().contains("safety system")) {
                throw new FoxException(FoxException.OPENAI_MODEL_FORBIDDEN, FORBIDDEN_MESSAGE);
            }
            throw e;
        } catch (Exception e) {
            record.setSuccessful(false)
                    .setErrorMessage(e.getMessage())
                    .setStatus(3);
            record.setUpdatedAt(LocalDateTime.now());
            recordMapper.updateById(record);
            //回退积分
            chatgptUserService.addUserLimit(user.getUserToken(), drawPoint, UserPointsRecord.SourceType.IMAGE_GEN_REFUND);
            log.error("生成图像失败: " + e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 编辑图像
     */
//    @Transactional
    public ImageGenerationResponse editImage(ImageEditRequest request) {
//        log.info("编辑图像请求:{}", JSON.toJSONString(request));
        String apiKey = localCache.getConfigMap().get("draw_api_token");
        String apiUrl = localCache.getConfigMap().get("draw_api_base_url");
        String draw_model = localCache.getConfigMap().getOrDefault("draw_model", "gpt-image-1");
        String quality = localCache.getConfigMap().getOrDefault("imageQuality", "high");
        if (StringUtils.isEmpty(apiKey) || StringUtils.isEmpty(apiUrl)) {
            throw new FoxException("请先配置draw_api_token和draw_api_base_url");
        }
        Integer drawPoint = Integer.valueOf(localCache.getConfigMap().getOrDefault("draw_consume_points", "10"));

        ChatgptUser user = UserContext.getUser();
        request.setModel(draw_model);
        ImageGenerationRecord record = ImageGenerationRecord.builder()
                .operationType(ImageGenerationRecord.OperationType.EDIT)
                .prompt(request.getPrompt())
                .model(request.getModel())
                .requestedCount(request.getN())
                .size(request.getSize())
                .quality(quality)
                .userToken(user.getUserToken())
                .status(1)
                .point(drawPoint)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        //消耗积分
        chatgptUserService.addUserLimit(user.getUserToken(), -drawPoint, UserPointsRecord.SourceType.IMAGE_GEN_CONSUME);

        // 先保存记录获取ID
        recordMapper.insert(record);
        boolean generateFail = false;
        String errorMessage = null;
        try {
            // 保存源图片
            List<Long> sourceImageIds = new ArrayList<>();
            for (MultipartFile image : request.getImages()) {
                ImageStorage imageStorage = imageStorageService.saveUploadedImage(
                        image,
                        record.getId(),
                        ImageStorage.ImageType.SOURCE
                );
                sourceImageIds.add(imageStorage.getId());
            }
            record.setSourceImageFiles(JSON.toJSONString(sourceImageIds
                    .stream()
                    .map(String::valueOf).toList()));

            // 保存遮罩图片
            if (request.getMask() != null) {
                ImageStorage maskStorage = imageStorageService.saveUploadedImage(
                        request.getMask(),
                        record.getId(),
                        ImageStorage.ImageType.MASK
                );
                record.setMaskImageFile(maskStorage.getId().toString());
            }

            // 调用API
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.setBearerAuth(apiKey);

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            if (request.getImages() != null && request.getImages().length > 0) {
                if ("dall-e-2".equals(request.getModel())) {
                    MultipartFile firstImage = request.getImages()[0];
                    body.add("image", new ByteArrayResource(firstImage.getBytes()) {
                        @Override
                        public String getFilename() {
                            return firstImage.getOriginalFilename();
                        }
                    });
                } else {
                    for (MultipartFile image : request.getImages()) {
                        body.add("image", new ByteArrayResource(image.getBytes()) {
                            @Override
                            public String getFilename() {
                                return image.getOriginalFilename();
                            }
                        });
                    }
                }
            } else {
                throw new RuntimeException("至少需要提供一张图片");
            }

            body.add("prompt", request.getPrompt());

            if (request.getMask() != null) {
                body.add("mask", new ByteArrayResource(request.getMask().getBytes()) {
                    @Override
                    public String getFilename() {
                        return request.getMask().getOriginalFilename();
                    }
                });
            }
            if (request.getModel() != null) body.add("model", request.getModel());
            if (request.getN() != null) body.add("n", request.getN());
            if (quality != null) body.add("quality", quality);
            if (request.getResponseFormat() != null) body.add("response_format", request.getResponseFormat());
            if (request.getSize() != null) body.add("size", request.getSize());

            HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(body, headers);

            String url = apiUrl + "/v1/images/edits";
            ImageGenerationResponse response = restTemplate.postForObject(url, entity, ImageGenerationResponse.class);

            // 更新记录
            record.setSuccessful(true)
                    .setOpenaiCreatedTimestamp(response.getCreated());

            // 保存生成的图片
            List<Long> generatedImageIds = new ArrayList<>();
            for (ImageGenerationResponse.ImageData imageData : response.getData()) {
                ImageStorage imageStorage;
                if (imageData.getB64_json() != null && !imageData.getB64_json().isEmpty()) {
                    // 如果有base64数据，使用它
                    imageStorage = imageStorageService.saveGeneratedImage(
                            record.getId(),
                            imageData.getB64_json()
                    );
                } else if (imageData.getUrl() != null && !imageData.getUrl().isEmpty()) {
                    // 如果没有base64数据但有URL，从URL下载图片
                    imageStorage = imageStorageService.saveImageFromUrl(
                            record.getId(),
                            imageData.getUrl()
                    );
                } else {
                    // 如果既没有base64数据也没有URL，抛出异常
                    throw new RuntimeException("API返回的图像数据中既没有b64_json也没有url");
                }
                generatedImageIds.add(imageStorage.getId());
            }
            record.setGeneratedImages(JSON.toJSONString(generatedImageIds
                    .stream()
                    .map(String::valueOf).toList()));
            record.setStatus(2);
            record.setUpdatedAt(LocalDateTime.now());
            recordMapper.updateById(record);

            return response;
        } catch (IOException e) {
            generateFail = true;
            errorMessage = e.getMessage();
            throw new RuntimeException("文件处理失败", e);
        } catch (HttpClientErrorException.BadRequest e) {
            generateFail = true;
            errorMessage = e.getMessage();
            if (e.getMessage().contains("safety system")) {
                throw new FoxException(FoxException.OPENAI_MODEL_FORBIDDEN, FORBIDDEN_MESSAGE);
            }
            throw e;
        } catch (Exception e) {
            generateFail = true;
            errorMessage = e.getMessage();
            throw e;
        } finally {
            if (generateFail) {
                record.setSuccessful(false)
                        .setErrorMessage(errorMessage);
                record.setStatus(3);
                // 回退积分
                chatgptUserService.addUserLimit(user.getUserToken(), drawPoint, UserPointsRecord.SourceType.IMAGE_GEN_REFUND);
                record.setUpdatedAt(LocalDateTime.now());
                recordMapper.updateById(record);
            }
        }
    }

    /**
     * 分页查询图像生成记录
     */
    public Page<ImageGenerationRecord> page(PageQueryReq req, boolean queryAll) {
        ChatgptUser user = UserContext.getUser();
        Page<ImageGenerationRecord> page = new Page<>(req.getPageNum(), req.getPageSize());
        QueryWrapper<ImageGenerationRecord> wrapper = new QueryWrapper<>();
        if (StringUtils.hasText(req.getQuery())) {
            wrapper.lambda().like(ImageGenerationRecord::getPrompt, req.getQuery());
        }
        if (!queryAll) {
            wrapper.lambda().eq(ImageGenerationRecord::getUserToken, user.getUserToken());
        }
        if (StringUtils.hasText(req.getSortField()) && StringUtils.hasText(req.getSortOrder())) {
            boolean asc = "ascend".equalsIgnoreCase(req.getSortOrder()) || "asc".equalsIgnoreCase(req.getSortOrder());
            if (asc) {
                wrapper.orderByAsc(req.getSortField());
            } else {
                wrapper.orderByDesc(req.getSortField());
            }
        } else {
            wrapper.orderByDesc("created_at");
        }
        return recordMapper.selectPage(page, wrapper);
    }

    public Page<UserPointsRecord> pageUserPoints(PageQueryReq req) {
        ChatgptUser user = UserContext.getUser();
        Page<UserPointsRecord> page = new Page<>(req.getPageNum(), req.getPageSize());
        QueryWrapper<UserPointsRecord> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(UserPointsRecord::getUserToken, user.getUserToken());
        if (StringUtils.hasText(req.getSortField()) && StringUtils.hasText(req.getSortOrder())) {
            boolean asc = "ascend".equalsIgnoreCase(req.getSortOrder()) || "asc".equalsIgnoreCase(req.getSortOrder());
            if (asc) {
                wrapper.orderByAsc(req.getSortField());
            } else {
                wrapper.orderByDesc(req.getSortField());
            }
        } else {
            wrapper.orderByDesc("created_at");
        }
        return userPointsRecordMapper.selectPage(page, wrapper);
    }

    @Resource
    private UserPointsRecordMapper userPointsRecordMapper;

    static final String FORBIDDEN_MESSAGE = "您触发了openai的违禁策略,请检查提示词或者图片是否包含色情、政治等违禁内容。" +
            "                        如果您坚信没有违禁，请调整提示词或者图片重试";
}
