package com.kedish.xyhelper_fox.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@Service
@Slf4j
public class FileStorageService {

    @Value("${app.file-storage.base-path:/data/xyhelper_fox/images}")
    private String baseStoragePath;


    /**
     * 初始化存储目录
     */
    public void init() {
        try {
            Files.createDirectories(Paths.get(baseStoragePath));
        } catch (IOException e) {
            throw new RuntimeException("Could not initialize storage location", e);
        }
    }

    /**
     * 存储上传的文件
     */
    public String storeFile(MultipartFile file) {
        return storeFile(file, null);
    }

    /**
     * 存储上传的文件，并在路径中包含recordId
     */
    public String storeFile(MultipartFile file, Long recordId) {
        try {
            if (file.isEmpty()) {
                throw new RuntimeException("Failed to store empty file");
            }

            // 生成文件名：年月/日/[recordId/]UUID.扩展名
            String fileName = generateFileName(file.getOriginalFilename(), recordId);
            Path targetPath = Paths.get(baseStoragePath).resolve(fileName);

            // 确保目标目录存在
            Files.createDirectories(targetPath.getParent());

            // 保存文件
            Files.copy(file.getInputStream(), targetPath);

            // 返回可访问的URL
            return  fileName;
        } catch (IOException e) {
            throw new RuntimeException("Failed to store file", e);
        }
    }

    /**
     * 存储Base64编码的图片
     */
    public String storeBase64Image(String base64Image) {
        return storeBase64Image(base64Image, null);
    }

    /**
     * 存储Base64编码的图片，并在路径中包含recordId
     */
    public String storeBase64Image(String base64Image, Long recordId) {
        try {
            // 移除Base64前缀（如果存在）
            String imageData = base64Image;
            if (base64Image.contains(",")) {
                imageData = base64Image.split(",")[1];
            }

            // 解码Base64数据
            byte[] imageBytes = Base64.decodeBase64(imageData);

            // 生成文件名
            String fileName = generateFileName(".png", recordId); // 默认使用PNG格式
            Path targetPath = Paths.get(baseStoragePath).resolve(fileName);

            // 确保目标目录存在
            Files.createDirectories(targetPath.getParent());

            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(targetPath.toFile())) {
                fos.write(imageBytes);
            }

            // 返回可访问的URL
            return  fileName;
        } catch (IOException e) {
            throw new RuntimeException("Failed to store base64 image", e);
        }
    }



    /**
     * 生成文件名
     * 格式：年月/日/UUID.扩展名
     */
    private String generateFileName(String originalFilename) {
        return generateFileName(originalFilename, null);
    }

    /**
     * 生成文件名
     * 格式：年月/日/[recordId/]UUID.扩展名
     */
    private String generateFileName(String originalFilename, Long recordId) {
        LocalDateTime now = LocalDateTime.now();
        String datePath = now.format(DateTimeFormatter.ofPattern("yyyyMM/dd"));
        String uuid = UUID.randomUUID().toString();

        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        // 如果有recordId，则在路径中包含recordId
        if (recordId != null) {
            return datePath + "/" + recordId + "/" + uuid + extension;
        } else {
            return datePath + "/" + uuid + extension;
        }
    }

    /**
     * 删除文件
     *
     * @param filePath 文件路径（相对于baseStoragePath的路径）
     * @return 是否删除成功
     */
    public boolean deleteFile(String filePath) {
        try {
            Path path = Paths.get(baseStoragePath).resolve(filePath);
            if (Files.exists(path)) {
                Files.delete(path);
                log.info("Deleted file: {}", path);
                return true;
            } else {
                log.warn("File not found for deletion: {}", path);
                return false;
            }
        } catch (IOException e) {
            log.error("Failed to delete file: {}", filePath, e);
            return false;
        }
    }

    /**
     * 删除目录及其所有内容
     *
     * @param directoryPath 目录路径（相对于baseStoragePath的路径）
     * @return 是否删除成功
     */
    public boolean deleteDirectory(String directoryPath) {
        try {
            Path path = Paths.get(baseStoragePath).resolve(directoryPath);
            if (Files.exists(path) && Files.isDirectory(path)) {
                // 递归删除目录及其内容
                Files.walk(path)
                    .sorted((p1, p2) -> -p1.compareTo(p2)) // 逆序，先删除文件，再删除目录
                    .forEach(p -> {
                        try {
                            Files.delete(p);
                            log.info("Deleted: {}", p);
                        } catch (IOException e) {
                            log.error("Failed to delete: {}", p, e);
                        }
                    });
                return true;
            } else {
                log.warn("Directory not found for deletion: {}", path);
                return false;
            }
        } catch (IOException e) {
            log.error("Failed to delete directory: {}", directoryPath, e);
            return false;
        }
    }
}
