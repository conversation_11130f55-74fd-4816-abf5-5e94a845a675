package com.kedish.xyhelper_fox.service;

import com.kedish.xyhelper_fox.utils.SnowflakeIdGenerator;
import org.springframework.stereotype.Service;

/**
 * Service for generating unique IDs using Snowflake algorithm
 */
@Service
public class IdGeneratorService {

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    public IdGeneratorService(SnowflakeIdGenerator snowflakeIdGenerator) {
        this.snowflakeIdGenerator = snowflakeIdGenerator;
    }

    /**
     * Generate a new unique ID using Snowflake algorithm
     *
     * @return a unique ID
     */
    public long generateId() {
        return snowflakeIdGenerator.nextId();
    }
}
