package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.repo.mapper.ForbiddenWordMapper;
import com.kedish.xyhelper_fox.repo.model.ForbiddenWord;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class ForbiddenWordService {

    @Resource
    private ForbiddenWordMapper forbiddenWordMapper;

    @Resource
    private LocalCache localCache;

    public List<String> getAll() {
        return forbiddenWordMapper.selectList(null).stream().map(ForbiddenWord::getWord).toList();
    }


    public void batchAdd(List<String> words) {
        List<String> all = getAll();
        words.removeAll(all);

        if (!words.isEmpty()) {
            List<ForbiddenWord> toInsert = words
                    .stream().map(word -> {
                        ForbiddenWord forbiddenWord = new ForbiddenWord();
                        forbiddenWord.setWord(word);
                        forbiddenWord.setCreatedAt(LocalDateTime.now());
                        return forbiddenWord;
                    }).toList();
            forbiddenWordMapper.insert(toInsert);
            // 重新加载
            localCache.loadForbiddenWords();
        }
    }

    public List<ForbiddenWord> getAllForbiddenWords() {
        return forbiddenWordMapper.selectList(null);
    }

    public Page<ForbiddenWord> page(PageQueryReq req) {
        Page<ForbiddenWord> page = new Page<>(req.getPageNum(), req.getPageSize());
        QueryWrapper<ForbiddenWord> queryWrapper = new QueryWrapper<>();
        if (StringUtils.hasLength(req.getQuery()))
            queryWrapper.and(wrapper -> wrapper.like("word", req.getQuery()));
        return forbiddenWordMapper.selectPage(page, queryWrapper);
    }

    public void deleteById(List<Long> ids) {
        forbiddenWordMapper.deleteByIds(ids);
    }
}
