package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kedish.xyhelper_fox.repo.mapper.ImageStorageMapper;
import com.kedish.xyhelper_fox.repo.model.ImageStorage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;

/**
 * 图片存储服务类
 */
@Service
@Slf4j
public class ImageStorageService {

    private final ImageStorageMapper imageStorageMapper;
    private final FileStorageService fileStorageService;
    private final IdGeneratorService idGeneratorService;
    private final RestTemplate restTemplate;

    @Value("${app.file-storage.base-path}")
    private String baseStoragePath;

    public ImageStorageService(ImageStorageMapper imageStorageMapper,
                              FileStorageService fileStorageService,
                              IdGeneratorService idGeneratorService,
                              RestTemplate restTemplate) {
        this.imageStorageMapper = imageStorageMapper;
        this.fileStorageService = fileStorageService;
        this.idGeneratorService = idGeneratorService;
        this.restTemplate = restTemplate;
    }

    /**
     * 保存上传的图片
     */
    @Transactional
    public ImageStorage saveUploadedImage(MultipartFile file, Long recordId, ImageStorage.ImageType imageType) throws IOException {
        // 保存文件到磁盘，并在路径中包含recordId
        String imageUrl = fileStorageService.storeFile(file, recordId);

        // 计算MD5
        String md5Hash = DigestUtils.md5Hex(file.getInputStream());

        // 从URL中提取存储的文件名
        String storedFilename = extractFilenameFromUrl(imageUrl);

        // 使用雪花算法生成ID
        long snowflakeId = idGeneratorService.generateId();

        // 创建记录
        ImageStorage imageStorage = ImageStorage.builder()
                .id(snowflakeId)
                .recordId(recordId)
                .imageType(imageType)
                .originalFilename(file.getOriginalFilename())
                .storedFilename(storedFilename)
                .fileSize(file.getSize())
                .contentType(file.getContentType())
                .imageUrl(imageUrl)
                .md5Hash(md5Hash)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // 保存记录
        imageStorageMapper.insert(imageStorage);

        return imageStorage;
    }

    /**
     * 保存生成的图片信息
     */
    @Transactional
    public ImageStorage saveGeneratedImage(Long recordId, String base64Data) throws IOException {
        // 保存Base64图片到磁盘，并在路径中包含recordId
        String imageUrl = fileStorageService.storeBase64Image(base64Data, recordId);

        // 从URL中提取存储的文件名
        String storedFilename = extractFilenameFromUrl(imageUrl);

        // 计算文件大小
        byte[] imageBytes = Base64.getDecoder().decode(base64Data);
        long fileSize = imageBytes.length;

        // 计算MD5
        String md5Hash = DigestUtils.md5Hex(new ByteArrayInputStream(imageBytes));

        // 使用雪花算法生成ID
        long snowflakeId = idGeneratorService.generateId();

        // 创建记录
        ImageStorage imageStorage = ImageStorage.builder()
                .id(snowflakeId)
                .recordId(recordId)
                .imageType(ImageStorage.ImageType.GENERATED)
                .storedFilename(storedFilename)
                .fileSize(fileSize)
                .contentType("image/png") // 假设生成的图片都是PNG格式
                .imageUrl(imageUrl)
                .md5Hash(md5Hash)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        imageStorageMapper.insert(imageStorage);

        return imageStorage;
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFilenameFromUrl(String imageUrl) {
        return imageUrl.substring(imageUrl.lastIndexOf("/") + 1);
    }

    /**
     * 删除图片
     */
    @Transactional
    public void deleteImage(Long imageId) {
        ImageStorage imageStorage = imageStorageMapper.selectById(imageId);
        if (imageStorage != null && imageStorage.getImageUrl() != null) {
            // 删除物理文件
//            fileStorageService.deleteFile(imageStorage.getImageUrl());
            // 删除数据库记录
            imageStorageMapper.deleteById(imageId);
        }
    }

    /**
     * 下载图片文件
     *
     * @param id 图片存储ID
     * @return 图片文件资源
     */
    public ResponseEntity<Resource> downloadImage(Long id) {
        ImageStorage imageStorage = imageStorageMapper.selectById(id);
        if (imageStorage == null || imageStorage.getStoredFilename() == null) {
            throw new RuntimeException("图片不存在");
        }

        try {
            Path filePath = Paths.get(baseStoragePath).resolve(imageStorage.getImageUrl());
            Resource resource = new UrlResource(filePath.toUri());

            if (!resource.exists() || !resource.isReadable()) {
                throw new RuntimeException("无法读取图片文件");
            }

            String contentType = imageStorage.getContentType();
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            String filename = imageStorage.getOriginalFilename();
            if (filename == null) {
                filename = imageStorage.getStoredFilename().substring(
                    imageStorage.getStoredFilename().lastIndexOf("/") + 1
                );
            }

            // 计算ETag (基于文件的MD5)
            String etag = imageStorage.getMd5Hash();

            // 设置缓存时间为7天
            long cacheSeconds = 7 * 24 * 60 * 60;

            return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .header(HttpHeaders.CACHE_CONTROL, "public, max-age=" + cacheSeconds)
                .header(HttpHeaders.PRAGMA, "public")
                .header(HttpHeaders.EXPIRES, String.valueOf(System.currentTimeMillis() + cacheSeconds * 1000))
                .header(HttpHeaders.ETAG, "\"" + etag + "\"")
                .body(resource);
        } catch (IOException e) {
            throw new RuntimeException("无法下载图片文件", e);
        }
    }

    public ImageStorage selectById(long id) {
        return imageStorageMapper.selectById(id);
    }

    /**
     * 根据查询条件获取图片存储记录列表
     *
     * @param queryWrapper 查询条件
     * @return 图片存储记录列表
     */
    public List<ImageStorage> list(QueryWrapper<ImageStorage> queryWrapper) {
        return imageStorageMapper.selectList(queryWrapper);
    }

    /**
     * 根据ID删除图片存储记录（逻辑删除）
     *
     * @param id 记录ID
     * @return 是否删除成功
     */
    public boolean removeById(Long id) {
        return imageStorageMapper.deleteById(id) > 0;
    }

    /**
     * 从URL下载图片并保存
     * 用于处理OpenAI API返回url而不是b64_json的情况
     */
    @Transactional
    public ImageStorage saveImageFromUrl(Long recordId, String imageUrl) throws IOException {
        try {
            // 生成临时文件路径
            Path tempFile = Files.createTempFile("download-", ".png");

            // 下载图片到临时文件
            URL url = new URL(imageUrl);
            Files.copy(url.openStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);

            // 读取图片数据
            byte[] imageData = Files.readAllBytes(tempFile);

            // 计算MD5
            String md5Hash = DigestUtils.md5Hex(new ByteArrayInputStream(imageData));

            // 保存图片到存储系统
            String storedImageUrl = fileStorageService.storeBase64Image(
                Base64.getEncoder().encodeToString(imageData),
                recordId
            );

            // 从URL中提取存储的文件名
            String storedFilename = extractFilenameFromUrl(storedImageUrl);

            // 使用雪花算法生成ID
            long snowflakeId = idGeneratorService.generateId();

            // 创建记录
            ImageStorage imageStorage = ImageStorage.builder()
                    .id(snowflakeId)
                    .recordId(recordId)
                    .imageType(ImageStorage.ImageType.GENERATED)
                    .storedFilename(storedFilename)
                    .fileSize((long) imageData.length)
                    .contentType("image/png") // 假设生成的图片都是PNG格式
                    .imageUrl(storedImageUrl)
                    .md5Hash(md5Hash)
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();

            // 保存记录
            imageStorageMapper.insert(imageStorage);

            // 删除临时文件
            Files.deleteIfExists(tempFile);

            return imageStorage;
        } catch (Exception e) {
            throw new IOException("Failed to save image from URL: " + e.getMessage(), e);
        }
    }
}
