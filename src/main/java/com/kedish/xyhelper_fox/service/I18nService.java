package com.kedish.xyhelper_fox.service;

import com.kedish.xyhelper_fox.config.I18nConfig;
import com.kedish.xyhelper_fox.utils.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * 国际化服务类
 * 提供高级的国际化功能
 */
@Service
@Slf4j
public class I18nService {

    @Resource
    private MessageSource messageSource;

    /**
     * 获取当前语言的所有消息
     * @return 当前语言的所有消息
     */
    public Map<String, String> getAllMessages() {
        return getAllMessages(LocaleContextHolder.getLocale());
    }

    /**
     * 获取指定语言的所有消息
     * @param locale 语言环境
     * @return 指定语言的所有消息
     */
    public Map<String, String> getAllMessages(Locale locale) {
        Map<String, String> messages = new HashMap<>();
        
        // 这里可以根据需要加载所有的消息键
        // 由于ResourceBundle的限制，我们需要预定义消息键列表
        List<String> messageKeys = getMessageKeys();
        
        for (String key : messageKeys) {
            try {
                String message = messageSource.getMessage(key, null, locale);
                messages.put(key, message);
            } catch (Exception e) {
                log.debug("Message key '{}' not found for locale: {}", key, locale);
            }
        }
        
        return messages;
    }

    /**
     * 批量获取消息
     * @param keys 消息键列表
     * @return 消息映射
     */
    public Map<String, String> getMessages(List<String> keys) {
        return getMessages(keys, LocaleContextHolder.getLocale());
    }

    /**
     * 批量获取指定语言的消息
     * @param keys 消息键列表
     * @param locale 语言环境
     * @return 消息映射
     */
    public Map<String, String> getMessages(List<String> keys, Locale locale) {
        Map<String, String> messages = new HashMap<>();
        
        for (String key : keys) {
            if (StringUtils.hasText(key)) {
                String message = I18nUtils.getMessage(key, null, locale);
                messages.put(key, message);
            }
        }
        
        return messages;
    }

    /**
     * 获取格式化消息
     * @param key 消息键
     * @param args 格式化参数
     * @return 格式化后的消息
     */
    public String getFormattedMessage(String key, Object... args) {
        return I18nUtils.getMessage(key, args);
    }

    /**
     * 获取指定语言的格式化消息
     * @param key 消息键
     * @param locale 语言环境
     * @param args 格式化参数
     * @return 格式化后的消息
     */
    public String getFormattedMessage(String key, Locale locale, Object... args) {
        return I18nUtils.getMessage(key, args, locale);
    }

    /**
     * 检查消息键是否存在
     * @param key 消息键
     * @return 是否存在
     */
    public boolean hasMessage(String key) {
        return hasMessage(key, LocaleContextHolder.getLocale());
    }

    /**
     * 检查指定语言的消息键是否存在
     * @param key 消息键
     * @param locale 语言环境
     * @return 是否存在
     */
    public boolean hasMessage(String key, Locale locale) {
        try {
            messageSource.getMessage(key, null, locale);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取支持的语言信息
     * @return 支持的语言信息列表
     */
    public List<Map<String, String>> getSupportedLanguageInfo() {
        return I18nUtils.getSupportedLanguages();
    }

    /**
     * 验证语言代码
     * @param langCode 语言代码
     * @return 是否有效
     */
    public boolean isValidLanguageCode(String langCode) {
        return I18nUtils.isSupportedLanguage(langCode);
    }

    /**
     * 获取语言显示名称
     * @param langCode 语言代码
     * @return 显示名称
     */
    public String getLanguageDisplayName(String langCode) {
        Locale locale = I18nUtils.getLocaleByCode(langCode);
        if (I18nUtils.isChineseLocale()) {
            return locale.equals(Locale.SIMPLIFIED_CHINESE) ? "中文" : "English";
        } else {
            return locale.equals(Locale.SIMPLIFIED_CHINESE) ? "Chinese" : "English";
        }
    }

    /**
     * 获取当前语言代码
     * @return 当前语言代码
     */
    public String getCurrentLanguageCode() {
        Locale locale = LocaleContextHolder.getLocale();
        if (locale.equals(Locale.SIMPLIFIED_CHINESE)) {
            return "zh";
        } else if (locale.equals(Locale.ENGLISH)) {
            return "en";
        }
        return "zh"; // 默认
    }

    /**
     * 获取预定义的消息键列表
     * @return 消息键列表
     */
    private List<String> getMessageKeys() {
        return Arrays.asList(
            // 通用消息
            "common.success", "common.fail", "common.error", "common.param.error",
            "common.unauthorized", "common.forbidden", "common.confirm", "common.cancel",
            "common.save", "common.delete", "common.edit", "common.add", "common.search",
            "common.reset", "common.submit", "common.loading", "common.close", "common.refresh",
            "common.export", "common.import", "common.upload", "common.download", "common.preview",
            "common.settings", "common.help", "common.about", "common.version", "common.language", "common.theme",
            
            // 用户相关
            "user.login.success", "user.login.fail", "user.logout.success", "user.register.success",
            "user.register.fail", "user.password.change.success", "user.password.change.fail",
            "user.not.found", "user.token.expired", "user.not.admin", "user.username", "user.password",
            "user.email", "user.phone", "user.profile", "user.avatar", "user.nickname",
            "user.invalid.credentials", "user.account.locked", "user.account.expired", "user.permission.denied",
            
            // 配置相关
            "config.get.fail", "config.update.success", "config.update.fail", "config.not.found", "config.invalid.value",
            
            // 邮件相关
            "email.send.success", "email.send.fail", "email.code.invalid", "email.code.expired",
            "email.code.sent", "email.invalid.format",
            
            // 文件相关
            "file.upload.success", "file.upload.fail", "file.not.found", "file.size.exceeded",
            "file.type.not.supported", "file.download.success", "file.download.fail",
            
            // 系统相关
            "system.maintenance", "system.busy", "system.error", "system.timeout", "system.service.unavailable",
            
            // 车辆相关
            "car.not.found", "car.access.denied", "car.limit.exceeded", "car.status.invalid",
            
            // 绘图相关
            "draw.generation.success", "draw.generation.fail", "draw.limit.exceeded",
            "draw.prompt.invalid", "draw.style.not.supported",
            
            // 激活码相关
            "activation.code.invalid", "activation.code.used", "activation.code.expired",
            "activation.code.exchange.success", "activation.code.not.found", "activation.code.insufficient.balance",
            
            // 国际化相关
            "i18n.language.switch.success", "i18n.language.switch.fail", "i18n.language.not.supported", "i18n.message.not.found",
            
            // 验证相关
            "validation.required", "validation.email", "validation.phone", "validation.password",
            "validation.confirm.password", "validation.min.length", "validation.max.length",
            "validation.numeric", "validation.url", "validation.date", "validation.time",
            
            // 分页相关
            "pagination.total", "pagination.page", "pagination.page.size", "pagination.goto",
            "pagination.prev", "pagination.next", "pagination.first", "pagination.last",
            
            // API相关
            "api.rate.limit.exceeded", "api.quota.exceeded", "api.key.invalid", "api.service.error", "api.timeout",
            
            // 支付相关
            "payment.success", "payment.fail", "payment.pending", "payment.cancelled",
            "payment.refunded", "payment.amount.invalid", "payment.method.not.supported"
        );
    }
}
