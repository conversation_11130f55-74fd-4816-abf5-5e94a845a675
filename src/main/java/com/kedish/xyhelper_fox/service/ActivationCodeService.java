package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.constant.UserTypeEnum;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.model.req.AddActivationCodeReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.repo.mapper.ActivationCodeMapper;
import com.kedish.xyhelper_fox.repo.model.ActivationCode;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.repo.model.SalesPlan;
import com.kedish.xyhelper_fox.security.UserContext;
import com.kedish.xyhelper_fox.utils.I18nUtils;
import com.kedish.xyhelper_fox.utils.ShortUUID;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class ActivationCodeService {

    @Resource
    private ActivationCodeMapper activationCodeMapper;

    @Resource
    private SalesPlanService salesPlanService;

    @Resource
    private ChatgptUserService chatgptUserService;


    public String batchAdd(AddActivationCodeReq req) {
        int num = req.getNum();
        Long salesPlanId = req.getSalesPlanId();
        Long expireDate = req.getExpireDate();
        if (num <= 0) {
            return "";
        }

        SalesPlan salesPlan = salesPlanService.getById(salesPlanId);

        if (salesPlan == null) {
            throw new FoxException("config.not.found", null);
        }
        String batchId = UUID.randomUUID().toString();
        StringBuilder sb = new StringBuilder();

        List<ActivationCode> toadd = new java.util.ArrayList<>();
        for (int i = 0; i < num; i++) {
            String uuid = ShortUUID.encode(UUID.randomUUID());
            ActivationCode activationCode = new ActivationCode();
            sb.append(uuid).append("\n");
            activationCode.setActivationCode(uuid);
            activationCode.setRemark("批量生成,batchId: " + batchId);
            activationCode.setStatus(0);
            activationCode.setSalesPlanId(salesPlanId);
            activationCode.setSalesPlanName(salesPlan.getName());
            if (expireDate != null) {
                activationCode.setExpirationTime(
                        LocalDateTime.ofInstant(Instant.ofEpochMilli(expireDate), java.time.ZoneId.systemDefault()));
            }
            activationCode.setCreatedAt(LocalDateTime.now());
            toadd.add(activationCode);
        }

        activationCodeMapper.insert(toadd);
        return sb.toString();
    }

    public Page<ActivationCode> page(PageQueryReq req) {
        Page<ActivationCode> page = new Page<>(req.getPageNum(), req.getPageSize());
        QueryWrapper<ActivationCode> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("created_at","id");
        return activationCodeMapper.selectPage(page, queryWrapper);
    }

    public void exchange(String activationCode) {
        QueryWrapper<ActivationCode> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("activation_code", activationCode);

        ActivationCode selected = activationCodeMapper.selectOne(queryWrapper);

        if (selected == null) {
            throw new FoxException("activation.code.not.found", null);
        }
        if (selected.getStatus() == 1) {
            throw new FoxException("activation.code.used", null);
        }
        if (selected.getExpirationTime() != null
                && selected.getExpirationTime().isBefore(LocalDateTime.now())) {
            throw new FoxException("activation.code.expired", null);
        }
        selected.setStatus(1);

        ChatgptUser user = UserContext.getUser();
        selected.setUsedBy(user.getUserToken());
        selected.setUsedAt(LocalDateTime.now());
        selected.setUpdatedAt(LocalDateTime.now());
        activationCodeMapper.updateById(selected);

        SalesPlan salesPlan = salesPlanService.getById(selected.getSalesPlanId());

        chatgptUserService.addUserRights(salesPlan.getValidDays(),
                salesPlan.getMembershipType(), user.getUserToken());
    }

    public void deleteByIds(List<Long> ids) {
        activationCodeMapper.deleteByIds(ids);
    }
}
