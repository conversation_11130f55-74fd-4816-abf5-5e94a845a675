package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.req.PaymentMethodReq;
import com.kedish.xyhelper_fox.model.resp.PaymentMethodVO;
import com.kedish.xyhelper_fox.model.resp.SimpleVO;
import com.kedish.xyhelper_fox.repo.mapper.PaymentMethodMapper;
import com.kedish.xyhelper_fox.repo.model.PaymentMethod;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class PaymentMethodService {

    @Resource
    private PaymentMethodMapper paymentMethodMapper;


    public void addOrUpdate(PaymentMethodReq req) {
        if (req.getId() != null) {
            PaymentMethod paymentMethod = paymentMethodMapper.selectById(req.getId());

            paymentMethod.setName(req.getName());
            paymentMethod.setPaymentType(req.getPaymentType());
            paymentMethod.setAppid(req.getAppid());
            paymentMethod.setAppkey(req.getAppkey());
            paymentMethod.setCallbackUrl(req.getCallbackUrl());
            paymentMethod.setPaymentUrl(req.getPaymentUrl());
            paymentMethod.setIsEnabled(req.getIsEnabled());
            paymentMethod.setUpdatedAt(java.time.LocalDateTime.now());
            paymentMethodMapper.updateById(paymentMethod);
        } else {
            PaymentMethod paymentMethod = new PaymentMethod();
            paymentMethod.setName(req.getName());
            paymentMethod.setPaymentType(req.getPaymentType());
            paymentMethod.setAppid(req.getAppid());
            paymentMethod.setAppkey(req.getAppkey());
            paymentMethod.setCallbackUrl(req.getCallbackUrl());
            paymentMethod.setPaymentUrl(req.getPaymentUrl());
            paymentMethod.setIsEnabled(req.getIsEnabled());
            paymentMethod.setCreatedAt(java.time.LocalDateTime.now());
            paymentMethodMapper.insert(paymentMethod);
        }
    }

    public void delete(Long id) {
        paymentMethodMapper.deleteById(id);
    }

    public Page<PaymentMethod> page(PageQueryReq req) {
        Page<PaymentMethod> page = new Page<>(req.getPageNum(), req.getPageSize());
        return paymentMethodMapper.selectPage(page, null);
    }

    public List<SimpleVO> queryAll() {


        List<PaymentMethod> paymentMethods = paymentMethodMapper.selectList(null);
        return paymentMethods.stream()
                .filter(PaymentMethod::getIsEnabled).map(paymentMethod -> {
            PaymentMethodVO simpleVO = new PaymentMethodVO();
            simpleVO.setId(paymentMethod.getId());
            simpleVO.setName(paymentMethod.getName());
            simpleVO.setPaymentType(paymentMethod.getPaymentType());
            return simpleVO;
        }).collect(java.util.stream.Collectors.toList());
    }

    public PaymentMethod getById(Long paymentMethodId) {
        return paymentMethodMapper.selectById(paymentMethodId);
    }
}
