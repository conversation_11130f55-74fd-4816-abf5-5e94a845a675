package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.model.req.Oauth2ClientReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.repo.mapper.Oauth2ClientMapper;
import com.kedish.xyhelper_fox.repo.model.Oauth2Client;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class Oauth2ClientService {

    @Resource
    private Oauth2ClientMapper oauth2ClientMapper;

    public Oauth2Client getByType(String type) {
        return oauth2ClientMapper.selectOne(
                new QueryWrapper<Oauth2Client>()
                        .eq("type", type)
        );
    }

    public Page<Oauth2Client> page(PageQueryReq req) {
        Page<Oauth2Client> page = new Page<>(req.getPageNum(), req.getPageSize());
        return oauth2ClientMapper.selectPage(page, new QueryWrapper<Oauth2Client>()
                .orderByDesc("create_time"));
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(Oauth2ClientReq req) {
        Oauth2Client oauth2Client = new Oauth2Client();
        BeanUtils.copyProperties(req, oauth2Client);
        
        if (oauth2Client.getId() == null) {
            oauth2Client.setCreateTime(LocalDateTime.now());
            oauth2Client.setUpdateTime(LocalDateTime.now());
            oauth2ClientMapper.insert(oauth2Client);
        } else {
            oauth2Client.setUpdateTime(LocalDateTime.now());
            oauth2ClientMapper.updateById(oauth2Client);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Integer> ids) {
        oauth2ClientMapper.deleteBatchIds(ids);
    }

    public List<Oauth2Client> queryAll() {
        return oauth2ClientMapper.selectList(new QueryWrapper<>());
    }
}