package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.repo.mapper.UserPromotionMapper;
import com.kedish.xyhelper_fox.repo.model.UserPromotion;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Service
@Slf4j
public class UserPromotionService {

    @Resource
    private UserPromotionMapper userPromotionMapper;

    public void addPromotionAmount(String userToken, BigDecimal amount) {

        UserPromotion userPromotion = userPromotionMapper
                .selectOne(new QueryWrapper<UserPromotion>().eq("user_token", userToken));

        if (userPromotion == null) {
            userPromotion = new UserPromotion();
            userPromotion.setUserToken(userToken);
            userPromotion.setPromotionAmount(amount);
            userPromotion.setPromotionOrderNum(1);
            userPromotion.setWaitWithdrawAmount(amount);
            userPromotion.setWithdrawAmount(BigDecimal.ZERO);
            userPromotion.setCreatedAt(LocalDateTime.now());
            userPromotion.setUpdatedAt(LocalDateTime.now());
            userPromotionMapper.insert(userPromotion);
        } else {
            userPromotion.setPromotionAmount(userPromotion.getPromotionAmount().add(amount));
            userPromotion.setPromotionOrderNum(userPromotion.getPromotionOrderNum() + 1);
            userPromotion.setWaitWithdrawAmount(userPromotion.getWaitWithdrawAmount().add(amount));
            userPromotion.setUpdatedAt(LocalDateTime.now());
            userPromotionMapper.updateById(userPromotion);

        }
    }

    /**
     * 查询用户推广信息
     * @param userToken 用户标识
     * @return 用户推广信息，如果不存在则返回null
     */
    public UserPromotion getPromotionInfo(String userToken) {
        log.info("查询用户推广信息，userToken: {}", userToken);

        UserPromotion userPromotion = userPromotionMapper
                .selectOne(new QueryWrapper<UserPromotion>().eq("user_token", userToken));

        if (userPromotion == null) {
            log.info("用户推广信息不存在，userToken: {}", userToken);
        } else {
            log.info("查询到用户推广信息，userToken: {}, 推广金额: {}, 推广订单数: {}, 待提现金额: {}, 已提现金额: {}",
                    userToken, userPromotion.getPromotionAmount(), userPromotion.getPromotionOrderNum(),
                    userPromotion.getWaitWithdrawAmount(), userPromotion.getWithdrawAmount());
        }

        return userPromotion;
    }

    /**
     * 分页查询用户推广信息
     * @param req 分页查询请求
     * @return 分页结果
     */
    public Page<UserPromotion> page(PageQueryReq req) {
        log.info("分页查询用户推广信息，pageNum: {}, pageSize: {}, query: {}",
                req.getPageNum(), req.getPageSize(), req.getQuery());

        Page<UserPromotion> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 设置排序
        if (StringUtils.hasLength(req.getSortField()) && StringUtils.hasLength(req.getSortOrder())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(req.getSortField());
            orderItem.setAsc("asc".equalsIgnoreCase(req.getSortOrder()));
            page.addOrder(orderItem);
        } else {
            // 默认按更新时间倒序排列
            page.addOrder(OrderItem.desc("updated_at"));
        }

        QueryWrapper<UserPromotion> queryWrapper = new QueryWrapper<>();

        // 如果有查询条件，支持按用户标识模糊查询
        if (StringUtils.hasLength(req.getQuery())) {
            queryWrapper.like("user_token", req.getQuery());
        }

        return userPromotionMapper.selectPage(page, queryWrapper);
    }
}
