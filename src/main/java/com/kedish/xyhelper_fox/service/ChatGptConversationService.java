package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.repo.mapper.ChatGptConversationMapper;
import com.kedish.xyhelper_fox.repo.model.ChatgptConversation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ChatGptConversationService {

    @Resource
    private ChatGptConversationMapper chatGptConversationMapper;

    public Page<ChatgptConversation> page(PageQueryReq re) {
        Page<ChatgptConversation> page = new Page<>(re.getPageNum(), re.getPageSize());
        if (StringUtils.hasLength(re.getSortField()) && StringUtils.hasLength(re.getSortOrder())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(re.getSortField());
            orderItem.setAsc(re.getSortOrder().equals("asc"));
            page.addOrder(orderItem);
        }
        QueryWrapper<ChatgptConversation> queryWrapper = new QueryWrapper<>();
        return chatGptConversationMapper.selectPage(page, queryWrapper);
    }

    public void deleteByIds(List<Long> ids) {
        chatGptConversationMapper.deleteByIds(ids);
    }

    @Scheduled(cron = "0 0 4 * * ? ")
    public void scheduleDelete() {

        log.info("开始清理聊天记录...");
        Map<String, String> configMap = localCache.getConfigMap();
        String s = configMap.get("logRetentionDays");
        if (StringUtils.hasLength(s)) {
            int retentionDays = Integer.parseInt(s);
            log.info("清理聊天记录，保留天数：{}", retentionDays);


            chatGptConversationMapper.deleteByDays(retentionDays);
        } else {
            log.info("清理聊天记录，未配置天数，不执行");
        }
    }

    @Resource
    private LocalCache localCache;
}
