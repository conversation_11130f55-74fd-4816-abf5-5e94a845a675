package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.model.req.CouponReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.repo.mapper.CouponMapper;
import com.kedish.xyhelper_fox.repo.model.Coupon;
import com.kedish.xyhelper_fox.repo.model.SalesPlan;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Arrays;

@Service
@Slf4j
public class CouponService {

    @Resource
    private CouponMapper couponMapper;

    @Resource
    private SalesPlanService salesPlanService;

    public Coupon getCouponByCode(String code) {
        QueryWrapper<Coupon> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("coupon_code", code);
        return couponMapper.selectOne(queryWrapper);
    }

    public void addOrUpdate(CouponReq addReq) {
        if (addReq.getDiscountAmount().compareTo(BigDecimal.ZERO) <= 0
                && addReq.getDiscountPercentage().compareTo(BigDecimal.ZERO) <= 0) {
            throw new FoxException("coupon.discount.zero", null);
        }

        if (addReq.getId() != null) {
            Coupon coupon = couponMapper.selectById(addReq.getId());
            coupon.setCouponCode(addReq.getCouponCode());
            coupon.setDiscountAmount(addReq.getDiscountAmount());
            coupon.setDiscountPercentage(addReq.getDiscountPercentage());
            coupon.setSalesPlan(addReq.getSalesPlan());
            coupon.setExpirationTime(addReq.getExpirationTime());
            coupon.setRemark(addReq.getRemark());
            coupon.setUpdatedAt(LocalDateTime.now());
            couponMapper.updateById(coupon);
        } else {
            if (getCouponByCode(addReq.getCouponCode()) != null) {
                throw new FoxException("coupon.code.exists", null);
            }

            Coupon coupon = new Coupon();
            coupon.setCouponCode(addReq.getCouponCode());
            coupon.setDiscountAmount(addReq.getDiscountAmount());
            coupon.setDiscountPercentage(addReq.getDiscountPercentage());
            coupon.setSalesPlan(addReq.getSalesPlan());
            coupon.setExpirationTime(addReq.getExpirationTime());
            coupon.setRemark(addReq.getRemark());
            coupon.setCreatedAt(LocalDateTime.now());
            coupon.setUpdatedAt(LocalDateTime.now());
            couponMapper.insert(coupon);
        }
    }

    public Page<Coupon> page(PageQueryReq req) {
        Page<Coupon> page = new Page<>(req.getPageNum(), req.getPageSize());
        if (StringUtils.hasLength(req.getSortField()) && StringUtils.hasLength(req.getSortOrder())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(req.getSortField());
            orderItem.setAsc(req.getSortOrder().equals("asc"));
            page.addOrder(orderItem);
        }
        QueryWrapper<Coupon> queryWrapper = new QueryWrapper<>();
        return couponMapper.selectPage(page, queryWrapper);
    }

    public Coupon getById(Long id) {
        return couponMapper.selectById(id);
    }

    public BigDecimal checkAccess(String couponCode, Long salesPlanId) {
        Coupon coupon = getCouponByCode(couponCode);
        if (coupon == null) {
            throw new FoxException("coupon.not.found", null);
        }
        if (coupon.getExpirationTime().isBefore(LocalDateTime.now())) {
            throw new FoxException("coupon.expired", null);
        }
        String salesPlan = coupon.getSalesPlan();

        if (StringUtils.hasLength(salesPlan)) {
            boolean b = Arrays.stream(salesPlan.split(","))
                    .map(Long::valueOf)
                    .anyMatch(e -> e.equals(salesPlanId));
            if (!b) {
                throw new FoxException("coupon.not.available", null);
            }
        }
        return getDiscount(coupon, salesPlanService.getById(salesPlanId));
    }

    public BigDecimal checkAccess(Coupon coupon, SalesPlan salesPlan) {
        String salesPlanIds = coupon.getSalesPlan();

        if (StringUtils.hasLength(salesPlanIds)) {
            boolean b = Arrays.stream(salesPlanIds.split(","))
                    .map(Long::valueOf)
                    .anyMatch(e -> e.equals(salesPlan.getId()));
            if (!b) {
                throw new FoxException("coupon.not.available", null);
            }
        }
        return getDiscount(coupon, salesPlan);
    }

    public BigDecimal getDiscount(Coupon coupon, SalesPlan salesPlan) {
        BigDecimal amount = salesPlan.getAmount();
        BigDecimal discount = BigDecimal.ZERO;
        if (coupon.getDiscountAmount() != null) {
            discount = coupon.getDiscountAmount();
        } else if (coupon.getDiscountPercentage() != null) {
            discount = amount.multiply(coupon.getDiscountPercentage()).divide(new BigDecimal(100), 2, RoundingMode.CEILING);
        }
        return discount;
    }

    public void deleteById(Long id) {
        couponMapper.deleteById(id);
    }
}
