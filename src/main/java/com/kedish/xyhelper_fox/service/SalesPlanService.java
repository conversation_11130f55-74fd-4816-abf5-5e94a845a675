package com.kedish.xyhelper_fox.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.req.SalesPlanAddReq;
import com.kedish.xyhelper_fox.repo.mapper.SalesPlanMapper;
import com.kedish.xyhelper_fox.repo.model.SalesPlan;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

@Service
@Slf4j
public class SalesPlanService {

    @Resource
    private SalesPlanMapper salesPlanMapper;

    public void addOrUpdate(SalesPlanAddReq addReq) {
        if (addReq.getId() != null) {
            SalesPlan salesPlan = salesPlanMapper.selectById(addReq.getId());
            salesPlan.setName(addReq.getName());
            salesPlan.setAmount(addReq.getAmount());
            salesPlan.setValidDays(addReq.getValidDays());
            salesPlan.setMembershipType(addReq.getMembershipType());
            salesPlan.setIsDisplayOnFront(addReq.getIsDisplayOnFront());
            salesPlan.setPer("1h");
            salesPlan.setLimit(0);
            salesPlan.setOrder(addReq.getOrder());
            salesPlan.setTags(addReq.getTags());
            salesPlan.setUpdatedAt(LocalDateTime.now());
            salesPlan.setIsHot(addReq.getIsHot());
            salesPlanMapper.updateById(salesPlan);

        } else {
            SalesPlan salesPlan = new SalesPlan();
            salesPlan.setName(addReq.getName());
            salesPlan.setAmount(addReq.getAmount());
            salesPlan.setValidDays(addReq.getValidDays());
            salesPlan.setMembershipType(addReq.getMembershipType());
            salesPlan.setIsDisplayOnFront(addReq.getIsDisplayOnFront());
            salesPlan.setPer("1h");
            salesPlan.setLimit(0);
            salesPlan.setOrder(addReq.getOrder());
            salesPlan.setTags(addReq.getTags());
            salesPlan.setCreatedAt(LocalDateTime.now());
            salesPlan.setUpdatedAt(LocalDateTime.now());
            salesPlan.setIsHot(addReq.getIsHot());
            salesPlanMapper.insert(salesPlan);
        }
    }

    public void deleteById(Long id) {
        salesPlanMapper.deleteById(id);
    }

    public SalesPlan getById(Long id) {
        return salesPlanMapper.selectById(id);
    }

    public Page<SalesPlan> page(PageQueryReq req) {
        Page<SalesPlan> page = new Page<>(req.getPageNum(), req.getPageSize());
        if (StringUtils.hasLength(req.getSortField()) && StringUtils.hasLength(req.getSortOrder())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(req.getSortField());
            orderItem.setAsc(req.getSortOrder().equals("asc"));
            page.addOrder(orderItem);

        }
        QueryWrapper<SalesPlan> queryWrapper = new QueryWrapper<>();
        return salesPlanMapper.selectPage(page, queryWrapper);
    }
}
