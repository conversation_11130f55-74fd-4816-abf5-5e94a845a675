package com.kedish.xyhelper_fox.service;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.kedish.xyhelper_fox.oauth2.GithubTokenResponse;
import com.kedish.xyhelper_fox.oauth2.GithubUser;
import com.kedish.xyhelper_fox.repo.model.Oauth2Client;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

@Service
public class GithubService {

    private String tokenUrl = "https://github.com/login/oauth/access_token";

    private String userUrl = "https://api.github.com/user";

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    // 使用授权码获取访问令牌
    public String getAccessToken(String code, Oauth2Client oauth2Client) throws IOException {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("client_id", oauth2Client.getClientId());
        map.add("client_secret", oauth2Client.getClientSecret());
        map.add("code", code);
        map.add("redirect_uri", oauth2Client.getRedirectUri());

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
        String response = restTemplate.postForObject(tokenUrl, request, String.class);

        // 解析响应获取访问令牌
        // GitHub返回的可能是URL编码格式或JSON格式，需要处理两种情况
        if (response.startsWith("{")) {
            // JSON格式响应
            GithubTokenResponse tokenResponse = objectMapper.readValue(response, GithubTokenResponse.class);
            return tokenResponse.getAccessToken();
        } else {
            // URL编码格式响应，例如: access_token=xxx&token_type=bearer
            String[] params = response.split("&");
            for (String param : params) {
                if (param.startsWith("access_token=")) {
                    return param.substring("access_token=".length());
                }
            }
            throw new RuntimeException("Failed to extract access token from response: " + response);
        }
    }

    // 使用访问令牌获取用户信息
    public GithubUser getUserInfo(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + accessToken);
        headers.set("Accept", "application/vnd.github.v3+json");
        HttpEntity<String> entity = new HttpEntity<>(headers);

        return restTemplate.exchange(userUrl, org.springframework.http.HttpMethod.GET, entity, GithubUser.class).getBody();
    }
}
