package com.kedish.xyhelper_fox.model.req;

import lombok.Data;

@Data
public class BatchAddUserReq {

    /**
     * 售卖计划ID（可选，与手动选择二选一）
     */
    private Long salesPlanId;

    /**
     * 手动选择的用户组ID（可选，与售卖计划二选一）
     */
    private Long groupId;

    /**
     * 手动选择的有效天数（可选，与售卖计划二选一）
     */
    private Integer validDays;

    /**
     * 批量添加的用户数量
     */
    private Integer num;

    /**
     * 用户名类型：1-UUID，2-时间戳
     */
    private Integer userTokenType;

    /**
     * 用户名前缀（可选）
     */
    private String userTokenPrefix;

    /**
     * 备注
     */
    private String remark;

    /**
     * 默认密码（可选，如果不设置则使用随机密码）
     */
    private String defaultPassword;
}
