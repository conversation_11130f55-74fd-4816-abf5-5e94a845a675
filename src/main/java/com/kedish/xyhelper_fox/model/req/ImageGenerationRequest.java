package com.kedish.xyhelper_fox.model.req;

import lombok.Data;

/**
 * 图像生成请求类
 */
@Data
public class ImageGenerationRequest {
    /**
     * 需要生成的图像的文本描述
     * 必需参数，最大长度1000字符
     */
    private String prompt;

    /**
     * 要生成的图像数量
     * 可选参数，范围1-10，默认为1
     */
    private Integer n;

    /**
     * 生成图像的尺寸
     * 可选参数，可选值：256x256、512x512、1024x1024
     */
    private String size;

    /**
     * 使用的模型名称
     * 可选参数，例如：gpt-image-1
     */
    private String model;
}
