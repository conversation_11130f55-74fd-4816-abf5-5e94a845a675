package com.kedish.xyhelper_fox.model.req;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AddUserReq {

    private Long id;
    private String username;
    private String password;
    private String email;
//    private String captchaCode;

    //    @TableField("`limit`")
    private Long limit;

    private String per;

    //    @TableField("user_type")
    private Integer userType;

    //    @TableField("enable_claude")
    private Boolean enableClaude;

    private Integer status;

    private LocalDateTime expireTime;
    private LocalDateTime plusExpireTime;

    private String remark;

    private Long groupId;

    private Boolean enableUserTokenLogin;
}
