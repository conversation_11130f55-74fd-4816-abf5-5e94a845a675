package com.kedish.xyhelper_fox.model.req;

import lombok.Data;

@Data
public class AddGrokSessionReq {
    /**
     * {"sort":0,"email":"213121123","password":"12312","status":1,"isPro":0,"officialSession":"eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uX2lkIjoiMjRiYTU5YmQtYmNhMi00YTg2LWFhZWItZjkzMTgxY2FmNmI1In0.Dn_mOq45t-gktEORtD-8Ihpg8BitU84XXsp2zRtbYjg","remark":"","count":0,"createTime":"2025-05-23 11:07:36","deepersearch":3,"deepsearch":10,"deleted_at":null,"grok2":0,"grok3":18,"id":23,"reasoning":8,"updateTime":"2025-05-23 11:07:39","updatemodel":"2025-05-23 11:07:39"}
     */
    private Long sort;
    private String carID;
    private String email;
    private String password;
    private Integer status;
    private Integer isPro;
    private String officialSession;
    private String remark;
    private Long id;

}
