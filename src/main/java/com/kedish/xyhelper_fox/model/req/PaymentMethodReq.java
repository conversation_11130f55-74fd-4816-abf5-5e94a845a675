package com.kedish.xyhelper_fox.model.req;

import lombok.Data;

@Data
public class PaymentMethodReq {

    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 支付方式
     */
    private String paymentType;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 应用密钥
     */
    private String appkey;

    /**
     * 回调地址
     */
    private String callbackUrl;

    /**
     * 支付地址
     */
    private String paymentUrl;

    /**
     * 是否启用，1表示启用，0表示禁用
     */
    private Boolean isEnabled;

}
