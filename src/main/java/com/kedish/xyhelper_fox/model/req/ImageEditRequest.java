package com.kedish.xyhelper_fox.model.req;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 图像编辑请求类
 */
@Data
public class ImageEditRequest {
    /**
     * 要编辑的图片
     * 必需参数
     * - gpt-image-1: 支持多张png、webp或jpg文件，每张大小<25MB
     * - dall-e-2: 仅支持一张方形png文件，大小<4MB
     */
    private MultipartFile[] images;

    /**
     * 所需图像的文本描述
     * 必需参数
     * - dall-e-2: 最大长度1000字符
     * - gpt-image-1: 最大长度32000字符
     */
    private String prompt;

    /**
     * 遮罩图片
     * 可选参数
     * - 必须是有效的PNG文件，小于4MB
     * - 尺寸必须与第一张image相同
     * - 完全透明区域（alpha值为零）指示应编辑image的位置
     * - 遮罩将应用于第一张图片
     */
    private MultipartFile mask;

    /**
     * 用于生成图像的模型
     * 可选参数
     * - 支持dall-e-2和gpt-image-1
     * - 默认为dall-e-2
     */
    private String model;

    /**
     * 要生成的图像数量
     * 可选参数，范围1-10
     */
    private Integer n;

    /**
     * 生成图像的质量
     * 可选参数
     * - gpt-image-1: 支持high、medium和low
     * - dall-e-2: 仅支持standard
     * - 默认为auto
     */
    private String quality;

    /**
     * 返回生成图像的格式
     * 可选参数
     * - 可选值：url或b64_json
     * - URL在图像生成后60分钟内有效
     * - 仅适用于dall-e-2，gpt-image-1始终返回base64编码的图像
     */
    private String responseFormat;

    /**
     * 生成图像的尺寸
     * 可选参数
     * - gpt-image-1: 1024x1024、1536x1024（横向）、1024x1536（纵向）或auto（默认值）
     * - dall-e-2: 256x256、512x512或1024x1024
     */
    private String size;
}
