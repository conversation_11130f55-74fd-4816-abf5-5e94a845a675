package com.kedish.xyhelper_fox.model.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kedish.xyhelper_fox.repo.model.ClaudeSession;
import lombok.Data;

@Data
public class GrokCar {
    /**
     * {"sort":0,"email":"213121123","password":"12312","status":1,"isPro":0,"officialSession":"eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uX2lkIjoiMjRiYTU5YmQtYmNhMi00YTg2LWFhZWItZjkzMTgxY2FmNmI1In0.Dn_mOq45t-gktEORtD-8Ihpg8BitU84XXsp2zRtbYjg","remark":"","count":0,"createTime":"2025-05-23 11:07:36","deepersearch":3,"deepsearch":10,"deleted_at":null,"grok2":0,"grok3":18,"id":23,"reasoning":8,"updateTime":"2025-05-23 11:07:39","updatemodel":"2025-05-23 11:07:39"}
     */
    private String carID;

    private String desc;

    private Integer isPro;
    private String remark;

    private String label;
    @JsonIgnore
    private boolean isVirtual;

}
