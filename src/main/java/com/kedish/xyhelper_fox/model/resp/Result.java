package com.kedish.xyhelper_fox.model.resp;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class Result {
    private Integer code;

    private String msg;

    private Boolean isPro;

    private String expireTime;

    public static Result ok() {
        return new Result(1, "ok", true, null);
    }

    public static Result ok(String expireTime) {
        return new Result(1, "ok", true, expireTime);
    }


    public static Result fail(String msg) {
        return new Result(0, msg, false, null);
    }
}
