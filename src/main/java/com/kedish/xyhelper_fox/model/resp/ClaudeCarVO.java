package com.kedish.xyhelper_fox.model.resp;

import lombok.Data;

@Data
public class ClaudeCarVO {
    /**
     * {"sort":0,"email":"213121123","password":"12312","status":1,"isPro":0,"officialSession":"eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW92zRtbYjg","remark":"","count":0,"createTime":"2025-05-23 11:07:36","deepersearch":3,"deepsearch":10,"deleted_at":null,"grok2":0,"grok3":18,"id":23,"reasoning":8,"updateTime":"2025-05-23 11:07:39","updatemodel":"2025-05-23 11:07:39"}
     */
    private String email;
    private String password;
    private Integer status;
    private String officialSession;
    private Integer count;
    private String createTime;
    private Integer isPro;
    private String remark;
    private String updateTime;
    private String orgnizationsid;
    private String carID;
    private Long id;

}
