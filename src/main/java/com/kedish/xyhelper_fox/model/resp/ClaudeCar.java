package com.kedish.xyhelper_fox.model.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kedish.xyhelper_fox.repo.model.ClaudeSession;
import lombok.Data;

@Data
public class ClaudeCar {

    private String carID;

    private String desc;

    private Integer isPro;
    private String remark;

    private String label;

    @JsonIgnore
    private boolean isVirtual;

    public static ClaudeCar fromClaudeSession(ClaudeSession session) {
        ClaudeCar claudeCar = new ClaudeCar();
        claudeCar.setCarID(session.getCarID());
        claudeCar.setDesc("推荐");
        claudeCar.setIsPro(session.getAccountType());
        claudeCar.setLabel(session.getAccountType()==1?"PRO":"4.0");
        return claudeCar;
    }
}
