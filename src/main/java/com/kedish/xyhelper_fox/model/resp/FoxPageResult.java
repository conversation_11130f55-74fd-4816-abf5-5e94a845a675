package com.kedish.xyhelper_fox.model.resp;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class FoxPageResult extends FoxResult{

    private int total;
    private int pageNum;
    private int pageSize;

    public FoxPageResult(int code, String msg, Object data) {
        super(code, msg, data);
    }

    public static FoxPageResult  fromPage(Page<?> page){
        FoxPageResult pageResult = new FoxPageResult(0, "", page.getRecords());
        pageResult.total = (int) page.getTotal();
        pageResult.pageNum = (int) page.getCurrent();
        pageResult.pageSize = (int) page.getSize();
        return pageResult;
    }

}
