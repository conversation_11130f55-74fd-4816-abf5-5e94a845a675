package com.kedish.xyhelper_fox.model.resp;

import com.kedish.xyhelper_fox.utils.I18nUtils;
import lombok.Data;

@Data
public class FoxResult {

    private int code;
    private String msg;
    private Object data;

    public FoxResult(){}


    public FoxResult(int code, String msg, Object data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static FoxResult ok(Object data) {
        return new FoxResult(0, I18nUtils.getMessage("common.success"), data);
    }

    public static FoxResult ok() {
        return new FoxResult(0, I18nUtils.getMessage("common.success"), null);
    }

    public static FoxResult fail(String msg) {
        return new FoxResult(1, msg, null);
    }

    /**
     * 使用国际化消息键创建失败结果
     * @param messageKey 国际化消息键
     * @return FoxResult
     */
    public static FoxResult failWithI18n(String messageKey) {
        return new FoxResult(1, I18nUtils.getMessage(messageKey), null);
    }

    /**
     * 使用国际化消息键和参数创建失败结果
     * @param messageKey 国际化消息键
     * @param args 参数
     * @return FoxResult
     */
    public static FoxResult failWithI18n(String messageKey, Object[] args) {
        return new FoxResult(1, I18nUtils.getMessage(messageKey, args), null);
    }
}
