package com.kedish.xyhelper_fox.model.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kedish.xyhelper_fox.repo.model.ChatGptSession;
import lombok.Data;

@Data
public class GptCar {
    //8位 随机码，不可重复，包含大小写字母数字，例如znzrFUqI
    private String carID;

    //使用次数
    private Integer count;

    //备注
    private String remark;

    // plus team pro
    private String label;

    //是否是plus
    private Integer isPlus;

    private Integer isPro;

    //推荐，空闲，可用，繁忙
    private String desc;

    private Integer sort;

    @JsonIgnore
    private boolean isVirtual;

    @JsonIgnore
    private Integer iqStatus;

    private int clearsIn = -1;

    public boolean isTeam() {
        return isPlus != null && isPlus == 1 && "TEAM".equals(label);
    }

    public boolean isPro() {
        return isPlus != null && isPlus == 1 && "PRO".equals(label);
    }

    public static GptCar fromChatGptSession(ChatGptSession session) {
        GptCar car = new GptCar();
        car.setCarID(session.getCarID());
        car.setCount(Math.toIntExact(session.getCount()));
        car.setRemark(session.getRemark());
        car.setVirtual(false);
        car.setClearsIn(-1);
        car.setCount(0);
        if (session.getIsPlus() != null && session.getIsPlus() == 1) {
            car.setLabel("PLUS");
            car.setIsPlus(1);
        } else {
            car.setLabel("4o");
        }
        if (session.getSort() != null) {

            car.setSort(Math.toIntExact(session.getSort()));
        } else {
            car.setSort(0);
        }
        if (session.getIqStatus() != null) {
            car.setIqStatus(session.getIqStatus());
        } else {
            car.setIqStatus(1);
        }
        return car;
    }

    public static ClaudeCar toClaudeCar(GptCar gptCar) {
        ClaudeCar claudeCar = new ClaudeCar();
        claudeCar.setCarID(gptCar.getCarID());
        claudeCar.setDesc(gptCar.getDesc());
        claudeCar.setIsPro(gptCar.getIsPro());
        claudeCar.setVirtual(gptCar.isVirtual);
        claudeCar.setLabel(gptCar.getLabel());
        return claudeCar;
    }

    public static GrokCar toGrokCar(GptCar gptCar) {
        GrokCar grokCar = new GrokCar();
        grokCar.setCarID(gptCar.getCarID());
        grokCar.setDesc(gptCar.getDesc());
        grokCar.setIsPro(gptCar.getIsPro());
        grokCar.setRemark(gptCar.getRemark());
        grokCar.setVirtual(gptCar.isVirtual);
        grokCar.setLabel(gptCar.getLabel());
        return grokCar;
    }
}
