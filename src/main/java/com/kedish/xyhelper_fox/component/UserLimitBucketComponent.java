package com.kedish.xyhelper_fox.component;

import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.repo.model.GroupRateLimit;
import com.kedish.xyhelper_fox.repo.model.UserGroup;
import com.kedish.xyhelper_fox.repo.model.UserRateLimit;
import com.kedish.xyhelper_fox.service.ChatgptUserService;
import com.kedish.xyhelper_fox.service.UserGroupService;
import com.kedish.xyhelper_fox.utils.UserUtils;
import io.github.bucket4j.BucketConfiguration;
import io.github.bucket4j.TokensInheritanceStrategy;
import io.github.bucket4j.VerboseBucket;
import io.github.bucket4j.redis.redisson.cas.RedissonBasedProxyManager;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UserLimitBucketComponent {

    private static final String COMMON_LIMIT_PREFIX = "bucket4j:common_limit_:";
    @Resource
    private RedissonBasedProxyManager<String> proxyManager;
    @Resource
    private ChatgptUserService chatgptUserService;

    @Resource
    private UserGroupService userGroupService;
    @Resource
    private LocalCache localCache;

    @AllArgsConstructor
    @Getter
    public static class RateLimit {
        //速率
        private int limit;
        //周期
        private String per;
        //倍率
        private int multiplier;
        //模型
        private String model;
    }

    private String getKey(String userToken, String model) {
        return COMMON_LIMIT_PREFIX + userToken + ":" + model;
    }

    public void resetBucket(ChatgptUser user, List<GroupRateLimit> groupRateLimits) {
        String userToken = user.getUserToken();

        // 清除该用户的所有限速缓存
        localCache.clearUserRateLimitCache(userToken);

        for (GroupRateLimit groupRateLimit : groupRateLimits) {
            RateLimit rateLimit = getRateLimit(groupRateLimit);
            String model = groupRateLimit.getModel();
            if (rateLimit == null) {
//                continue;
                proxyManager.removeProxy(getKey(userToken, model));
            } else {
                proxyManager.getProxy(getKey(userToken, model), getBucketConfiguration(rateLimit))
                        .replaceConfiguration(getBucketConfiguration(rateLimit).get(),
                                TokensInheritanceStrategy.RESET);
                // 更新缓存
                localCache.cacheUserModelRateLimit(userToken, model, rateLimit);
            }
        }

        log.info("resetBucket userToken:{}", userToken);
    }

    public void resetBucket(ChatgptUser user) {
        String userToken = user.getUserToken();

        // 清除该用户的所有限速缓存
        localCache.clearUserRateLimitCache(userToken);

        // Get both user-specific and group rate limits
        List<GroupRateLimit> groupRateLimits = userGroupService.getGroupRateLimit(user.getGroupId());
        List<UserRateLimit> userRateLimits = userGroupService.getUserRateLimit(userToken);

        // Create a map of model -> user rate limit for quick lookup
        Map<String, UserRateLimit> userRateLimitMap = null;
        if (!CollectionUtils.isEmpty(userRateLimits)) {
            userRateLimitMap = userRateLimits.stream()
                    .collect(Collectors.toMap(UserRateLimit::getModel, limit -> limit, (a, b) -> a));
        }

        // Process group rate limits, but override with user-specific limits if they exist
        for (GroupRateLimit groupRateLimit : groupRateLimits) {
            String model = groupRateLimit.getModel();
            RateLimit rateLimit = null;

            // Check if there's a user-specific rate limit for this model
            if (userRateLimitMap != null && userRateLimitMap.containsKey(model)) {
                UserRateLimit userRateLimit = userRateLimitMap.get(model);
                if (userRateLimit.getRate() > 0) {
                    rateLimit = new RateLimit(userRateLimit.getRate(), userRateLimit.getPeriod(),
                            userRateLimit.getMultiplier(), model);
                }
                // Remove from map to track which ones we've processed
                userRateLimitMap.remove(model);
            } else {
                // Use group rate limit
                rateLimit = getRateLimit(groupRateLimit);
            }

            if (rateLimit == null) {
                proxyManager.removeProxy(getKey(userToken, model));
            } else {
                proxyManager.getProxy(getKey(userToken, model), getBucketConfiguration(rateLimit))
                        .replaceConfiguration(getBucketConfiguration(rateLimit).get(),
                                TokensInheritanceStrategy.RESET);
                // 更新缓存
                localCache.cacheUserModelRateLimit(userToken, model, rateLimit);
            }
        }

        // Process any remaining user-specific rate limits that don't have corresponding group limits
        if (userRateLimitMap != null && !userRateLimitMap.isEmpty()) {
            for (UserRateLimit userRateLimit : userRateLimitMap.values()) {
                String model = userRateLimit.getModel();
                if (userRateLimit.getRate() > 0) {
                    RateLimit rateLimit = new RateLimit(userRateLimit.getRate(), userRateLimit.getPeriod(),
                            userRateLimit.getMultiplier(), model);
                    proxyManager.getProxy(getKey(userToken, model), getBucketConfiguration(rateLimit))
                            .replaceConfiguration(getBucketConfiguration(rateLimit).get(),
                                    TokensInheritanceStrategy.RESET);
                    // 更新缓存
                    localCache.cacheUserModelRateLimit(userToken, model, rateLimit);
                } else {
                    proxyManager.removeProxy(getKey(userToken, model));
                }
            }
        }

        log.info("resetBucket userToken:{}", userToken);
    }

    public VerboseBucket getBucket(String userToken, RateLimit rateLimit) {

        return proxyManager.getProxy(getKey(userToken, rateLimit.getModel()), getBucketConfiguration(rateLimit)).asVerbose();
    }

    public RateLimit getRateLimit(GroupRateLimit rateLimit) {


        if (rateLimit == null) {
            return null;
        }
        if (rateLimit.getRate() <= 0) {
            return null;
        }
        return new RateLimit(rateLimit.getRate(), rateLimit.getPeriod(), rateLimit.getMultiplier(),
                rateLimit.getModel());

    }

    public RateLimit getRateLimit(String userToken, String model) {
        // 获取用户和用户组信息
        ChatgptUser userByUserToken;
        if (UserUtils.isVisitor(userToken)) {
            userByUserToken = UserUtils.getVisitor(userToken);
        } else {
            userByUserToken = chatgptUserService.getUserByUserToken(userToken);
        }
        if (userByUserToken == null) {
            throw new RuntimeException("用户不存在");
        }

        UserGroup userGroup = chatgptUserService.getUserGroup(userByUserToken.getGroupId());
        if (userGroup == null) {
            throw new RuntimeException("用户分组不存在");
        }

        // 处理模型名称映射
        String originalModel = model;

        // 处理模型名称映射逻辑
        boolean share4oLimit = userGroup.getAutoMiniShareGpt4Limit();
        String share4oModels = localCache.getConfigMap().get("share4oModels");

        if (StringUtils.hasLength(share4oModels) && share4oLimit) {
            String[] models = share4oModels.split(",");
            for (String s : models) {
                if (model.equals(s)) {
                    model = "gpt-4o";
                    break;
                }
            }
        }

        // 首先尝试从本地缓存获取
        RateLimit cachedRateLimit = localCache.getUserModelRateLimit(userToken, model);
        if (cachedRateLimit != null) {
            log.debug("Cache hit for user: {} model: {}", userToken, model);
            return cachedRateLimit;
        }

        log.debug("Cache miss for user: {} model: {}, fetching from database", userToken, model);

        // 首先检查是否有用户特定的限速
        List<UserRateLimit> userRateLimits = userGroupService.getUserRateLimit(userToken);
        if (!CollectionUtils.isEmpty(userRateLimits)) {
            Map<String, UserRateLimit> userRateLimitMap = userRateLimits
                    .stream()
                    .collect(Collectors
                            .toMap(UserRateLimit::getModel,
                                    e -> e,
                                    (a, b) -> a));

            UserRateLimit userRateLimit = userRateLimitMap.get(model.toLowerCase());
            if (userRateLimit != null) {
                // 没有访问该模型的权限
                if (userRateLimit.getRate() <= 0) {
                    return null;
                }
                RateLimit result = new RateLimit(userRateLimit.getRate(), userRateLimit.getPeriod(),
                        userRateLimit.getMultiplier(), model);
                // 缓存结果
                localCache.cacheUserModelRateLimit(userToken, model, result);
                return result;
            }
        }

        // 如果没有用户特定的限速，回退到组限速
        List<GroupRateLimit> groupRateLimit = userGroupService.getGroupRateLimit(userGroup.getId());

        if (CollectionUtils.isEmpty(groupRateLimit)) {
            // 不缓存空结果
            return null;
        }
        Map<String, GroupRateLimit> rateLimitMap = groupRateLimit
                .stream()
                .filter(e -> e.getRate() > 0)
                .collect(Collectors
                        .toMap(GroupRateLimit::getModel,
                                e -> e,
                                (a, b) -> a));

        GroupRateLimit rateLimit = rateLimitMap.get(model.toLowerCase());

        if (rateLimit == null || rateLimit.getRate() <= 0) {
            // 不缓存空结果
            return null;
        }

        RateLimit result = new RateLimit(rateLimit.getRate(), rateLimit.getPeriod(), rateLimit.getMultiplier(),
                model);
        // 缓存结果
        localCache.cacheUserModelRateLimit(userToken, model, result);
        return result;
    }


    private Supplier<BucketConfiguration> getBucketConfiguration(RateLimit rateLimit) {
        return () -> BucketConfiguration
                .builder()
                .addLimit(n -> n.capacity(rateLimit.getLimit())
                        .refillIntervally(rateLimit.getLimit(), per2Duration(rateLimit.per))).build();
    }

    private Duration per2Duration(String per) {
        long n = Long.parseLong(per.substring(0, per.length() - 1));
        if (per.toLowerCase().endsWith("h")) {
            return Duration.ofHours(n);
        } else if (per.toLowerCase().endsWith("m")) {
            return Duration.ofMinutes(n);
        } else if (per.toLowerCase().endsWith("d")) {
            return Duration.ofDays(n);

        } else if (per.toLowerCase().endsWith("w")) {
            return Duration.ofDays(n * 7);

        }
        throw new RuntimeException("时间单位错误");
    }
}
