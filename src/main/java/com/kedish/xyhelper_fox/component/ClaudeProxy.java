package com.kedish.xyhelper_fox.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.model.req.AddDDDSessionReq;
import com.kedish.xyhelper_fox.model.req.AddGptSessionReq;
import com.kedish.xyhelper_fox.model.resp.ClaudeCar;
import com.kedish.xyhelper_fox.model.resp.ClaudeCarVO;
import com.kedish.xyhelper_fox.model.resp.GptCar;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class ClaudeProxy {
    private final RestTemplate restTemplate;

    public ClaudeProxy(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Value("${chat-share-server.apiauth}")
    private String apiauth;

    @Value("${claudeshare-server.host:chatgpt-share-server-fox-ddd-share-server-1}")
    private String chatShareServerUrl;

    private String host;

    @Resource
    private LocalCache localCache;

    @PostConstruct
    public void init() {
        host = chatShareServerUrl;
    }

    public String addClaudeSession(ClaudeCarVO req) {
        String url = host + "/adminapi/claude/session/add";

        // 创建请求体
        var payload = req;

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("apiauth", apiauth); // 替换自己的APIAUTH

        // 创建 HttpEntity 对象
        HttpEntity<ClaudeCarVO> requestEntity = new HttpEntity<>(payload, headers);

        // 发送 POST 请求
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

        // 返回响应体
        return responseEntity.getBody();
    }

    public String updateClaudeSession(ClaudeCarVO req) {
        String url = host + "/adminapi/claude/session/update";


        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("apiauth", apiauth); // 替换自己的APIAUTH

        // 创建 HttpEntity 对象
        HttpEntity<ClaudeCarVO> requestEntity = new HttpEntity<>(req, headers);

        // 发送 POST 请求
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

        // 返回响应体
        return responseEntity.getBody();
    }

    public String deleteClaudeSession(List<Long> ids) {
        String url = host + "/adminapi/claude/session/delete";

        // 创建请求体
        var jsonObject = new JSONObject();
        jsonObject.put("ids", ids);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("apiauth", apiauth); // 替换自己的APIAUTH

        // 创建 HttpEntity 对象
        HttpEntity<JSONObject> requestEntity = new HttpEntity<>(jsonObject, headers);

        // 发送 POST 请求
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

        // 返回响应体
        return responseEntity.getBody();
    }
    public String getCarLabel(String carid) {
        String url = host + "/endpoint";

        try {
            ResponseEntity<String> forEntity = restTemplate.getForEntity(url + "?carid=" + carid, String.class);

            if (!forEntity.getStatusCode().equals(HttpStatusCode.valueOf(200))) {
                log.error("get car endpoint error,httpstatus:{}", forEntity.getStatusCode());
            }
            String body = forEntity.getBody();
            JSONObject jsonObject = JSONObject.parseObject(body);
            return jsonObject.getString("label");
        } catch (Exception e) {
            log.error("get car endpoint error", e);
        }
        return "";
    }
    public Page<ClaudeCarVO> page(int page, int pageSize) {
        String url = host + "/adminapi/claude/session/page";

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("page", page);
        jsonObject.put("size", pageSize);
        jsonObject.put("sort", "desc");
        jsonObject.put("order", "sort");

        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("apiauth", apiauth); // 替换自己的APIAUTH

            // 创建 HttpEntity 对象
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(jsonObject, headers);

            // 发送 POST 请求
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

            String body = responseEntity.getBody();
            JSONObject result = JSONObject.parseObject(body);

            if (result.getInteger("code") != 1000) {
                log.error("fetch carpage fail, httpstatus:{},body:{}", responseEntity.getStatusCode(), body);
                throw new RuntimeException("fetch carpage fail, httpstatus:" + responseEntity.getStatusCode() + ",body:" + body);
            }
            JSONObject data = result.getJSONObject("data");
            JSONObject pagination = data.getJSONObject("pagination");
            int total = pagination.getInteger("total");
            Page<ClaudeCarVO> claudeCarPage = new Page<>(page, pageSize);
            List<ClaudeCarVO> gptCars = new ArrayList<>();
            JSONArray list = data.getJSONArray("list");
            if (list == null) {
                return claudeCarPage;
            }
            for (Object datum : list) {
                JSONObject carJSON = (JSONObject) datum;
                Integer status = carJSON.getInteger("status");
                if (status == 1) {
                    ClaudeCarVO car = carJSON.toJavaObject(ClaudeCarVO.class);
                    gptCars.add(car);
                }
            }
            claudeCarPage.setTotal(total);
            claudeCarPage.setRecords(gptCars);
            return claudeCarPage;
        } catch (Exception e) {
            log.error("fetch carpage fail", e);
            throw e;
        }

    }

    public List<ClaudeCar> carPage() {
        String url = host + "/carpage";

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("page", 1);
        jsonObject.put("size", 10000);
        jsonObject.put("sort", "desc");
        jsonObject.put("order", "sort");

        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("apiauth", apiauth); // 替换自己的APIAUTH

            // 创建 HttpEntity 对象
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(jsonObject, headers);

            // 发送 POST 请求
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

            String body = responseEntity.getBody();
            JSONObject result = JSONObject.parseObject(body);

            if (result.getInteger("code") != 1000) {
                log.error("fetch carpage fail, httpstatus:{},body:{}", responseEntity.getStatusCode(), body);
                return new ArrayList<>();
            }
            JSONObject data = result.getJSONObject("data");

            List<ClaudeCar> gptCars = new ArrayList<>();
            JSONArray list = data.getJSONArray("list");
            if (list == null) {
                return gptCars;
            }
            for (Object datum : list) {
                JSONObject carJSON = (JSONObject) datum;
                Integer status = carJSON.getInteger("status");
                if (status == 1) {
                    ClaudeCar gptCar = new ClaudeCar();
                    gptCar.setCarID(carJSON.getString("carID"));
                    gptCar.setIsPro(carJSON.getInteger("isPro"));
                    gptCars.add(gptCar);
                }
            }
            return gptCars;
        } catch (Exception e) {
            log.error("fetch carpage fail", e);
            return new ArrayList<>();
        }

    }

}
