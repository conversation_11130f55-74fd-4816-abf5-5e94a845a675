package com.kedish.xyhelper_fox.component;

import com.alibaba.fastjson.JSONObject;
import com.kedish.xyhelper_fox.utils.EncryptionUtil;
import com.kedish.xyhelper_fox.utils.MachineCodeUtils;
import com.kedish.xyhelper_fox.utils.RSAUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.crypto.SecretKey;
import java.io.File;
import java.io.FileWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class AuthComponent {
    private static final String AUTH_IP = "**************:8181";
    private static final SecretKey AUTH_KEY = EncryptionUtil.getKeyFromString("zhoujianyu123456");
    private static final String MACHINE_CODE_FILE = "/data/file/machine_code.txt";
    private static final String privateKey = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC8D+XNnaBmw/Ds60X7bk0jHf5UcJdNhp6v7MWBxTXIFJ954MLAM1EU07Gt0KH1jCseBUVdPdO1Rmkm/0KldeVf9Lq/3r9pptnr3WauRLwYG5+oDpQueH3TThgFGOjCMsKvXwA8zb6TQVFLQSPejrRLN3zPNeiZn3vidEmAxn1iSJ/gHvwNzGfVz89di8ndqeXb88l+E89bCQHSzbdzdPJI9LVhNDVxEaUg+Ni0HOcNjJSkVUGrHYtd5HKu/P/cLLyHuDl+0gdn/a8AHchh9My97ozVBlN9pc/snihr6u3g+1Y/uePRJ/VOvN2rCZ/q9opLrA/vrbJjmjvbWOHjHROhAgMBAAECggEAAjtXERJcj7OmFY2yOipkMHU/6i0e4ozlL7XJ30EhowL5pIbiEMlbHjnn6yXlciZMcbIC0Xk1HLrd7xhK6G9GflXHbJT+ZQjVn7q0J//JUqjrQ2GO6SFFbv9HgjH6OynCjTW9qTsQ1qsJBbFbdV3wrk7VhJkBbmdJ31VddPGGFec8+9MxYz5HeXy63Doo5HQzvfOYO+N8UJVDHzRWm1LxUMTCdC6jh15w2siEOATog1Hk+/KJ47ZmTV+Dllb/bZejTPQ3nHqm8zrGEOh3YQj+dr/Zi0xgTmhuwMn79jFrprPBOfMljE6R/GV2jMcCK++GJqgTc9gIwWpHKyMDvito0QKBgQDAf7rDZLk/Ag8DZRJKNO8IWUNeNrfnT45NOLL93+NZIm0iHYWqfbXtk/xzrI5Lz98l1EvtxYRFnbKFzqJEgJGR8M+qDqjGLKBRzdWnH2rykIwh70C7rLi19ikTKcmpR7wkAJfn4R/a3LNIBwZwLYYOx/ejykgL9GCXFSN2uZwiSQKBgQD6GXuahczlQ0TrlWR5x3mLv06ubonWvdsUj7/Gm16yrDlT69ltki90VFTasMhx21982o/XT7z7G4N8hKgo2rhqAPj/Hxo4IwQQ2uFr5S7CUNR0jxDaS0sFpZJgqJ47wNvQqs7fkDVuJN4ST6W5LjFWrAimbs2MSInY+SrXzgfmmQKBgAtd/4tWQVIlLvnGWCQlqA/YJZBErutr2T1aIce4FtbGn6cqEyCUN9AFsxypjeWQKt8zACDUTxFFVKtuHkrpRK9XhwvFvdWmAPmTrBAoXMpiGsROOoZalrW4CYjmKNBkOgnuNwosUxM7gSK5t3mcVpQ83rS/Vy3Nin6hcuyp3TxxAoGAfRjz/5W/qEmelqwSJuvE+0bEx3FZFwW+KKnChkg+9OY6gqStDCab//cSvpTAnlrZh20Xl941q/DWMxlzt4O3zKYR5iKbjcrOBNRG/x4S5nVh36+UAWy56OgpsJmHFGJiURyu/5p1b1OXlNcsENVvLmg8p2sy+2FpOhNf/7P5tbkCgYAh+g8yBkjSCTfXNIZ4xq0F0hGf73YeNaxZJMrJ0iS2e53PrVUJoRETXd+97IBkmVFa3m3G+ub4hidCIcouMiJpFmXqna1aBu2jWrpsp50iSRRs0ejTRG2/0eVi5RdPXbUal/u3Pe3bg7OtwJ88VR95M2+INCl0jgDGQWtTaC1jJA==";

    @Getter
    private volatile LocalDateTime authExpireTime = null;

    @Resource
    private RestTemplate restTemplate;
    @Value("${system.ver}")
    @Getter
    private String systemVer;

    @Value("${auth.license-key:#{null}}")
    private String licenseKey;

    @Value("${auth.mode:online}")
    private String authMode;

    public boolean isAuthValid() {
        return authExpireTime != null && authExpireTime.isAfter(LocalDateTime.now());
    }

    @PostConstruct
    public void init() {


        if ("offline".equalsIgnoreCase(authMode)) {
            // 生成并保存机器码
            generateAndSaveMachineCode();
            // 离线授权模式
            validateLicense();
        } else {
            // 在线授权模式
            refreshAuth();
        }
    }

    /**
     * 刷新授权，每小时执行一次，仅在在线模式下生效
     */
    @Scheduled(fixedRate = 1, timeUnit = TimeUnit.HOURS)
    public void scheduledRefreshAuth() {
        if ("online".equalsIgnoreCase(authMode)) {
            refreshAuth();
        }
    }

    /**
     * 执行授权刷新逻辑
     */
    public void refreshAuth() {
        if (!"online".equalsIgnoreCase(authMode)) {
            log.warn("当前为离线授权模式，不执行在线授权刷新");
            return;
        }

        try {
            JSONObject json = new JSONObject();
            json.put("systemVer", systemVer);
            String reqBody = EncryptionUtil.encrypt(json.toJSONString(), AUTH_KEY);

            ResponseEntity<String> resp = restTemplate.postForEntity("http://" + AUTH_IP + "/api/auth/doAuth", reqBody, String.class);
            if (resp.getStatusCode().equals(HttpStatus.OK)) {
                JSONObject body = JSONObject.parseObject(EncryptionUtil.decrypt(resp.getBody(), AUTH_KEY));
                if (body == null) {
                    log.error("获取授权失败：响应解密后为空");
                    return;
                }
                if (body.getInteger("code") == 0) {
                    Long timeStamp = body.getLong("data");
                    authExpireTime = Instant.ofEpochMilli(timeStamp).atZone(ZoneId.systemDefault())
                            .toLocalDateTime();
                    log.info("在线授权成功，过期时间是 {}", authExpireTime);
                } else {
                    log.error("获取授权失败，code is {}", body.getInteger("code"));
                    authExpireTime = LocalDateTime.now();
                }
            } else {
                log.error("获取授权失败，http status is {}", resp.getStatusCode());
                authExpireTime = LocalDateTime.now();
            }
        } catch (Exception e) {
            log.error("刷新授权过程中发生异常", e);
        }
    }

    /**
     * 生成并保存机器码到文件
     */
    private void generateAndSaveMachineCode() {
        try {
            String machineCode = MachineCodeUtils.getMachineCode();

            // 确保目录存在
            Path filePath = Paths.get(MACHINE_CODE_FILE);
            Files.createDirectories(filePath.getParent());

            // 写入机器码到文件
            try (FileWriter writer = new FileWriter(MACHINE_CODE_FILE)) {
                writer.write(machineCode);
                log.info("机器码已保存到文件: {}", MACHINE_CODE_FILE);
            }
        } catch (Exception e) {
            log.error("保存机器码到文件失败", e);
        }
    }

    /**
     * 验证离线授权
     */
    private void validateLicense() {
        if (!"offline".equalsIgnoreCase(authMode)) {
            log.warn("当前为在线授权模式，不执行离线授权验证");
            return;
        }

        try {
            if (licenseKey == null || licenseKey.trim().isEmpty()) {
                log.error("未配置授权码，请在环境变量中设置 AUTH_LICENSE_KEY");
                return;
            }

            if (privateKey.trim().isEmpty()) {
                log.error("未配置私钥，请在环境变量中设置 AUTH_PRIVATE_KEY");
                return;
            }

            // 使用RSA私钥解密授权内容
            String decrypted = RSAUtils.decrypt(licenseKey, privateKey);
            if (decrypted == null) {
                log.error("授权码解密失败");
                return;
            }

            JSONObject license = JSONObject.parseObject(decrypted);

            // 验证机器码
            String machineCode = license.getString("machineCode");
            String currentMachineCode = MachineCodeUtils.getMachineCode();
            if (!currentMachineCode.equals(machineCode)) {
                log.error("机器码不匹配，授权无效");
                return;
            }

            // 设置过期时间
            Long expireTimestamp = license.getLong("expireTime");
            if (expireTimestamp != null) {
                authExpireTime = LocalDateTime.ofEpochSecond(expireTimestamp, 0, java.time.ZoneOffset.UTC);
                log.info("离线授权验证成功，过期时间：{}", authExpireTime);
            } else {
                log.error("授权码格式错误：缺少过期时间");
            }
        } catch (Exception e) {
            log.error("验证授权码失败", e);
        }
    }

}
