package com.kedish.xyhelper_fox.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kedish.xyhelper_fox.model.req.AddGptSessionReq;
import com.kedish.xyhelper_fox.model.resp.GptCar;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class ChatShareServerProxy {
    private final RestTemplate restTemplate;

    public ChatShareServerProxy(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Value("${chat-share-server.apiauth}")
    private String apiauth;

    @Value("${chatshare-server.host:chatgpt-share-server-fox-chatgpt-share-server-1}")
    private String chatShareServerUrl;

    private String host;


    @PostConstruct
    public void init() {
        host = "http://" + chatShareServerUrl + ":8001";
    }

    public String addChatGptSession(AddGptSessionReq req) {
        String url = host + "/adminapi/chatgpt/session/add";

        // 创建请求体
        var payload = req;

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("apiauth", apiauth); // 替换自己的APIAUTH

        // 创建 HttpEntity 对象
        HttpEntity<AddGptSessionReq> requestEntity = new HttpEntity<>(payload, headers);

        // 发送 POST 请求
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

        // 返回响应体
        return responseEntity.getBody();
    }

    public String deleteChatGptSession(List<Long> ids) {
        String url = host + "/adminapi/chatgpt/session/delete";

        // 创建请求体
        var jsonObject = new JSONObject();
        jsonObject.put("ids", ids);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("apiauth", apiauth); // 替换自己的APIAUTH

        // 创建 HttpEntity 对象
        HttpEntity<JSONObject> requestEntity = new HttpEntity<>(jsonObject, headers);

        // 发送 POST 请求
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

        // 返回响应体
        return responseEntity.getBody();
    }

    public String updateChatGptSession(AddGptSessionReq req) {
        String url = host + "/adminapi/chatgpt/session/update";


        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("apiauth", apiauth); // 替换自己的APIAUTH

        // 创建 HttpEntity 对象
        HttpEntity<AddGptSessionReq> requestEntity = new HttpEntity<>(req, headers);

        // 发送 POST 请求
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

        // 返回响应体
        return responseEntity.getBody();
    }

    public String getCarLabel(String carid) {
        String url = host + "/endpoint";

        try {
            ResponseEntity<String> forEntity = restTemplate.getForEntity(url + "?carid=" + carid, String.class);

            if (!forEntity.getStatusCode().equals(HttpStatusCode.valueOf(200))) {
                log.error("get car endpoint error,httpstatus:{}", forEntity.getStatusCode());
            }
            String body = forEntity.getBody();
            JSONObject jsonObject = JSONObject.parseObject(body);
            return jsonObject.getString("label");
        } catch (Exception e) {
            log.error("get car endpoint error", e);
        }
        return "";
    }

    public List<GptCar> carPage() {
        String url = host + "/carpage";

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("page", 1);
        jsonObject.put("size", 10000);
        jsonObject.put("sort", "desc");
        jsonObject.put("order", "sort");

        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("apiauth", apiauth); // 替换自己的APIAUTH

            // 创建 HttpEntity 对象
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(jsonObject, headers);

            // 发送 POST 请求
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

            String body = responseEntity.getBody();
            JSONObject result = JSONObject.parseObject(body);

            if (result.getInteger("code") != 1000) {
                log.error("fetch carpage fail, httpstatus:{},body:{}", responseEntity.getStatusCode(), body);
                return new ArrayList<>();
            }
            JSONArray data = result.getJSONArray("data");

            List<GptCar> gptCars = new ArrayList<>();
            for (Object datum : data) {
                JSONObject carJSON = (JSONObject) datum;
                Integer status = carJSON.getInteger("status");
                if (status == 1) {
                    GptCar gptCar = new GptCar();
                    gptCar.setCarID(carJSON.getString("carID"));
                    gptCar.setIsPlus(carJSON.getInteger("isPlus"));
                    gptCars.add(gptCar);
                }
            }
            return gptCars;
        } catch (Exception e) {
            log.error("fetch carpage fail", e);
            return new ArrayList<>();
        }

    }

}
