package com.kedish.xyhelper_fox.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.model.req.AddGptSessionReq;
import com.kedish.xyhelper_fox.model.req.AddGrokSessionReq;
import com.kedish.xyhelper_fox.model.resp.Claude<PERSON>ar;
import com.kedish.xyhelper_fox.model.resp.GptCar;
import com.kedish.xyhelper_fox.model.resp.GrokCar;
import com.kedish.xyhelper_fox.model.resp.GrokCarVO;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class GrokProxy {
    private final RestTemplate restTemplate;

    public GrokProxy(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Value("${chat-share-server.apiauth}")
    private String apiauth;

    @Value("${grokshare-server.host:chatgpt-share-server-fox-grok-share-server-1}")
    private String chatShareServerUrl;

    private String host;


    @PostConstruct
    public void init() {
        host = chatShareServerUrl;
    }

    public String addGrokSession(GrokCarVO req) {
        String url = host + "/adminapi/grok/session/add";

        // 创建请求体
        var payload = req;

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("apiauth", apiauth); // 替换自己的APIAUTH

        // 创建 HttpEntity 对象
        HttpEntity<GrokCarVO> requestEntity = new HttpEntity<>(payload, headers);

        // 发送 POST 请求
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

        // 返回响应体
        return responseEntity.getBody();
    }

    public String deleteGrokSession(List<Long> ids) {
        String url = host + "/adminapi/grok/session/delete";

        // 创建请求体
        var jsonObject = new JSONObject();
        jsonObject.put("ids", ids);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("apiauth", apiauth); // 替换自己的APIAUTH

        // 创建 HttpEntity 对象
        HttpEntity<JSONObject> requestEntity = new HttpEntity<>(jsonObject, headers);

        // 发送 POST 请求
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

        // 返回响应体
        return responseEntity.getBody();
    }

    public String updateGrokSession(GrokCarVO req) {
        String url = host + "/adminapi/grok/session/update";


        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("apiauth", apiauth); // 替换自己的APIAUTH

        // 创建 HttpEntity 对象
        HttpEntity<GrokCarVO> requestEntity = new HttpEntity<>(req, headers);

        // 发送 POST 请求
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

        // 返回响应体
        return responseEntity.getBody();
    }

    public Page<GrokCarVO> page(int page, int pageSize) {
        String url = host + "/adminapi/grok/session/page";

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("page", page);
        jsonObject.put("size", pageSize);
        jsonObject.put("sort", "desc");
        jsonObject.put("order", "sort");

        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("apiauth", apiauth); // 替换自己的APIAUTH

            // 创建 HttpEntity 对象
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(jsonObject, headers);

            // 发送 POST 请求
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

            String body = responseEntity.getBody();
            JSONObject result = JSONObject.parseObject(body);

            if (result.getInteger("code") != 1000) {
                log.error("fetch carpage fail, httpstatus:{},body:{}", responseEntity.getStatusCode(), body);
                throw new RuntimeException("fetch carpage fail, httpstatus:" + responseEntity.getStatusCode() + ",body:" + body);
            }
            JSONObject data = result.getJSONObject("data");
            JSONObject pagination = data.getJSONObject("pagination");
            int total = pagination.getInteger("total");
            Page<GrokCarVO> grokCarPage = new Page<>(page, pageSize);

            List<GrokCarVO> gptCars = new ArrayList<>();
            JSONArray list = data.getJSONArray("list");
            if (list == null) {
                return grokCarPage;
            }
            for (Object datum : data.getJSONArray("list")) {
                JSONObject carJSON = (JSONObject) datum;
                Integer status = carJSON.getInteger("status");
                if (status == 1) {
                    GrokCarVO car = carJSON.toJavaObject(GrokCarVO.class);
                    gptCars.add(car);
                }
            }
            grokCarPage.setTotal(total);
            grokCarPage.setRecords(gptCars);
            return grokCarPage;
        } catch (Exception e) {
            log.error("fetch carpage fail", e);
            throw e;
        }
    }

    public List<GptCar> carPage() {
        String url = host + "/carpage";

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("page", 1);
        jsonObject.put("size", 10000);
        jsonObject.put("sort", "desc");
        jsonObject.put("order", "sort");

        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("apiauth", apiauth); // 替换自己的APIAUTH

            // 创建 HttpEntity 对象
            HttpEntity<JSONObject> requestEntity = new HttpEntity<>(jsonObject, headers);

            // 发送 POST 请求
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

            String body = responseEntity.getBody();
            JSONObject result = JSONObject.parseObject(body);

            if (result.getInteger("code") != 1000) {
                log.error("fetch carpage fail, httpstatus:{},body:{}", responseEntity.getStatusCode(), body);
                return new ArrayList<>();
            }
            JSONArray data = result.getJSONArray("data");

            List<GptCar> gptCars = new ArrayList<>();
            for (Object datum : data) {
                JSONObject carJSON = (JSONObject) datum;
                Integer status = carJSON.getInteger("status");
                if (status == 1) {
                    GptCar gptCar = new GptCar();
                    gptCar.setCarID(carJSON.getString("carID"));
                    gptCar.setIsPlus(carJSON.getInteger("isPlus"));
                    gptCars.add(gptCar);
                }
            }
            return gptCars;
        } catch (Exception e) {
            log.error("fetch carpage fail", e);
            return new ArrayList<>();
        }

    }

}
