package com.kedish.xyhelper_fox.config;

import com.kedish.xyhelper_fox.utils.SnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for Snowflake ID Generator
 */
@Configuration
public class SnowflakeConfig {

    @Value("${snowflake.datacenter-id:1}")
    private long datacenterId;

    @Value("${snowflake.worker-id:1}")
    private long workerId;

    /**
     * Create a Snowflake ID Generator bean
     *
     * @return SnowflakeIdGenerator instance
     */
    @Bean
    public SnowflakeIdGenerator snowflakeIdGenerator() {
        return new SnowflakeIdGenerator(datacenterId, workerId);
    }
}
