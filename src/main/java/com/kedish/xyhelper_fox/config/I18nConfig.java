package com.kedish.xyhelper_fox.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;

/**
 * 国际化配置类
 */
@Configuration
public class I18nConfig implements WebMvcConfigurer {

    /**
     * 配置消息源
     */
    @Bean
    public ResourceBundleMessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setBasename("i18n/messages");
        messageSource.setDefaultEncoding("UTF-8");
        messageSource.setUseCodeAsDefaultMessage(true);
        messageSource.setCacheSeconds(3600); // 缓存1小时
        messageSource.setFallbackToSystemLocale(false); // 不使用系统默认语言
        messageSource.setAlwaysUseMessageFormat(false); // 不强制使用MessageFormat
        return messageSource;
    }

    /**
     * 配置语言环境解析器
     * 优先使用Session，如果没有则使用请求头Accept-Language
     */
    @Bean
    public LocaleResolver localeResolver() {
        SessionLocaleResolver localeResolver = new SessionLocaleResolver();
        localeResolver.setDefaultLocale(Locale.SIMPLIFIED_CHINESE); // 默认中文
        return localeResolver;
    }

    /**
     * 配置基于请求头的语言解析器（备用）
     */
    public AcceptHeaderLocaleResolver acceptHeaderLocaleResolver() {
        AcceptHeaderLocaleResolver resolver = new AcceptHeaderLocaleResolver();
        resolver.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        // 设置支持的语言列表
        List<Locale> supportedLocales = Arrays.asList(
            Locale.SIMPLIFIED_CHINESE,
            Locale.ENGLISH
        );
        resolver.setSupportedLocales(supportedLocales);
        return resolver;
    }

    /**
     * 配置语言环境变更拦截器
     */
    @Bean
    public LocaleChangeInterceptor localeChangeInterceptor() {
        LocaleChangeInterceptor interceptor = new LocaleChangeInterceptor();
        interceptor.setParamName("lang"); // 通过 lang 参数切换语言
        interceptor.setIgnoreInvalidLocale(true); // 忽略无效的语言参数
        return interceptor;
    }

    /**
     * 添加拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(localeChangeInterceptor())
                .addPathPatterns("/**") // 对所有路径生效
                .excludePathPatterns("/static/**", "/css/**", "/js/**", "/images/**"); // 排除静态资源
    }

    /**
     * 获取支持的语言列表
     * @return 支持的语言列表
     */
    public static List<Locale> getSupportedLocales() {
        return Arrays.asList(
            Locale.SIMPLIFIED_CHINESE,
            Locale.ENGLISH
        );
    }

    /**
     * 检查语言是否支持
     * @param locale 语言
     * @return 是否支持
     */
    public static boolean isSupportedLocale(Locale locale) {
        return getSupportedLocales().contains(locale);
    }
}
