package com.kedish.xyhelper_fox.config;

import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.utils.I18nUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {


    @ExceptionHandler(Exception.class)
    public FoxResult handleException(Exception e) {
        log.error("全局异常处理", e);
        return FoxResult.fail(e.getMessage());
    }

    @ExceptionHandler(FoxException.class)
    public FoxResult handleFoxException(FoxException e) {
        log.error("全局异常处理", e);
        if (e.getMessageKey() != null) {
            return FoxResult.failWithI18n(e.getMessage<PERSON>ey());
        }
        return FoxResult.fail(e.getMessage());
    }
}
