package com.kedish.xyhelper_fox.config;

import org.redisson.api.RedissonClient;
import org.redisson.Redisson;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedissonConfig {


    @Value("${spring.data.redis.port}")
    private String port;
    @Value("${spring.data.redis.host}")
    private String host;
    @Bean
    public RedissonClient createRedissonClient() {
        // 创建配置对象
        Config config = new Config();

        // 配置单节点Redis地址
        config.useSingleServer()
                .setAddress("redis://"+host+":"+port)  // 设置 Redis 地址
                .setConnectionPoolSize(8)  // 设置连接池大小
                .setSubscriptionConnectionPoolSize(16)  // 设置订阅连接池大小
                .setConnectionMinimumIdleSize(2)  // 设置最小空闲连接数
                .setSubscriptionConnectionMinimumIdleSize(2);  // 设置订阅连接池最小空闲连接数

        // 创建 Redisson 客户端实例
        return Redisson.create(config);
    }
}