package com.kedish.xyhelper_fox.constant;

import lombok.Getter;

@Getter
public enum UserTypeEnum {
    FREE(0, "体验用户"),
    COMMON(1, "普通用户"),
    VIP(2, "普通会员"),
    SVIP(3, "高级会员");
    private Integer code;
    private String desc;

    UserTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static UserTypeEnum getByStringCode(String code) {
        switch (code) {
            case "1":
                return COMMON;
            case "2":
                return VIP;
            case "3":
                return SVIP;
            default:
                return FREE;
        }
    }
}
