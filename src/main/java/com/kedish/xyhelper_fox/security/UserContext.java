package com.kedish.xyhelper_fox.security;

import com.kedish.xyhelper_fox.repo.model.ChatgptUser;

public class UserContext {

    private static final ThreadLocal<ChatgptUser> user = new ThreadLocal<>();

    public static ChatgptUser getUser() {
        return user.get();
    }

    public static void setUser(ChatgptUser user) {
        UserContext.user.set(user);
    }

    public static void remove() {
        user.remove();
    }
}
