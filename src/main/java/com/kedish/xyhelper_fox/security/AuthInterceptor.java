package com.kedish.xyhelper_fox.security;

import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.component.AuthComponent;
import com.kedish.xyhelper_fox.constant.UserTypeEnum;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.service.ChatgptUserService;
import com.kedish.xyhelper_fox.utils.JwtUtils;
import com.kedish.xyhelper_fox.utils.UserUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class AuthInterceptor implements HandlerInterceptor {
    public static final String USER_TOKEN_PREFIX = "login_user:";
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ChatgptUserService chatgptUserService;
    @Resource
    private LocalCache localCache;
    @Resource
    private AuthComponent authComponent;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (request.getMethod().equals("OPTIONS")) {
            return true;
        }
        if (!authComponent.isAuthValid()) {
            throw new FoxException("平台授权已过期，无法访问");
        }
        // 获取请求头中的 Token
        String token = request.getHeader("Authorization");

        // 校验 token 的合法性，可以根据自己的业务逻辑解析 token
        if (token != null) {
            Map<String, String> configMap =
                    localCache.getConfigMap();

            if (Objects.equals("true", configMap.get("canLoginMulti"))) {

                // Token 合法，允许继续处理请求
                try {
                    String username = validateToken(token);
                    ChatgptUser userByUserToken = chatgptUserService.getUserByUserToken(username);
                    if (userByUserToken != null) {
                        UserContext.setUser(userByUserToken);
                        return true;
                    }
                } catch (Exception e) {
                    log.error("token验证失败", e);
                    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                    response.getWriter().write("Unauthorized");
                    return false;
                }
                return true;
            } else {
                RBucket<String> bucket = redissonClient.getBucket(USER_TOKEN_PREFIX + token);
                String username = bucket.get();
                if (username != null) {
                    ChatgptUser userByUserToken = chatgptUserService.getUserByUserToken(username);
                    if (userByUserToken != null) {
                        UserContext.setUser(userByUserToken);
                        return true;
                    }
                }
            }
        } else if (Objects.equals("true", localCache.getConfigMap().get("enableVisitor"))
                && request.getParameter("visitorId") != null && VISITOR_ACCESS_PATHS.contains(request.getRequestURI())) {
            String visitorId = request.getParameter("visitorId");
            if (visitorId.startsWith("visitorId_")) {
                ChatgptUser visitor = UserUtils.getVisitor(visitorId);

                UserContext.setUser(visitor);
                return true;
            }
        }

        // Token 不合法，返回 401 错误
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.getWriter().write("Unauthorized");
        return false;
    }

    private static List<String> VISITOR_ACCESS_PATHS = Arrays.asList("/api/chatGpt/car/selectClaudeCar", "/api/chatGpt/car/selectCar");

    // 假设这里是一个校验 token 的方法，可以根据自己的需求替换
    private String validateToken(String token) {
        // 例如：简单验证 token 是否是 "valid-token"
        return JwtUtils.extractUsername(token);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
        UserContext.remove();
    }
}
