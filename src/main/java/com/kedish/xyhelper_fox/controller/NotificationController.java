package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.model.req.AddNotificationReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.SystemNotificationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
@RequestMapping("/api/notification")
public class NotificationController extends BaseController{

    @Resource
    private SystemNotificationService service;

    @PostMapping("/list")
    public FoxPageResult list(@RequestBody PageQueryReq req) {
        return FoxPageResult.fromPage(service.getPage(req));
//        return FoxPageResult.ok();
    }

    @PostMapping("/addOrUpdate")
    public FoxResult addOrUpdate(@RequestBody AddNotificationReq req) {
        log.info("addNotification req:{}", req);
        checkIsAdmin();
        service.addOrUpdate(req);
        return FoxResult.ok();
    }

    @PostMapping("/delete")
    public FoxResult delete(@RequestParam Long id) {
        log.info("delete req:{}", id);
        checkIsAdmin();
        service.delete(id);
        return FoxResult.ok();
    }

    @GetMapping("/getLatest")
    public FoxResult getLatestNotification(@RequestParam String typeList) {

        return FoxResult.ok(service.getLatestNotification(typeList));
    }

}
