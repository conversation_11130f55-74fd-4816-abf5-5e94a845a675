package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSON;
import com.kedish.xyhelper_fox.model.req.CouponReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.CouponService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/coupon")
@Slf4j
public class CouponController extends BaseController{

    @Resource
    private CouponService couponService;

    @PostMapping("/addOrUpdate")
    public FoxResult addOrUpdate(@RequestBody CouponReq addReq) {
        log.info("coupon addOrUpdate: addReq is {}", JSON.toJSONString(addReq));
        checkIsAdmin();
        couponService.addOrUpdate(addReq);
        return FoxResult.ok();
    }

    @PostMapping("/deleteById")
    public FoxResult deleteById(Long id) {
        log.info("coupon deleteById: id is {}", id);
        checkIsAdmin();
        couponService.deleteById(id);
        return FoxResult.ok();
    }

    @PostMapping("/page")
    public FoxPageResult page(@RequestBody PageQueryReq req) {
        log.info("coupon page: req is {}", JSON.toJSONString(req));
        return FoxPageResult.fromPage(couponService.page(req));
    }


    @GetMapping("/checkCoupon")
    public FoxResult checkAccess(@RequestParam String couponCode, @RequestParam Long salesPlanId) {
        return FoxResult.ok(couponService.checkAccess(couponCode, salesPlanId));
    }
}
