package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kedish.xyhelper_fox.cache.LocalCache;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/pwa")
public class PWAController {


    @Resource
    private LocalCache localCache;

    /**
     * {
     *     "name": "我的Web应用",
     *     "short_name": "应用",
     *     "description": "一个可以安装的简单Web应用",
     *     "start_url": ".",
     *     "display": "standalone",
     *     "background_color": "#ffffff",
     *     "theme_color": "#007bff",
     *     "icons": [
     *         {
     *             "src": "fox.svg",
     *             "sizes": "192x192",
     *             "type": "image/png"
     *         }
     *     ]
     * }
     *
     */
    @GetMapping("/manifest.json")
    public JSONObject manifest() {
        JSONObject manifest = new JSONObject();
        manifest.put("name", localCache.getString("systemName", "Chatgpt"));
        manifest.put("short_name", localCache.getString("systemName", "Chatgpt"));
        manifest.put("description", localCache.getString("systemName", "Chatgpt"));
        manifest.put("start_url", "/");
        manifest.put("display", "standalone");
        manifest.put("background_color", "#ffffff");
        manifest.put("theme_color", "#2196F3");

        JSONArray icons = new JSONArray();
        JSONObject icon = new JSONObject();
        icon.put("src", localCache.getString("systemLogo","fox.svg"));
        icon.put("sizes", "192x192");
        icon.put("type", "image/png");
        icons.add(icon);
        manifest.put("icons", icons);
        return manifest;
    }
}
