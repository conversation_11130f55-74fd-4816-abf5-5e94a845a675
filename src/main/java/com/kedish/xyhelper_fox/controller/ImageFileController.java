package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.model.resp.FoxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 通用图片文件上传下载控制器
 * 基础路径: /api/files
 */
@RestController
@RequestMapping("/api/files")
@Slf4j
public class ImageFileController {

    @Value("${app.file-storage.base-path:/data/upload}")
    private String fileStoragePath;

    /**
     * 图片下载 - 支持任意路径格式
     */
    @GetMapping("/download/**")
    public ResponseEntity<Resource> downloadImage(HttpServletRequest request) {
        try {
            // 获取完整的请求路径
            String requestPath = request.getRequestURI();
            String basePath = "/api/files/download/";
            String relativePath = requestPath.substring(requestPath.indexOf(basePath) + basePath.length());

            // 验证路径安全性
            if (relativePath.contains("..")) {
                throw new RuntimeException("非法访问路径");
            }

            // 构建文件路径并尝试访问
            Path filePath = Paths.get(fileStoragePath, relativePath);
            Resource resource = new UrlResource(filePath.toUri());

            if (!resource.exists() || !resource.isReadable()) {
                throw new RuntimeException("文件不存在或无法读取");
            }

            // 获取文件名
            String fileName = filePath.getFileName().toString();

            // 获取文件类型
            String contentType = Files.probeContentType(filePath);
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            // 获取文件的最后修改时间
            long lastModified = Files.getLastModifiedTime(filePath).toMillis();

            // 获取文件的ETag（基于文件大小和最后修改时间）
            String eTag = String.format("\"%x-%x\"", lastModified, Files.size(filePath));

            // 检查If-None-Match头，实现协商缓存
            String ifNoneMatch = request.getHeader("If-None-Match");
            if (ifNoneMatch != null && ifNoneMatch.equals(eTag)) {
                return ResponseEntity.status(HttpStatus.NOT_MODIFIED)
                        .eTag(eTag)
                        .lastModified(lastModified)
                        .build();
            }

            // 检查If-Modified-Since头，实现协商缓存
            long ifModifiedSince = request.getDateHeader("If-Modified-Since");
            if (ifModifiedSince != -1 && ifModifiedSince >= lastModified) {
                return ResponseEntity.status(HttpStatus.NOT_MODIFIED)
                        .eTag(eTag)
                        .lastModified(lastModified)
                        .build();
            }

            // 设置缓存控制
            CacheControl cacheControl = CacheControl.maxAge(30, TimeUnit.DAYS) // 30天的强缓存
                    .cachePublic()  // 允许CDN等公共缓存
                    .mustRevalidate(); // 过期后必须重新验证

            return ResponseEntity.ok()
                    .cacheControl(cacheControl)
                    .eTag(eTag) // ETag用于协商缓存
                    .lastModified(lastModified) // Last-Modified用于协商缓存
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + fileName + "\"")
                    .body(resource);

        } catch (Exception e) {
            log.error("图片下载失败: path={}", request.getRequestURI(), e);
            throw new RuntimeException("图片下载失败: " + e.getMessage());
        }
    }

    /**
     * 单个图片上传
     */
    @PostMapping("/upload")
    public FoxResult uploadImage(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                return FoxResult.fail("上传文件不能为空");
            }

            // 验证文件类型
            if (!isValidImageType(file)) {
                return FoxResult.fail("不支持的图片格式，仅支持 JPG、PNG、GIF、WebP");
            }

            // 验证文件大小（最大10MB）
            if (file.getSize() > 10 * 1024 * 1024) {
                return FoxResult.fail("文件大小不能超过10MB");
            }

            // 生成文件名
            String fileName = generateFileName(file.getOriginalFilename());

            // 获取日期路径
            String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));

            // 保存文件
            Path filePath = saveFile(file, fileName);

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("fileName", fileName);
            response.put("originalFileName", file.getOriginalFilename());
            response.put("downloadUrl", "/api/files/download/" + datePath + "/" + fileName);
            response.put("fileSize", file.getSize());
            response.put("contentType", file.getContentType());
            response.put("datePath", datePath);

            log.info("图片上传成功: 文件名={}, 路径={}, 大小={}", fileName, datePath, file.getSize());

            return FoxResult.ok(response);

        } catch (IOException e) {
            log.error("图片上传失败", e);
            return FoxResult.fail("图片上传失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("图片上传异常", e);
            return FoxResult.fail("图片上传异常: " + e.getMessage());
        }
    }

    /**
     * 保存文件
     */
    private Path saveFile(MultipartFile file, String fileName) throws IOException {
        // 获取当前日期作为子目录
        String dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));

        // 创建存储目录（包含日期子目录）
        Path uploadDir = Paths.get(fileStoragePath, dateDir);
        if (!Files.exists(uploadDir)) {
            Files.createDirectories(uploadDir);
        }

        // 保存文件
        Path filePath = uploadDir.resolve(fileName);
        Files.copy(file.getInputStream(), filePath);

        return filePath;
    }

    /**
     * 验证是否为有效的图片类型
     *
     * @param file 上传的文件
     * @return 是否为有效图片类型
     */
    private boolean isValidImageType(MultipartFile file) {
        String contentType = file.getContentType();
        if (contentType == null) {
            return false;
        }

        return contentType.equals("image/jpeg") ||
               contentType.equals("image/jpg") ||
               contentType.equals("image/png") ||
               contentType.equals("image/gif") ||
               contentType.equals("image/webp");
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String originalFilename) {
        // 获取文件扩展名
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        // 生成时间戳（使用yyyyMMdd格式开头）+ UUID + 扩展名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);

        return timestamp + "_" + uuid + extension;
    }
}
