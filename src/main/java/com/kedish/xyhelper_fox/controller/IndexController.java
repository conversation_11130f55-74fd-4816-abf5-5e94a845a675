package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.service.ChatGptConversationService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/test")
public class IndexController {

    @Resource
    private ChatGptConversationService chatGptConversationService;

    @GetMapping(value = "/test")
    public String forward() {
        // 转发所有非/api开头的请求到index.html
        chatGptConversationService.scheduleDelete();
        return "forward:/index.html";
    }
}
