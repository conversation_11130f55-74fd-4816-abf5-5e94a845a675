package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.OrderService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/order")
@Slf4j
public class OrderController {

    @Resource
    private OrderService orderService;

    @GetMapping("/queryOrder")
    public FoxResult queryOrder(@RequestParam String outTradeNo) {
        return FoxResult.ok(orderService.queryOrder(outTradeNo));
    }

    @PostMapping("/page")
    public FoxPageResult page(@RequestBody PageQueryReq req) {
        return FoxPageResult.fromPage(orderService.page(req));
    }
}
