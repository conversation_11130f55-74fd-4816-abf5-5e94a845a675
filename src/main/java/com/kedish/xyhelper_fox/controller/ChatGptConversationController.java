package com.kedish.xyhelper_fox.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.repo.model.ChatgptConversation;
import com.kedish.xyhelper_fox.service.ChatGptConversationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/conversation")
@Slf4j
public class ChatGptConversationController extends BaseController{


    @Resource
    private ChatGptConversationService chatGptConversationService;

    @PostMapping("/page")
    public FoxResult page(@RequestBody PageQueryReq req) {

        Page<ChatgptConversation> page = chatGptConversationService.page(req);
        return FoxPageResult.fromPage(page);
    }

    @PostMapping("/deleteByIds")
    public FoxResult deleteByIds(List<Long> ids) {
        checkIsAdmin();
        chatGptConversationService.deleteByIds(ids);
        return FoxResult.ok();
    }
}
