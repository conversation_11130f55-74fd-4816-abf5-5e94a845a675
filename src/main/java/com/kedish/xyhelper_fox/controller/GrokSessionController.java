package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.component.GrokProxy;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.model.req.AddClaudeSessionReq;
import com.kedish.xyhelper_fox.model.req.AddDDDSessionReq;
import com.kedish.xyhelper_fox.model.req.AddGrokSessionReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.*;
import com.kedish.xyhelper_fox.service.ChatGptSessionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
@RequestMapping("/api/lyy/grokSession")
public class GrokSessionController extends BaseController{


    @Resource
    private GrokProxy grokSessionService;

    @PostMapping("/addOrUpdate")
    public FoxResult add(@RequestBody GrokCarVO req) {

        log.info("grokSession addOrUpdate: addReq is {}", JSON.toJSONString(req));
        checkIsAdmin();
        String result;
        if (req.getId() != null) {
            result = grokSessionService.updateGrokSession(req);
        } else {
            result = grokSessionService.addGrokSession(req);
        }
        checkShareServerResult(result);
        return FoxResult.ok();
    }

    private void checkShareServerResult(String result) {
        //{code: 1000, message: "BaseResMessage", data: {Locker: {}}}
        JSONObject jsonObject = JSON.parseObject(result);
        if (jsonObject.getInteger("code") != 1000) {
            throw new FoxException(jsonObject.getString("message"));
        }
    }

    @PostMapping("/page")
    public FoxResult page(@RequestBody PageQueryReq req) {
        log.info("grokSession page: pageReq is {}", JSON.toJSONString(req));
        Page<GrokCarVO> grokCarPage = grokSessionService.page(req.getPageNum(), req.getPageSize());

        return FoxPageResult.fromPage(grokCarPage);
    }

    @PostMapping("/delete")
    public FoxResult delete(@RequestBody List<Long> ids) {
        log.info("grokSession delete: id is {}", JSON.toJSONString(ids));
        checkIsAdmin();
        String s = grokSessionService.deleteGrokSession(ids);
        checkShareServerResult(s);
        return FoxResult.ok();
    }
}
