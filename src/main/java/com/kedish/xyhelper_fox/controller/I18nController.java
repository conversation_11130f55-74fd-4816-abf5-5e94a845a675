package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.I18nService;
import com.kedish.xyhelper_fox.utils.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.LocaleResolver;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 国际化控制器
 */
@RestController
@Slf4j
@RequestMapping("/api/i18n")
public class I18nController {

    @Resource
    private LocaleResolver localeResolver;

    @Resource
    private I18nService i18nService;

    /**
     * 切换语言
     * @param lang 语言代码 (zh, en)
     * @param request HttpServletRequest
     * @param response HttpServletResponse
     * @return 切换结果
     */
    @PostMapping("/changeLanguage")
    public FoxResult changeLanguage(@RequestParam String lang,
                                   HttpServletRequest request,
                                   HttpServletResponse response) {
        try {
            Locale locale;
            switch (lang.toLowerCase()) {
                case "zh":
                case "zh-cn":
                    locale = Locale.SIMPLIFIED_CHINESE;
                    break;
                case "en":
                case "en-us":
                    locale = Locale.ENGLISH;
                    break;
                default:
                    locale = Locale.SIMPLIFIED_CHINESE; // 默认中文
            }

            localeResolver.setLocale(request, response, locale);
            log.info("语言切换成功: {}", locale);
            return FoxResult.ok(I18nUtils.getMessage("common.success"));
        } catch (Exception e) {
            log.error("语言切换失败: {}", e.getMessage());
            return FoxResult.fail(I18nUtils.getMessage("i18n.language.switch.fail"));
        }
    }

    /**
     * 获取当前语言
     * @return 当前语言信息
     */
    @GetMapping("/getCurrentLanguage")
    public FoxResult getCurrentLanguage() {
        Locale currentLocale = I18nUtils.getCurrentLocale();
        Map<String, Object> result = new HashMap<>();
        result.put("locale", currentLocale.toString());
        result.put("language", currentLocale.getLanguage());
        result.put("country", currentLocale.getCountry());
        result.put("displayName", currentLocale.getDisplayName());
        return FoxResult.ok(result);
    }

    /**
     * 获取支持的语言列表
     * @return 支持的语言列表
     */
    @GetMapping("/getSupportedLanguages")
    public FoxResult getSupportedLanguages() {
        try {
            List<Map<String, String>> languages = i18nService.getSupportedLanguageInfo();
            return FoxResult.ok(languages);
        } catch (Exception e) {
            log.error("获取支持语言列表失败", e);
            return FoxResult.fail(I18nUtils.getMessage("common.fail"));
        }
    }

    /**
     * 获取指定语言的消息
     * @param key 消息键
     * @param lang 语言代码（可选）
     * @return 国际化消息
     */
    @GetMapping("/getMessage")
    public FoxResult getMessage(@RequestParam String key,
                               @RequestParam(required = false) String lang) {
        try {
            String message;
            if (lang != null && !lang.isEmpty()) {
                Locale locale;
                switch (lang.toLowerCase()) {
                    case "zh":
                        locale = Locale.SIMPLIFIED_CHINESE;
                        break;
                    case "en":
                        locale = Locale.ENGLISH;
                        break;
                    default:
                        locale = I18nUtils.getCurrentLocale();
                }
                message = I18nUtils.getMessage(key, null, locale);
            } else {
                message = I18nUtils.getMessage(key);
            }

            Map<String, String> result = new HashMap<>();
            result.put("key", key);
            result.put("message", message);
            return FoxResult.ok(result);
        } catch (Exception e) {
            log.error("获取国际化消息失败", e);
            return FoxResult.fail(I18nUtils.getMessage("common.fail"));
        }
    }

    /**
     * 批量获取消息
     * @param keys 消息键列表（逗号分隔）
     * @param lang 语言代码（可选）
     * @return 消息映射
     */
    @GetMapping("/getMessages")
    public FoxResult getMessages(@RequestParam String keys,
                                @RequestParam(required = false) String lang) {
        try {
            List<String> keyList = Arrays.asList(keys.split(","));
            Map<String, String> messages;

            if (lang != null && !lang.isEmpty()) {
                Locale locale = I18nUtils.getLocaleByCode(lang);
                messages = i18nService.getMessages(keyList, locale);
            } else {
                messages = i18nService.getMessages(keyList);
            }

            return FoxResult.ok(messages);
        } catch (Exception e) {
            log.error("批量获取国际化消息失败", e);
            return FoxResult.fail(I18nUtils.getMessage("common.fail"));
        }
    }

    /**
     * 获取所有消息
     * @param lang 语言代码（可选）
     * @return 所有消息
     */
    @GetMapping("/getAllMessages")
    public FoxResult getAllMessages(@RequestParam(required = false) String lang) {
        try {
            Map<String, String> messages;

            if (lang != null && !lang.isEmpty()) {
                Locale locale = I18nUtils.getLocaleByCode(lang);
                messages = i18nService.getAllMessages(locale);
            } else {
                messages = i18nService.getAllMessages();
            }

            return FoxResult.ok(messages);
        } catch (Exception e) {
            log.error("获取所有国际化消息失败", e);
            return FoxResult.fail(I18nUtils.getMessage("common.fail"));
        }
    }

    /**
     * 验证语言代码
     * @param lang 语言代码
     * @return 验证结果
     */
    @GetMapping("/validateLanguage")
    public FoxResult validateLanguage(@RequestParam String lang) {
        try {
            boolean isValid = i18nService.isValidLanguageCode(lang);
            Map<String, Object> result = new HashMap<>();
            result.put("lang", lang);
            result.put("valid", isValid);
            result.put("displayName", isValid ? i18nService.getLanguageDisplayName(lang) : null);

            return FoxResult.ok(result);
        } catch (Exception e) {
            log.error("验证语言代码失败", e);
            return FoxResult.fail(I18nUtils.getMessage("common.fail"));
        }
    }
}
