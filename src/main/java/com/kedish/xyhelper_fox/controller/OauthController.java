package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSONObject;
import com.kedish.xyhelper_fox.model.req.GrokOauthReq;
import com.kedish.xyhelper_fox.model.req.OauthReq;
import com.kedish.xyhelper_fox.model.resp.Result;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.service.ChatgptUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

@RestController
@Slf4j
@RequestMapping("/api/chatShareServer")
public class OauthController {

    @Resource
    private ChatgptUserService chatgptUserService;

    @PostMapping("/oauth")
    public Result doOauth(@RequestBody OauthReq req) {
        if (!StringUtils.hasLength(req.getUserToken())) {
            return Result.fail("参数错误");
        }
        if (req.getUserToken().startsWith("visitorId_")) {
            return Result.ok();
        }

        ChatgptUser userByUserToken = chatgptUserService.getUserByUserToken(req.getUserToken());
        log.info("userByUserToken:{}", userByUserToken);
        if (userByUserToken == null) {
            return Result.fail("用户不存在");
        }
        return Result.ok();
    }

    @PostMapping("/claude/oauth")
    public Result doClaudeAuth(@RequestParam String usertoken, @RequestParam String carid) {
        log.info("claude oauth usertoken:{},carid: {}", usertoken, carid);
        if (!StringUtils.hasLength(usertoken)) {
            return Result.fail("参数错误");
        }
        if (usertoken.startsWith("visitorId_")) {
            return Result.ok();
        }

        ChatgptUser userByUserToken = chatgptUserService.getUserByUserToken(usertoken);
        log.info("userByUserToken:{}", userByUserToken);
        if (userByUserToken == null) {
            return Result.fail("用户不存在");
        }
        //当前时间增加7天
        LocalDateTime expire = LocalDateTime.now().plusDays(7);
        //yyyy-MM-dd HH:mm:ss
        String expireTime = expire.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        return Result.ok(expireTime);
    }

    @PostMapping("/grok/oauth")
    public Result doGrokOauth(@RequestParam String usertoken, @RequestParam(required = false) String carid) {
        log.info("grok oauth usertoken:{},carid:{}", usertoken, carid);
        if (!StringUtils.hasLength(usertoken)) {
            return Result.fail("参数错误");
        }
        if (usertoken.startsWith("visitorId_")) {
            return Result.ok();
        }

        ChatgptUser userByUserToken = chatgptUserService.getUserByUserToken(usertoken);
        log.info("userByUserToken:{}", userByUserToken);
        if (userByUserToken == null) {
            return Result.fail("用户不存在");
        }
        //当前时间增加7天
        LocalDateTime expire = LocalDateTime.now().plusDays(7);
        //yyyy-MM-dd HH:mm:ss
        String expireTime = expire.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        return Result.ok(expireTime);
    }
}
