package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.component.AuthComponent;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Resource
    private AuthComponent authComponent;

    @GetMapping("/status")
    public FoxResult getAuthStatus() {
        return FoxResult.ok(Map.of("expireTime", authComponent.getAuthExpireTime(),
                "ver", authComponent.getSystemVer()));
    }

    @GetMapping("/refresh")
    public FoxResult refreshAuth() {
        authComponent.refreshAuth();
        return getAuthStatus();
    }
}
