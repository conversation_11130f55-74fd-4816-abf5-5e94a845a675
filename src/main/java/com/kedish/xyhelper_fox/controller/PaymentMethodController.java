package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSON;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.req.PaymentMethodReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.PaymentMethodService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
@RequestMapping("/api/paymentMethod")
public class PaymentMethodController extends BaseController{

    @Resource
    private PaymentMethodService paymentMethodService;

    @PostMapping("/addOrUpdate")
    public FoxResult addOrUpdate(@RequestBody PaymentMethodReq req) {
        log.info("addOrUpdate req:{}", JSON.toJSONString(req));
        checkIsAdmin();
        paymentMethodService.addOrUpdate(req);
        return FoxResult.ok();
    }


    @PostMapping("/deleteById")
    public FoxResult delete(@RequestParam Long id) {
        log.info("delete id:{}", id);
        checkIsAdmin();
        paymentMethodService.delete(id);
        return FoxResult.ok();
    }

    @PostMapping("/page")
    public FoxPageResult page(@RequestBody PageQueryReq req) {
        log.info("page req:{}", JSON.toJSONString(req));

        return FoxPageResult.fromPage(paymentMethodService.page(req));
    }

    @GetMapping("/queryAll")
    public FoxResult queryAll() {
        return FoxResult.ok(paymentMethodService.queryAll());
    }
}
