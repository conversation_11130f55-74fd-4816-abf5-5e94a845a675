package com.kedish.xyhelper_fox.controller;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.component.ChatGptSessionAccessComponent;
import com.kedish.xyhelper_fox.component.UserLimitBucketComponent;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import io.github.bucket4j.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import static com.kedish.xyhelper_fox.utils.HttpServletRequestUtils.getGfSessionIdFromCookie;

@RestController
@Slf4j
@RequestMapping("/api")
public class AuditLimitController {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private LocalCache localCache;

    @Resource
    private UserLimitBucketComponent userLimitBucketComponent;

    @Resource
    private ChatGptSessionAccessComponent chatGptSessionAccessComponent;

    @GetMapping("/testSetSession")
    public FoxResult testSetSession() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("carid", "123");
        jsonObject.put("usertoken", "admin");
        RBucket<Object> bucket = redissonClient.getBucket("gfsession:123", StringCodec.INSTANCE);
        bucket.set(jsonObject.toString());
        bucket.expire(Duration.ofMinutes(5));

        jsonObject = new JSONObject();
        jsonObject.put("carid", "456");
        jsonObject.put("usertoken", "admin");
        bucket = redissonClient.getBucket("gfsession:456", StringCodec.INSTANCE);
        bucket.set(jsonObject.toString());
        bucket.expire(Duration.ofMinutes(5));
        return FoxResult.ok();
    }

    @GetMapping("/checkCarAccess")
    public FoxResult checkCarAccess() {
        try {
            Map<String, Integer> accessCountMap =
                    chatGptSessionAccessComponent.getAccessCountMap(Arrays.asList("123", "456"));

            log.info(JSON.toJSONString(accessCountMap));
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return FoxResult.ok();
    }

    @GetMapping("/testUserActive")
    public FoxResult testUserActive() {
        // 测试记录用户活跃状态
        chatGptSessionAccessComponent.recordUserActive("testUser1");
        chatGptSessionAccessComponent.recordUserActive("testUser2");
        chatGptSessionAccessComponent.recordUserActive("visitorId_123"); // 这个应该被跳过

        // 获取活跃用户数量
        long activeUserCount = chatGptSessionAccessComponent.getActiveUserCount();
        long onlineUserCount = chatGptSessionAccessComponent.getOnlineUserCount();

        log.info("活跃用户数量: {}, 在线用户数量: {}", activeUserCount, onlineUserCount);

        Map<String, Object> result = new HashMap<>();
        result.put("activeUserCount", activeUserCount);
        result.put("onlineUserCount", onlineUserCount);

        return FoxResult.ok(result);
    }

    @GetMapping("/testConversation")
    public FoxResult testConversation() {
        // 测试对话方法，应该会自动记录用户活跃状态
        chatGptSessionAccessComponent.conversation("testCarId", "testUser3", "gpt-4");

        // 等待一下让异步任务执行
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        long activeUserCount = chatGptSessionAccessComponent.getActiveUserCount();
        log.info("对话后活跃用户数量: {}", activeUserCount);

        return FoxResult.ok(Map.of("activeUserCount", activeUserCount));
    }

    @PostMapping("/auditLimit")
    public void auditLimit(@RequestBody JSONObject json, HttpServletRequest httpServletRequest,
                           HttpServletResponse httpServletResponse) throws IOException {
        String authorization = httpServletRequest.getHeader("Authorization");
        String userToken = authorization.replace("Bearer ", "");
//        log.info("auditLimit,json is {}", json.toJSONString());
        // 解析 JSON 字符串

        // 提取 "model" 字段
        String model = json.getString("model");
        String prompt = "";

        if (json.containsKey("messages")) {

            // 提取 "parts" 字段
            prompt = json.getJSONArray("messages")
                    .getJSONObject(0)
                    .getJSONObject("content")
                    .getJSONArray("parts")
                    .getString(0);
            String forbiddenWord = checkForbiddenWord(prompt);
            if (forbiddenWord != null) {
                sendBadRequest(httpServletResponse, String.format("请珍惜账号，不要提问违禁内容【%s】。" +
                        "如果您对违禁内容有疑问，请联系管理员", forbiddenWord));
                return;
            }
        }

        String gfsessionid = getGfSessionIdFromCookie(httpServletRequest);
        RBucket<Object> bucket = redissonClient.getBucket("gfsession:" + gfsessionid, StringCodec.INSTANCE);
        String session = bucket.get().toString();
        JSONObject jsonObject = JSONObject.parseObject(session);
        String usertokenFromSession = jsonObject.getString("usertoken");
        String carid = jsonObject.getString("carid");
        log.info("auditLimit,userToken: {},prompt: {},model: {}, usertokenFromSession:{},carid:{},gfsessionid:{}",
                userToken, prompt, model, usertokenFromSession, carid, gfsessionid);

        UserLimitBucketComponent.RateLimit rateLimit = userLimitBucketComponent.getRateLimit(usertokenFromSession, model);
        if (rateLimit == null) {
            sendBadRequest(httpServletResponse, String.format("您没有访问该模型【%s】的权限", model));
            return;
        }
        VerboseBucket verboseBucket;
        verboseBucket = userLimitBucketComponent.getBucket(usertokenFromSession, rateLimit);


        VerboseResult<ConsumptionProbe> cpr =
                verboseBucket.tryConsumeAndReturnRemaining(rateLimit.getMultiplier());
        BucketConfiguration configuration = cpr.getConfiguration();
        long capacity = Arrays.stream(configuration.getBandwidths())
                .mapToLong(Bandwidth::getCapacity)
                .max().getAsLong();
        VerboseResult.Diagnostics diagnostics = cpr.getDiagnostics();
        long availableTokens = diagnostics.getAvailableTokens();
        long refillingTime = TimeUnit.NANOSECONDS.toSeconds(diagnostics.calculateFullRefillingTime());
        ConsumptionProbe consumptionProbe = cpr.getValue();

        log.info("userToken: {}, capacity: {}, availableTokens: {}, refillingTime: {}, isConsumed: {}",
                usertokenFromSession, capacity, availableTokens, refillingTime, consumptionProbe.isConsumed());
        if (!consumptionProbe.isConsumed()) {
            sendBadRequest(httpServletResponse, String.format("您已达到自身权益的对话速率限制，请等待%s后再试。" +
                    "购买会员可以解锁更高速率", formatTime(refillingTime)));
        } else {
            //记录车队访问
            chatGptSessionAccessComponent.conversation(carid, userToken, model);
            sendSuccess(httpServletResponse);
        }


    }

    @PostMapping("/claude/auditLimit")
    public void claudeAuditLimit(@RequestBody JSONObject json, HttpServletRequest httpServletRequest,
                                 HttpServletResponse httpServletResponse) throws IOException {
        String authorization = httpServletRequest.getHeader("Authorization");
        String userToken = authorization.replace("Bearer ", "");
        log.info("claudeAuditLimit,json is {}", json.toJSONString());
        // 提取 "model" 字段，按照Go代码逻辑处理
        String model = json.getString("model");
        // 判断是否为Claude模型
        // Claude模型限速逻辑 - 根据API请求参数解析
        if (model == null || model.contains("claude-sonnet-4")) {
            model = "claude-4-sonnet";
        } else if (model.contains("claude-opus-4-1")) {
            model = "claude-4-1-opus";
        } else if (model.contains("claude-opus-4")) {
            model = "claude-4-opus";
        } else if (model.contains("claude-3-7-sonnet")) {
            model = "claude-3-7-sonnet";
        } else if (model.contains("claude-3-opus")) {
            model = "claude-3-opus";
        } else if (model.contains("claude-3-5-haiku")) {
            model = "claude-3-5-haiku";
        } else if (model.contains("claude-3-5-sonnet")) {
            // 保持原有的Claude 3.5 Sonnet支持
            model = "claude-3-5-sonnet";
        } else if (model.contains("claude-3-haiku")) {
            // 保持原有的Claude 3 Haiku支持
            model = "claude-3-haiku";
        } else {
            // 默认为Claude 4 Sonnet
            model = "claude-4-sonnet";
        }

        // Claude API使用"prompt"字段，不是"messages"
        String prompt = json.getString("prompt");
        if (prompt != null) {
            String forbiddenWord = checkForbiddenWord(prompt);
            if (forbiddenWord != null) {
                sendClaudeBadRequest(httpServletResponse, String.format("请珍惜账号，不要提问违禁内容【%s】。" +
                        "如果您对违禁内容有疑问，请联系管理员", forbiddenWord));
                return;
            }
        }

        String gfsessionid = getGfSessionIdFromCookie(httpServletRequest);
        String usertokenFromSession = userToken;
        String carid = "";
//        RBucket<Object> bucket = redissonClient.getBucket("gfsession:" + gfsessionid, StringCodec.INSTANCE);
//        String session = bucket.get().toString();
//        JSONObject jsonObject = JSONObject.parseObject(session);
//        String usertokenFromSession = jsonObject.getString("usertoken");
//        String carid = jsonObject.getString("carid");
        log.info("claudeAuditLimit,userToken: {},prompt: {},model: {}, usertokenFromSession:{},carid:{},gfsessionid:{}",
                userToken, prompt, model, usertokenFromSession, carid, gfsessionid);

        String claudeModel = model;
        UserLimitBucketComponent.RateLimit rateLimit = userLimitBucketComponent.getRateLimit(usertokenFromSession, claudeModel);
        if (rateLimit == null) {
            sendClaudeBadRequest(httpServletResponse, String.format("您没有访问该模型【%s】的权限", model));
            return;
        }
        VerboseBucket verboseBucket;
        verboseBucket = userLimitBucketComponent.getBucket(usertokenFromSession, rateLimit);

        VerboseResult<ConsumptionProbe> cpr =
                verboseBucket.tryConsumeAndReturnRemaining(rateLimit.getMultiplier());
        BucketConfiguration configuration = cpr.getConfiguration();
        long capacity = Arrays.stream(configuration.getBandwidths())
                .mapToLong(Bandwidth::getCapacity)
                .max().getAsLong();
        VerboseResult.Diagnostics diagnostics = cpr.getDiagnostics();
        long availableTokens = diagnostics.getAvailableTokens();
        long refillingTime = TimeUnit.NANOSECONDS.toSeconds(diagnostics.calculateFullRefillingTime());
        ConsumptionProbe consumptionProbe = cpr.getValue();

        log.info("claudeAuditLimit: userToken: {}, capacity: {}, availableTokens: {}, refillingTime: {}, isConsumed: {}",
                usertokenFromSession, capacity, availableTokens, refillingTime, consumptionProbe.isConsumed());
        if (!consumptionProbe.isConsumed()) {
            sendClaudeRateLimitError(httpServletResponse, String.format("您已达到自身权益的对话速率限制，请等待%s后再试。" +
                    "购买会员可以解锁更高速率", formatTime(refillingTime)));
        } else {
            //记录车队访问
//            chatGptSessionAccessComponent.conversation(carid);
            chatGptSessionAccessComponent.conversation(carid, userToken, model);
            sendSuccess(httpServletResponse);
        }
    }

    @PostMapping("/grok/auditLimit")
    public void grokAuditLimit(@RequestBody JSONObject json, HttpServletRequest httpServletRequest,
                               HttpServletResponse httpServletResponse) throws IOException {
        String authorization = httpServletRequest.getHeader("Authorization");
        String userToken = authorization.replace("Bearer ", "");
        log.info("grokAuditLimit,json is {}", json.toJSONString());
        // Grok API请求参数解析，按照Go代码逻辑
        String modelName = json.getString("modelName");


        String prompt = json.getString("message");
        if (prompt != null) {
            String forbiddenWord = checkForbiddenWord(prompt);
            if (forbiddenWord != null) {
                sendGrokBadRequest(httpServletResponse, String.format("请珍惜账号，不要提问违禁内容【%s】。" +
                        "如果您对违禁内容有疑问，请联系管理员", forbiddenWord));
                return;
            }
        }

        String gfsessionid = getGfSessionIdFromCookie(httpServletRequest);
        String carid = "";
        String usertokenFromSession = userToken;
//        RBucket<Object> bucket = redissonClient.getBucket("gfsession:" + gfsessionid, StringCodec.INSTANCE);
//        String session = bucket.get().toString();
//        JSONObject jsonObject = JSONObject.parseObject(session);
//        String usertokenFromSession = jsonObject.getString("usertoken");
//        String carid = jsonObject.getString("carid");
        log.info("grokAuditLimit,userToken: {},prompt: {},model: {}, usertokenFromSession:{},carid:{},gfsessionid:{}",
                userToken, prompt, modelName, usertokenFromSession, carid, gfsessionid);

        // 为Grok模型添加前缀，以区分不同系统的模型
        String grokModel = modelName;
        UserLimitBucketComponent.RateLimit rateLimit = userLimitBucketComponent.getRateLimit(usertokenFromSession, grokModel);
        if (rateLimit == null) {
            sendGrokBadRequest(httpServletResponse, String.format("您没有访问该模型【%s】的权限", modelName));
            return;
        }
        VerboseBucket verboseBucket;
        verboseBucket = userLimitBucketComponent.getBucket(usertokenFromSession, rateLimit);

        VerboseResult<ConsumptionProbe> cpr =
                verboseBucket.tryConsumeAndReturnRemaining(rateLimit.getMultiplier());
        BucketConfiguration configuration = cpr.getConfiguration();
        long capacity = Arrays.stream(configuration.getBandwidths())
                .mapToLong(Bandwidth::getCapacity)
                .max().getAsLong();
        VerboseResult.Diagnostics diagnostics = cpr.getDiagnostics();
        long availableTokens = diagnostics.getAvailableTokens();
        long refillingTime = TimeUnit.NANOSECONDS.toSeconds(diagnostics.calculateFullRefillingTime());
        ConsumptionProbe consumptionProbe = cpr.getValue();

        log.info("grokAuditLimit, userToken: {}, capacity: {}, availableTokens: {}, refillingTime: {}, isConsumed: {}",
                usertokenFromSession, capacity, availableTokens, refillingTime, consumptionProbe.isConsumed());
        if (!consumptionProbe.isConsumed()) {
            sendGrokRateLimitError(httpServletResponse, String.format("您已达到自身权益的对话速率限制，请等待%s后再试。" +
                    "购买会员可以解锁更高速率", formatTime(refillingTime)));
        } else {
            //记录车队访问
            chatGptSessionAccessComponent.conversation(carid, userToken, modelName);
            sendSuccess(httpServletResponse);
        }
    }

    private void sendBadRequest(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(String.format("{\"error\":\"%s\"}", message));
        response.getWriter().flush();
    }

    private void sendSuccess(HttpServletResponse response) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write("{\"code\":0}");
        response.getWriter().flush();
    }

    // Claude特定的错误响应格式
    private void sendClaudeBadRequest(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(String.format("{\"type\":\"error\",\"error\":{\"type\":\"blocked content\",\"message\":\"%s\"}}", message));
        response.getWriter().flush();
    }

    private void sendClaudeRateLimitError(HttpServletResponse response, String message) throws IOException {
        response.setStatus(429);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(String.format("{\"type\":\"error\",\"error\":{\"type\":\"rate limit exceeded\",\"message\":\"%s\"}}", message));
        response.getWriter().flush();
    }

    // Grok特定的错误响应格式
    private void sendGrokBadRequest(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(String.format("{\"error\":{\"code\":13,\"message\":\"Don't ask for forbidden content.\",\"detail\":[\"%s\"]}}", message));
        response.getWriter().flush();
    }

    private void sendGrokRateLimitError(HttpServletResponse response, String message) throws IOException {
        response.setStatus(429);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(String.format("{\"error\":{\"code\":13,\"message\":\"rate limit exceeded\",\"detail\":[\"%s\"]}}", message));
        response.getWriter().flush();
    }

    public static String formatTime(long seconds) {
        if (seconds == 0) {
            return "0秒";
        }

        long days = seconds / (24 * 3600);
        seconds %= (24 * 3600);
        long hours = seconds / 3600;
        seconds %= 3600;
        long minutes = seconds / 60;
        seconds %= 60;

        StringBuilder result = new StringBuilder();
        if (days > 0) {
            result.append(days).append("天");
        }
        if (hours > 0) {
            result.append(hours).append("小时");
        }
        if (minutes > 0) {
            result.append(minutes).append("分钟");
        }
        if (seconds > 0) {
            result.append(seconds).append("秒");
        }

        return result.toString().trim(); // 去除末尾多余的空格
    }

    private String checkForbiddenWord(String prompt) {
        List<String> forbiddenWords = localCache.getForbiddenWords();
        if (forbiddenWords.isEmpty()) {
            return null;
        }
        for (String forbiddenWord : forbiddenWords) {
            if (prompt.contains(forbiddenWord)) {
                return forbiddenWord;
            }
        }
        return null;
    }


}
