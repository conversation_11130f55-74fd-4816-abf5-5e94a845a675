package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.model.req.AddActivationCodeReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.ActivationCodeService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/activationCode")
@Slf4j
public class ActivationCodeController extends BaseController{

    @Resource
    private ActivationCodeService activationCodeService;

    @PostMapping("/page")
    public FoxPageResult page(@RequestBody PageQueryReq req) {
        return FoxPageResult.fromPage(activationCodeService.page(req));
    }

    @PostMapping("/exchange")
    public FoxResult exchange(String activationCode) {
        activationCodeService.exchange(activationCode);
        return FoxResult.ok();
    }

    @PostMapping("/batchAdd")
    public FoxResult batchAdd(@RequestBody AddActivationCodeReq req) {
        log.info("batchAdd req: {}", req);
        checkIsAdmin();
        String result = activationCodeService.batchAdd(req);
        return FoxResult.ok(result);
    }

    @PostMapping("/deleteByIds")
    public FoxResult deleteByIds(@RequestBody List<Long> ids) {
        checkIsAdmin();
        activationCodeService.deleteByIds(ids);
        return FoxResult.ok();
    }
}
