package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.repo.model.InvitationRecord;
import com.kedish.xyhelper_fox.service.InvitationRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/invitationRecord")
@Slf4j
public class InvitationRecordController extends BaseController {

    @Resource
    private InvitationRecordService invitationRecordService;

    /**
     * 分页查询邀请记录
     * @param req 分页查询请求
     * @return 分页结果
     */
    @PostMapping("/page")
    public FoxPageResult page(@RequestBody PageQueryReq req) {
        log.info("invitationRecord page: pageReq is {}", JSON.toJSONString(req));
        checkIsAdmin(); // 需要管理员权限
        Page<InvitationRecord> page = invitationRecordService.page(req);
        return FoxPageResult.fromPage(page);
    }

    /**
     * 根据ID获取邀请记录详情
     * @param id 记录ID
     * @return 邀请记录详情
     */
    @GetMapping("/detail/{id}")
    public FoxResult getDetail(@PathVariable Long id) {
        log.info("invitationRecord getDetail: id is {}", id);
        checkIsAdmin(); // 需要管理员权限
        InvitationRecord record = invitationRecordService.getById(id);
        return FoxResult.ok(record);
    }

    /**
     * 根据邀请人名称查询邀请记录
     * @param inviterName 邀请人名称
     * @return 邀请记录列表
     */
    @GetMapping("/byInviter")
    public FoxResult getByInviterName(@RequestParam String inviterName) {
        log.info("invitationRecord getByInviterName: inviterName is {}", inviterName);
        checkIsAdmin(); // 需要管理员权限
        List<InvitationRecord> records = invitationRecordService.getByInviterName(inviterName);
        return FoxResult.ok(records);
    }

    /**
     * 根据被邀请人名称查询邀请记录
     * @param inviteeName 被邀请人名称
     * @return 邀请记录列表
     */
    @GetMapping("/byInvitee")
    public FoxResult getByInviteeName(@RequestParam String inviteeName) {
        log.info("invitationRecord getByInviteeName: inviteeName is {}", inviteeName);
        checkIsAdmin(); // 需要管理员权限
        List<InvitationRecord> records = invitationRecordService.getByInviteeName(inviteeName);
        return FoxResult.ok(records);
    }

    /**
     * 统计邀请人的邀请数量
     * @param inviterName 邀请人名称
     * @return 邀请数量
     */
    @GetMapping("/count")
    public FoxResult countByInviterName(@RequestParam String inviterName) {
        log.info("invitationRecord countByInviterName: inviterName is {}", inviterName);
        checkIsAdmin(); // 需要管理员权限
        Long count = invitationRecordService.countByInviterName(inviterName);
        return FoxResult.ok(count);
    }
}
