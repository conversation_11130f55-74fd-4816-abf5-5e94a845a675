package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.ForbiddenWordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@Slf4j
@RequestMapping("/api/forbiddenWord")
public class ForbiddenWordController extends BaseController{

    @Resource
    private ForbiddenWordService forbiddenWordService;

    @PostMapping("/batchAdd")
    public FoxResult batchAdd(@RequestBody String text) {
        checkIsAdmin();
        // 将输入的文本按行分割成列表
        List<String> wordsList = new ArrayList<>(List.of(text.split("\n")));

        // 调用服务方法批量添加违禁词
        forbiddenWordService.batchAdd(wordsList);
        return FoxResult.ok();
    }

    @PostMapping("/page")
    public FoxPageResult page(@RequestBody PageQueryReq req) {

        return FoxPageResult.fromPage(forbiddenWordService.page(req));
    }

    @PostMapping("/deleteByIds")
    public FoxResult deleteById(@RequestBody List<Long> ids) {
        checkIsAdmin();
        forbiddenWordService.deleteById(ids);
        return FoxResult.ok();
    }


}
