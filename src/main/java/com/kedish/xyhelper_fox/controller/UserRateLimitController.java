package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSON;
import com.kedish.xyhelper_fox.component.UserLimitBucketComponent;
import com.kedish.xyhelper_fox.model.req.AddUserGroupReq;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.model.resp.RateLimitVO;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.repo.model.UserRateLimit;
import com.kedish.xyhelper_fox.service.ChatgptUserService;
import com.kedish.xyhelper_fox.service.UserGroupService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/userRateLimit")
public class UserRateLimitController extends BaseController{

    @Resource
    private UserGroupService userGroupService;

    @Resource
    private ChatgptUserService chatgptUserService;

    @Resource
    private UserLimitBucketComponent userLimitBucketComponent;

    /**
     * Get rate limits for a specific user
     *
     * @param userToken The user token to get rate limits for
     * @return User rate limits
     */
    @GetMapping("/detail")
    public FoxResult getUserRateLimits(@RequestParam String userToken) {
        ChatgptUser user = chatgptUserService.getUserByUserToken(userToken);
        if (user == null) {
            return FoxResult.fail("User not found");
        }

        List<UserRateLimit> userRateLimits = userGroupService.getUserRateLimit(userToken);
        List<RateLimitVO> rateLimitVOs = userRateLimits.stream().map(limit -> {
            RateLimitVO vo = new RateLimitVO();
            vo.setModel(limit.getModel());
            vo.setLimit(limit.getRate() == null ? null : limit.getRate().longValue());
            vo.setPer(limit.getPeriod());
            vo.setMultiplier(limit.getMultiplier());
            vo.setSource("user"); // Indicate this is from user-specific limits
            return vo;
        }).collect(Collectors.toList());

        return FoxResult.ok(Map.of("userToken", userToken, "rateLimits", rateLimitVOs));
    }

    /**
     * Update rate limits for a specific user
     *
     * @param userToken The user token to update rate limits for
     * @param rateLimits List of rate limit items to apply
     * @return Success or failure result
     */
    @PostMapping("/update")
    public FoxResult updateUserRateLimits(@RequestParam String userToken,
                                         @RequestBody List<AddUserGroupReq.RateLimitItem> rateLimits) {
        log.info("Updating rate limits for user {}: {}", userToken, JSON.toJSONString(rateLimits));

        checkIsAdmin();
        ChatgptUser user = chatgptUserService.getUserByUserToken(userToken);
        if (user == null) {
            return FoxResult.fail("User not found");
        }

        try {
            userGroupService.updateUserRateLimits(userToken, rateLimits);

            // Reset the user's bucket to apply the new rate limits
            userLimitBucketComponent.resetBucket(user);

            return FoxResult.ok();
        } catch (Exception e) {
            log.error("Error updating user rate limits", e);
            return FoxResult.fail(e.getMessage());
        }
    }

    /**
     * Delete all rate limits for a specific user
     *
     * @param userToken The user token to delete rate limits for
     * @return Success or failure result
     */
    @DeleteMapping("/delete")
    public FoxResult deleteUserRateLimits(@RequestParam String userToken) {
        log.info("Deleting all rate limits for user {}", userToken);

        checkIsAdmin();
        ChatgptUser user = chatgptUserService.getUserByUserToken(userToken);
        if (user == null) {
            return FoxResult.fail("User not found");
        }

        try {
            userGroupService.updateUserRateLimits(userToken, null);

            // Reset the user's bucket to apply the group rate limits
            userLimitBucketComponent.resetBucket(user);

            return FoxResult.ok();
        } catch (Exception e) {
            log.error("Error deleting user rate limits", e);
            return FoxResult.fail(e.getMessage());
        }
    }
}
