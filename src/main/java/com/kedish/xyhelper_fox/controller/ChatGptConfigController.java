package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.security.UserContext;
import com.kedish.xyhelper_fox.service.ChatGptConfigService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/api/config")
public class ChatGptConfigController extends BaseController{

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @Resource
    private LocalCache localCache;
    @PostMapping("/get")
    public FoxResult getConfigs(@RequestBody List<String> keys) {

        if (CollectionUtils.isEmpty(keys)) {
            return FoxResult.failWithI18n("common.param.error");
        }
        Map<String, String> configMap = localCache.getConfigMap();
        Map<String,String> result = new HashMap<>();

        for (String key : keys) {
            result.put(key, configMap.get(key));
        }
        return FoxResult.ok(result);

    }

    @PostMapping("/addOrUpdate")
    public FoxResult addOrUpdate(@RequestBody Map<String, String> configMaps) {
        checkIsAdmin();
        log.info("configMaps:{}", configMaps);
        chatGptConfigService.addOrUpdate(configMaps);
        return FoxResult.ok();
    }
}
