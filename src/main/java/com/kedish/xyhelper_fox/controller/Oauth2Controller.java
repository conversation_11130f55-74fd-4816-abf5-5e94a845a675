package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.model.resp.FoxR<PERSON>ult;
import com.kedish.xyhelper_fox.oauth2.GithubUser;
import com.kedish.xyhelper_fox.oauth2.GoogleUser;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.repo.model.Oauth2Client;
import com.kedish.xyhelper_fox.service.ChatgptUserService;
import com.kedish.xyhelper_fox.service.GithubService;
import com.kedish.xyhelper_fox.service.GoogleService;
import com.kedish.xyhelper_fox.service.Oauth2ClientService;
import com.kedish.xyhelper_fox.utils.JwtUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Objects;

import static com.kedish.xyhelper_fox.security.AuthInterceptor.USER_TOKEN_PREFIX;

@RestController
@RequestMapping("/api/oauth")
@Slf4j
public class Oauth2Controller {

    @Resource
    private GithubService githubService;

    @Resource
    private Oauth2ClientService oauth2ClientService;

    @Resource
    private LocalCache localCache;

    @Resource
    private ChatgptUserService chatgptUserService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private GoogleService googleService;

    @GetMapping("/config")
    public FoxResult getConfig(@RequestParam String type) {
        try {
            Oauth2Client github = oauth2ClientService.getByType(type);
            if (github == null) {
                return FoxResult.fail("GitHub OAuth2配置未找到");
            }

            // 只返回前端需要的公开配置信息
            java.util.Map<String, String> config = new java.util.HashMap<>();
            config.put("clientId", github.getClientId());
            config.put("redirectUri", github.getRedirectUri());
            config.put("scope", github.getScope());

            return FoxResult.ok(config);
        } catch (Exception e) {
            return FoxResult.fail("获取GitHub配置失败: " + e.getMessage());
        }
    }

    @GetMapping("/github/login")
    public FoxResult githubLogin(@RequestParam String code,
                                @RequestParam(required = false) String state) {
        try {
            // 参数验证
            if (code == null || code.trim().isEmpty()) {
                return FoxResult.fail("授权码不能为空");
            }

            Oauth2Client github = oauth2ClientService.getByType("github");
            if (github == null) {
                return FoxResult.fail("GitHub OAuth2配置未找到");
            }

            // 1. 使用授权码获取访问令牌
            String accessToken = githubService.getAccessToken(code, github);
            if (accessToken == null || accessToken.trim().isEmpty()) {
                return FoxResult.fail("获取访问令牌失败");
            }

            // 2. 使用访问令牌获取用户信息
            GithubUser user = githubService.getUserInfo(accessToken);
            if (user == null || user.getEmail() == null) {
                return FoxResult.fail("获取GitHub用户信息失败，请确保您的GitHub账户设置了公开邮箱");
            }

            // 3. 登录或注册用户
            ChatgptUser login = chatgptUserService.githubLogin(user);
            if (login == null) {
                return FoxResult.fail("用户登录失败");
            }

            // 4. 生成JWT令牌
            String token = JwtUtils.generateToken(login.getUserToken());

            // 5. 处理多端登录限制
            if (!Objects.equals("true", localCache.getConfigMap().get("canLoginMulti"))) {
                RBucket<String> bucket = redissonClient.getBucket(USER_TOKEN_PREFIX + token);
                bucket.set(login.getUserToken(), 86400L * 7, java.util.concurrent.TimeUnit.SECONDS);
            }

            // 6. 返回令牌
            return FoxResult.ok(token);
        } catch (IOException e) {
            return FoxResult.fail("GitHub登录失败: " + e.getMessage());
        } catch (Exception e) {
            return FoxResult.fail("登录过程中发生错误: " + e.getMessage());
        }
    }


    @GetMapping("/google/login")
    public FoxResult googleLogin(@RequestParam String code,
                                 @RequestParam(required = false) String state) {
        try {
            // 获取Google OAuth2客户端配置
            Oauth2Client oauth2Client = oauth2ClientService.getByType("google");
            if (oauth2Client == null) {
                return FoxResult.fail("Google OAuth2配置不存在");
            }

            // 使用授权码获取访问令牌
            String accessToken = googleService.getAccessToken(code, oauth2Client);

            // 获取用户信息
            GoogleUser googleUser = googleService.getUserInfo(accessToken);


            ChatgptUser chatgptUser = chatgptUserService.googleLogin(googleUser);
            if (chatgptUser == null) {
                return FoxResult.fail("用户登录失败");
            }
            // 4. 生成JWT令牌
            String token = JwtUtils.generateToken(chatgptUser.getUserToken());

            // 5. 处理多端登录限制
            if (!Objects.equals("true", localCache.getConfigMap().get("canLoginMulti"))) {
                RBucket<String> bucket = redissonClient.getBucket(USER_TOKEN_PREFIX + token);
                bucket.set(chatgptUser.getUserToken(), 86400L * 7, java.util.concurrent.TimeUnit.SECONDS);
            }

            // 6. 返回令牌
            return FoxResult.ok(token);
        } catch (IOException e) {
            log.error("Google OAuth回调处理失败", e);
            return FoxResult.fail("认证失败：" + e.getMessage());
        }
    }
}
