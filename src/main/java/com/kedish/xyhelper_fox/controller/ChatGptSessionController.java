package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSON;
import com.kedish.xyhelper_fox.model.req.AddGptSessionReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.ChatGptSessionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

@RestController
@Slf4j
@RequestMapping("/api/chatGpt/session")
public class ChatGptSessionController extends BaseController{

    @Resource
    private ChatGptSessionService chatGptSessionService;

    @PostMapping("/list")
    public FoxPageResult backEndList(@RequestBody PageQueryReq req) {

        return FoxPageResult.fromPage(chatGptSessionService.page(req));
    }

    @PostMapping("/addSession")
    public FoxResult addSession(@RequestBody AddGptSessionReq req) {
        log.info("addSession req:{}", JSON.toJSONString(req));
        checkIsAdmin();
        return FoxResult.ok(chatGptSessionService.addChatGptSession(req));
    }
    @PostMapping("/addSessionBatch")
    public FoxResult addSessionBatch(@RequestBody String text) {
        log.info("addSession req:{}", JSON.toJSONString(text));
        checkIsAdmin();
        // 将输入的文本按行分割成列表
        List<String> rtList = new ArrayList<>(List.of(text.split("\n")));

        List<AddGptSessionReq> reqList = new ArrayList<>();
        for (String s : rtList) {
            AddGptSessionReq req = new AddGptSessionReq();
            req.setOfficialSession(s);
            req.setSort(1L);
            req.setIsPlus(1);
            req.setStatus(1);
            req.setRemark("批量添加|"+System.currentTimeMillis());
            req.setEmail("1");
            req.setPassword("1");
            req.setCarID(chatGptSessionService.generateCarId());
            reqList.add(req);
        }
        chatGptSessionService.addGptSessionBatch(reqList);

        return FoxResult.ok();
    }

    @PostMapping("/updateSession")
    public FoxResult updateSession(@RequestBody AddGptSessionReq req) {
        log.info("updateSession req:{}", JSON.toJSONString(req));
        checkIsAdmin();
        return FoxResult.ok(chatGptSessionService.updateChatGptSession(req));
    }

    @PostMapping("/deleteSession")
    public FoxResult deleteSession(@RequestBody List<Long> ids) {
        log.info("deleteSession id:{}", JSON.toJSONString(ids));
        checkIsAdmin();
        return FoxResult.ok(chatGptSessionService.deleteChatGptSession(ids));
    }

    @GetMapping("/generateCarId")
    public FoxResult generateCarId() {
        return FoxResult.ok(chatGptSessionService.generateCarId());
    }
}
