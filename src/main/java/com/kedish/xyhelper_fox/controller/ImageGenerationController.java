package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.model.req.ImageEditRequest;
import com.kedish.xyhelper_fox.model.req.ImageGenerationRequest;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.security.UserContext;
import com.kedish.xyhelper_fox.service.ChatgptUserService;
import com.kedish.xyhelper_fox.service.ImageGenerationService;
import com.kedish.xyhelper_fox.service.ImageStorageService;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 图像生成控制器
 * 基础路径: /api
 */
@RestController
@RequestMapping("/api")
public class ImageGenerationController {

    private final ImageGenerationService imageGenerationService;
    private final ImageStorageService imageStorageService;

    private final ChatgptUserService chatgptUserService;

    private final LocalCache localCache;

    public ImageGenerationController(ImageGenerationService imageGenerationService,
                                     ImageStorageService imageStorageService,
                                     ChatgptUserService chatgptUserService,
                                     LocalCache localCache) {
        this.imageGenerationService = imageGenerationService;
        this.imageStorageService = imageStorageService;
        this.chatgptUserService = chatgptUserService;
        this.localCache = localCache;
    }

    /**
     * 生成图像的接口
     *
     * @param request 图像生成请求，包含：
     *               - prompt: 需要生成的图像的文本描述（必需，最大长度1000字符）
     *               - n: 要生成的图像数量（可选，范围1-10）
     *               - size: 图像尺寸（可选，可选值：256x256、512x512、1024x1024）
     *               - model: 使用的模型名称
     * @return 图像生成响应，包含：
     *         - created: 请求创建时的Unix时间戳
     *         - data: 生成的图像URL数组
     *           - url: 生成的图像的URL地址
     */
    @PostMapping("/generate-image")
    public FoxResult generateImage(@RequestBody ImageGenerationRequest request) {
        ChatgptUser user = UserContext.getUser();
        Integer drawPoint = Integer.valueOf(localCache.getConfigMap().getOrDefault("draw_consume_points", "10"));
        chatgptUserService.checkUserLimitEnough(user.getUserToken(), drawPoint);
        return FoxResult.ok(imageGenerationService.generateImage(request));
    }

    @PostMapping("/mock-generate-image")
    public FoxResult mockgenerateImage(@RequestBody ImageGenerationRequest request) {
        return FoxResult.ok(imageGenerationService.mockGenerateImage(request));
    }

    /**
     * 编辑图像的接口
     *
     * @param request 图像编辑请求，包含：
     *               - images: 要编辑的图片数组（必需）
     *                 - gpt-image-1: 支持多张png、webp或jpg文件，每张大小<25MB
     *                 - dall-e-2: 仅支持一张方形png文件，大小<4MB
     *               - prompt: 图像编辑描述（必需，最大长度因模型而异）
     *               - mask: 遮罩图片（可选，指定要编辑的区域，将应用于第一张图片）
     *               - model: 使用的模型（可选，支持dall-e-2和gpt-image-1）
     *               - n: 生成数量（可选，范围1-10）
     *               - quality: 图像质量（可选，支持的选项因模型而异）
     *               - responseFormat: 返回格式（可选，url或b64_json）
     *               - size: 图像尺寸（可选，支持的尺寸因模型而异）
     * @return 图像生成响应，包含：
     *         - created: 请求创建时的Unix时间戳
     *         - data: 生成的图像数据数组
     *           - url: 生成的图像的URL地址（如果responseFormat为url）
     *           - b64_json: Base64编码的图像数据（如果responseFormat为b64_json）
     */
    @PostMapping("/edit-image")
    public FoxResult editImage(@ModelAttribute ImageEditRequest request) {
        ChatgptUser user = UserContext.getUser();
        Integer drawPoint = Integer.valueOf(localCache.getConfigMap().getOrDefault("draw_consume_points", "10"));

        chatgptUserService.checkUserLimitEnough(user.getUserToken(), drawPoint);
        return FoxResult.ok(imageGenerationService.editImage(request));
    }

    @PostMapping("/mock-edit-image")
    public FoxResult mockeditImage(@ModelAttribute ImageEditRequest request) {

        return FoxResult.ok(imageGenerationService.mockEditImage(request));
    }

    /**
     * 下载图片文件
     *
     * @param id 图片存储ID
     * @return 图片文件资源
     */
    @GetMapping("/download-image/{id}")
    public ResponseEntity<Resource> downloadImage(@PathVariable Long id) {
        return imageStorageService.downloadImage(id);
    }

    /**
     * 分页查询图像生成记录
     * @param req 分页查询请求
     * @return 分页结果
     */
    @PostMapping("/image-generation-records/page")
    public FoxPageResult pageImageGenerationRecords(@RequestBody PageQueryReq req) {
        return FoxPageResult.fromPage(imageGenerationService.page(req,false));
    }

    @PostMapping("/image-generation-records/pageAll")
    public FoxPageResult pageAll(@RequestBody PageQueryReq req) {
        return FoxPageResult.fromPage(imageGenerationService.page(req,true));
    }
    /**
     * 分页查询用户积分记录
     * @param req 分页查询请求
     * @return 分页结果
     */
    @PostMapping("/user-points/page")
    public FoxPageResult pageUserPoints(@RequestBody PageQueryReq req) {
        return FoxPageResult.fromPage(imageGenerationService.pageUserPoints(req));
    }

    /**
     * 测试清理过期图像记录（仅用于测试）
     *
     * @return 清理结果
     */
    @GetMapping("/test/cleanup-old-images")
    public FoxResult testCleanupOldImages() {
        imageGenerationService.cleanupOldImageRecords();
        return FoxResult.ok("Cleanup process triggered successfully");
    }
}
