package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSON;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.repo.model.UserPromotion;
import com.kedish.xyhelper_fox.security.UserContext;
import com.kedish.xyhelper_fox.service.UserPromotionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 用户推广控制器
 * 提供用户推广相关的API接口
 */
@RestController
@RequestMapping("/api/userPromotion")
@Slf4j
public class UserPromotionController extends BaseController {

    @Resource
    private UserPromotionService userPromotionService;

    /**
     * 查询当前用户的推广信息
     * @return 推广信息
     */
    @GetMapping("/myInfo")
    public FoxResult getMyPromotionInfo() {
        ChatgptUser currentUser = UserContext.getUser();
        String userToken = currentUser.getUserToken();
        
        log.info("查询用户推广信息，userToken: {}", userToken);
        
        UserPromotion promotionInfo = userPromotionService.getPromotionInfo(userToken);
        
        if (promotionInfo == null) {
            log.info("用户暂无推广信息，userToken: {}", userToken);
            // 返回默认的空推广信息
            UserPromotion emptyPromotion = new UserPromotion();
            emptyPromotion.setUserToken(userToken);
            emptyPromotion.setPromotionAmount(java.math.BigDecimal.ZERO);
            emptyPromotion.setPromotionOrderNum(0);
            emptyPromotion.setWaitWithdrawAmount(java.math.BigDecimal.ZERO);
            emptyPromotion.setWithdrawAmount(java.math.BigDecimal.ZERO);
            return FoxResult.ok(emptyPromotion);
        }
        
        return FoxResult.ok(promotionInfo);
    }

    /**
     * 管理员查询指定用户的推广信息
     * @param userToken 用户标识
     * @return 推广信息
     */
    @GetMapping("/info")
    public FoxResult getPromotionInfo(@RequestParam String userToken) {
        log.info("管理员查询用户推广信息，userToken: {}", userToken);
        
        // 检查管理员权限
        checkIsAdmin();
        
        UserPromotion promotionInfo = userPromotionService.getPromotionInfo(userToken);
        
        if (promotionInfo == null) {
            log.info("用户暂无推广信息，userToken: {}", userToken);
            return FoxResult.fail("用户暂无推广信息");
        }
        
        return FoxResult.ok(promotionInfo);
    }
}
