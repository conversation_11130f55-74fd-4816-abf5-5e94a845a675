package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSON;
import com.kedish.xyhelper_fox.model.req.AddClaudeSessionReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.ClaudeSessionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
@RequestMapping("/api/claudeSession")
public class ClaudeSessionController extends BaseController{


    @Resource
    private ClaudeSessionService claudeSessionService;

    @PostMapping("/addOrUpdate")
    public FoxResult add(@RequestBody AddClaudeSessionReq req) {

        log.info("claudeSession addOrUpdate: addReq is {}", JSON.toJSONString(req));
        checkIsAdmin();
        claudeSessionService.saveOrUpdate(req);
        return FoxResult.ok();
    }

    @PostMapping("/page")
    public FoxResult page(@RequestBody PageQueryReq req) {
        log.info("claudeSession page: pageReq is {}", JSON.toJSONString(req));
        return FoxPageResult.fromPage(claudeSessionService.page(req));
    }

    @PostMapping("/delete")
    public FoxResult delete(@RequestBody List<Long> ids) {
        log.info("claudeSession delete: id is {}", JSON.toJSONString(ids));
        checkIsAdmin();
        claudeSessionService.delete(ids);
        return FoxResult.ok();
    }
}
