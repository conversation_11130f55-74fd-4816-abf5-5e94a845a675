package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.component.ClaudeProxy;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.ClaudeCarVO;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.ChatGptSessionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
@RequestMapping("/api/lyy/claudeSession")
public class LyyClaudeSessionController extends BaseController{

    @Resource
    private ChatGptSessionService chatGptSessionService;
    @Resource
    private ClaudeProxy claudeProxy;

    @PostMapping("/addOrUpdate")
    public FoxResult add(@RequestBody ClaudeCarVO req) {

        log.info("claudeSession addOrUpdate: addReq is {}", JSON.toJSONString(req));
        checkIsAdmin();
        String result;
        if (req.getId() != null) {
            result = claudeProxy.updateClaudeSession(req);
        } else {
            result = claudeProxy.addClaudeSession(req);
        }
        checkShareServerResult(result);
        chatGptSessionService.refreshClaudeNode();
        return FoxResult.ok();
    }

    private void checkShareServerResult(String result) {
        //{code: 1000, message: "BaseResMessage", data: {Locker: {}}}
        JSONObject jsonObject = JSON.parseObject(result);
        if (jsonObject.getInteger("code") != 1000) {
            throw new FoxException(jsonObject.getString("message"));
        }
    }

    @PostMapping("/page")
    public FoxResult page(@RequestBody PageQueryReq req) {
        log.info("claudeSession page: pageReq is {}", JSON.toJSONString(req));
        Page<ClaudeCarVO> claudeCars = claudeProxy.page(req.getPageNum(), req.getPageSize());

        return FoxPageResult.fromPage(claudeCars);
    }

    @PostMapping("/delete")
    public FoxResult delete(@RequestBody List<Long> ids) {
        log.info("claudeSession delete: id is {}", JSON.toJSONString(ids));
        checkIsAdmin();
        String s = claudeProxy.deleteClaudeSession(ids);
        checkShareServerResult(s);
        chatGptSessionService.refreshClaudeNode();
        return FoxResult.ok();
    }
}
