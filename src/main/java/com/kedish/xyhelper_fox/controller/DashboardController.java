package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.DashboardService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/dashboard")
public class DashboardController {

    @Resource
    DashboardService dashboardService;

    @GetMapping("/summary")
    public FoxResult summary() {
        return FoxResult.ok(
                dashboardService
                        .getSummary()
        );
    }

    @GetMapping("/today-usage")
    public FoxResult todayUsage() {
        return FoxResult.ok(dashboardService.getTodayUsageStats());
    }

    @GetMapping("/users/growth")
    public FoxResult userGrowthTrend(@RequestParam(required = false, defaultValue = "7") Integer days) {
        return FoxResult.ok(dashboardService.getUserGrowthTrend(days));
    }

    @GetMapping("/users/group-distribution")
    public FoxResult userGroupDistribution() {
        return FoxResult.ok(dashboardService.getUserGroupDistribution());
    }

    @GetMapping("/orders/trend")
    public FoxResult orderTrend(@RequestParam(required = false, defaultValue = "7") Integer days) {
        return FoxResult.ok(dashboardService.getOrderTrend(days));
    }

    @GetMapping("/orders/distribution")
    public FoxResult orderDistribution(@RequestParam(required = false, defaultValue = "7") Integer days) {
        return FoxResult.ok(dashboardService.getOrderDistribution(days));
    }

    @GetMapping("/today-user-ranking")
    public FoxResult todayUserRanking() {
        return FoxResult.ok(dashboardService.getTodayUserRanking());
    }
}
