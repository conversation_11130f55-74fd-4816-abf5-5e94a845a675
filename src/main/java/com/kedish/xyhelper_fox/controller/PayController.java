package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.zxing.WriterException;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.model.req.PayReq;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.model.resp.PayQrCodeResp;
import com.kedish.xyhelper_fox.pay.epay.EpayService;
import com.kedish.xyhelper_fox.repo.model.Order;
import com.kedish.xyhelper_fox.repo.model.PaymentMethod;
import com.kedish.xyhelper_fox.service.OrderService;
import com.kedish.xyhelper_fox.service.PayService;
import com.kedish.xyhelper_fox.service.PaymentMethodService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
@RequestMapping("/api/pay")
@RestController
public class PayController extends BaseController{

    @Resource
    private PayService payService;

    @Resource
    private OrderService orderService;

    @Resource
    private PaymentMethodService paymentMethodService;

    @Resource
    private EpayService epayService;

    @PostMapping("/request")
    public FoxResult pay(@RequestBody PayReq payReq) {
        return FoxResult.ok();
    }

    @PostMapping("/qrCode")
    public FoxResult qrCode(@RequestBody PayReq payReq) {
        try {
            PayQrCodeResp qrCodeUrl = payService.getQrCodeUrl(payReq);
            return FoxResult.ok(qrCodeUrl);
        } catch (IOException | WriterException e) {
            log.error(e.getMessage(), e);
            throw new FoxException("创建支付订单失败，你可以重试，如果多次失败请联系管理员");
        }
    }


    /**
     * 商户ID	pid	是	Int	1001
     * 易支付订单号	trade_no	是	String	20160806151343349021	聚合支付平台订单号
     * 商户订单号	out_trade_no	是	String	20160806151343349	商户系统内部的订单号
     * 支付方式	type	是	String	alipay	支付方式列表
     * 商品名称	name	是	String	VIP会员
     * 商品金额	money	是	String	1.00
     * 支付状态	trade_status	是	String	TRADE_SUCCESS	只有TRADE_SUCCESS是成功
     * 业务扩展参数	param	否	String
     * 签名字符串	sign	是	String	202cb962ac59075b964b07152d234b70	签名算法点此查看
     * 签名类型	sign_type	是	String	MD5	默认为MD5
     */
    @GetMapping("/yzf/notify")
    public String notify(Integer pid,
                         String trade_no,
                         String out_trade_no,
                         String type,
                         String name,
                         String money,
                         String trade_status,
                         String param,
                         String sign,
                         String sign_type) {
        log.info("notify,pid:{},trade_no:{},out_trade_no:{},type:{},name:{},money:{},trade_status:{},param:{},sign:{},signType:{}",
                pid, trade_no, out_trade_no, type, name, money, trade_status, param, sign, sign_type);
        Order order = orderService.queryOrder(out_trade_no);
        if (order == null) {
            log.warn("订单不存在,outTradeNo:{}", out_trade_no);
            return "success";
        }
        PaymentMethod paymentMethod = getPaymentMethod(order);
        Map<String, Object> params = new TreeMap<>();
        params.put("pid", pid);
        params.put("trade_no", trade_no);
        params.put("out_trade_no", out_trade_no);
        params.put("type", type);
        params.put("name", name);
        params.put("money", money);
        params.put("trade_status", trade_status);
        params.put("param", param);
        params.put("sign", sign);
        params.put("sign_type", sign_type);
        boolean b = epayService.checkSign(params, paymentMethod);
        if (!b) {
            log.warn("验签失败,outTradeNo:{}", out_trade_no);
        } else {
            payService.notifyTradeSuccess(order);
        }

        return "success";
    }

    @GetMapping("/changeOrder2Success")
    public FoxResult changeOrder2Success(@RequestParam String outTradeNo) {

        log.info("changeOrder2Success,outTradeNo:{}", outTradeNo);
        checkIsAdmin();
        Order order = orderService.queryOrder(outTradeNo);

        payService.notifyTradeSuccess(order);
        return FoxResult.ok();
    }

    private PaymentMethod getPaymentMethod(Order order) {
        String buyInfo = order.getBuyInfo();
        JSONObject jsonObject = JSONObject.parseObject(buyInfo);
        Long paymentMethodId = jsonObject.getLong("paymentMethodId");
        return paymentMethodService.getById(paymentMethodId);
    }

    @GetMapping("/queryOrderStatus")
    public FoxResult queryOrder(@RequestParam String outTradeNo) {

        Order order = orderService.queryOrder(outTradeNo);
        if (order == null) {
            return FoxResult.fail("订单不存在");
        }
//        payService.query
        return FoxResult.ok(order.getStatus());
    }


}
