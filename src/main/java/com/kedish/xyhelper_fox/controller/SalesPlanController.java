package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSON;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.req.SalesPlanAddReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.SalesPlanService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
@RequestMapping("/api/salesPlan")
public class SalesPlanController extends BaseController {

    @Resource
    private SalesPlanService salesPlanService;

    @PostMapping("/page")
    public FoxPageResult page(@RequestBody PageQueryReq req) {
        log.info("salesPlan page: req is {}", JSON.toJSONString(req));
        return FoxPageResult.fromPage(salesPlanService.page(req));
    }

    @PostMapping("/addOrUpdate")
    public FoxResult addOrUpdate(@RequestBody SalesPlanAddReq req) {
        log.info("salesPlan addOrUpdate: addReq is {}", JSON.toJSONString(req));
        checkIsAdmin();
        salesPlanService.addOrUpdate(req);
        return FoxResult.ok();
    }

    @PostMapping("/deleteById")
    public FoxResult deleteById(@RequestParam Long id) {
        log.info("salesPlan deleteById: id is {}", id);
        checkIsAdmin();
        salesPlanService.deleteById(id);
        return FoxResult.ok();
    }
}
