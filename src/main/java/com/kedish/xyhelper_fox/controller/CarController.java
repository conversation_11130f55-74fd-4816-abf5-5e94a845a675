package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSONObject;
import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.model.req.SelectCarReq;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.repo.model.ClaudeSession;
import com.kedish.xyhelper_fox.security.UserContext;
import com.kedish.xyhelper_fox.service.ChatGptSessionService;
import com.kedish.xyhelper_fox.service.ClaudeSessionService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import static com.kedish.xyhelper_fox.utils.HttpServletRequestUtils.getGfSessionIdFromCookie;

@RestController
@Slf4j
@RequestMapping("/api/chatGpt/car")
public class CarController {

    @Resource
    private ChatGptSessionService chatGptSessionService;

    @Resource
    private LocalCache localCache;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ClaudeSessionService claudeSessionService;

    @Resource
    private RedissonClient redissonClient;

    @GetMapping("/list")
    public FoxResult carList(@RequestParam(required = true) String type) {


        return FoxResult.ok(chatGptSessionService.carList(type));
//        return FoxResult.ok(Arrays.asList(c1, c2, c3));
    }

    @PostMapping("/selectCar")
    public FoxResult selectCar(@RequestBody SelectCarReq req) {
        String carId = req.getCarId();
        String type = req.getType();
        chatGptSessionService.checkUserRights(carId, type);
        return FoxResult.ok(chatGptSessionService.selectCarId(carId, type));
    }

    @GetMapping("/selectAnotherCar")
    public FoxResult selectAnotherCar(HttpServletRequest httpServletRequest) {
        String gfsessionid = getGfSessionIdFromCookie(httpServletRequest);
        RBucket<Object> bucket = redissonClient.getBucket("gfsession:" + gfsessionid, StringCodec.INSTANCE);
        String session = bucket.get().toString();
        System.out.println("session is: "+ session);
        JSONObject jsonObject = JSONObject.parseObject(session);
        String carId = jsonObject.getString("carid");
        String type = chatGptSessionService.selectCarType(carId);
        chatGptSessionService.checkUserRights(carId, type);

        String anotherCar = chatGptSessionService.selectAnotherCar(carId, type);

        jsonObject.put("carid",anotherCar);
        return FoxResult.ok(jsonObject);
    }

    @GetMapping("/selectGrokCar")
    public FoxResult selectGrokCar(@RequestParam String type) {
        chatGptSessionService.checkUserRights("", type);

        ChatgptUser chatgptUser = UserContext.getUser();

        String grokUrl = localCache.getConfigMap().get("grokUrl");
        return FoxResult.ok(grokUrl + "/signtoken?usertoken=" + chatgptUser.getUserToken());
    }

    @PostMapping("/selectClaudeCar")
    public FoxResult selectClaudeCar(@RequestBody SelectCarReq req) {
        String carId = req.getCarId();
        String type = req.getType();
        chatGptSessionService.checkUserRights(carId, type);
        String chooseCarId = chatGptSessionService.selectCarId(carId, type);
        if (null == chooseCarId) {
            throw new FoxException("没有可用的carId");
        }
        String claudeUrl = localCache.getConfigMap().get("claudeUrl");
        String lyyClaudeUrl = localCache.getConfigMap().get("lyyClaudeUrl");


        ChatgptUser user = UserContext.getUser();

        if (StringUtils.hasLength(claudeUrl)) {

            JSONObject json = new JSONObject();
            ClaudeSession byCarID = claudeSessionService.getByCarID(chooseCarId);
            json.put("session_key", byCarID.getSession());
            json.put("unique_name", user.getUserToken());

            ResponseEntity<JSONObject> resp = restTemplate.postForEntity(claudeUrl + "/manage-api/auth/oauth_token", json, JSONObject.class);
            JSONObject body = resp.getBody();
            if (body == null) {
                throw new FoxException("获取claude地址失败,claude httpStatus" + resp.getStatusCode());
            }
            if (resp.getStatusCode().equals(HttpStatus.OK)) {

                return FoxResult.ok(claudeUrl + body.getString("login_url"));
            }
            throw new FoxException("获取claude地址失败,claude httpStatus" + resp.getStatusCode());
        } else if (StringUtils.hasLength(lyyClaudeUrl)) {
            return FoxResult.ok(lyyClaudeUrl + "/logintoken?usertoken=" + user.getUserToken() + "&carid=" + chooseCarId
                    + "&isPlus=1");
        }
        throw new FoxException("获取claude地址失败,没有可用的地址");
    }

}
