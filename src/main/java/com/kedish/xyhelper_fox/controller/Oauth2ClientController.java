package com.kedish.xyhelper_fox.controller;

import com.alibaba.fastjson.JSON;
import com.kedish.xyhelper_fox.model.req.Oauth2ClientReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.FoxPageResult;
import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.service.Oauth2ClientService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Slf4j
@RequestMapping("/api/oauth2Client")
public class Oauth2ClientController extends BaseController{

    @Resource
    private Oauth2ClientService oauth2ClientService;

    @PostMapping("/page")
    public FoxPageResult page(@RequestBody PageQueryReq req) {
        log.info("oauth2Client page: req is {}", JSON.toJSONString(req));
        return FoxPageResult.fromPage(oauth2ClientService.page(req));
    }

    @PostMapping("/addOrUpdate")
    public FoxResult addOrUpdate(@RequestBody Oauth2ClientReq req) {
        log.info("oauth2Client addOrUpdate: req is {}", JSON.toJSONString(req));
        checkIsAdmin();
        oauth2ClientService.saveOrUpdate(req);
        return FoxResult.ok();
    }

    @PostMapping("/deleteByIds")
    public FoxResult deleteByIds(@RequestBody List<Integer> ids) {
        log.info("oauth2Client deleteByIds: ids is {}", JSON.toJSONString(ids));
        checkIsAdmin();
        oauth2ClientService.deleteByIds(ids);
        return FoxResult.ok();
    }

    @GetMapping("/queryAll")
    public FoxResult queryAll() {
        log.info("oauth2Client queryAll");
        return FoxResult.ok(oauth2ClientService.queryAll());
    }

    @GetMapping("/getByType")
    public FoxResult getByType(@RequestParam String type) {
        log.info("oauth2Client getByType: type is {}", type);
        return FoxResult.ok(oauth2ClientService.getByType(type));
    }
}
