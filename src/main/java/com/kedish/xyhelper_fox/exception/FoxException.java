package com.kedish.xyhelper_fox.exception;

import lombok.Getter;

@Getter
public class FoxException extends RuntimeException{


    public static final int NOT_ENOUGH_POINTS = 1000;

    public static final int OPENAI_MODEL_FORBIDDEN = 1001;
    int code = -1;
    String messageKey;

    public FoxException(String msg) {
        super(msg);
    }

    public FoxException(String key,String msg){
        super(msg);
        this.messageKey = key;
    }

    public FoxException(int code, String msg) {
        super(msg);
        this.code = code;
    }

}
