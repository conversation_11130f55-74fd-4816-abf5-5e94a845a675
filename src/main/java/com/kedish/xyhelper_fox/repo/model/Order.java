package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("orders")
public class Order {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id; // 主键ID

    private BigDecimal amount; // 订单金额

    private String username; // 用户名

    @TableField(value = "trade_no")
    private String tradeNo; // 内部订单号

    @TableField(value = "out_trade_no")
    private String outTradeNo; // 外部订单号

    @TableField(value = "coupon_code")
    private String couponCode;

    private Integer status; // 订单状态 0-待支付，1-已支付

    @TableField(value = "sales_plan")
    private String salesPlan; // 对应套餐

    private String remarks; // 备注

    @TableField(value = "buy_info")
    private String buyInfo;

    @TableField(value = "created_at")
    private LocalDateTime createdAt; // 创建时间

    @TableField(value = "updated_at")
    private LocalDateTime updatedAt; // 修改时间

    @TableField(value = "cashback_amount")
    private BigDecimal cashbackAmount; // 返利金额

    @TableField(value = "cashback_user_token")
    private  String cashbackUserToken;
}
