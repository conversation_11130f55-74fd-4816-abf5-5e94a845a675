package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ActivationCode {

    @TableId(type=IdType.AUTO)
    private Long id;

    @TableField(value = "activation_code")
    private String activationCode;

    @TableField(value = "sales_plan_id")
    private Long salesPlanId;

    @TableField(value = "sales_plan_name")
    private String salesPlanName;

    private Integer status;

    @TableField(value = "used_by")
    private String usedBy;

    @TableField(value = "used_at")
    private LocalDateTime usedAt;

    @TableField(value = "expiration_time")
    private LocalDateTime expirationTime;

    private String remark;

    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;
}
