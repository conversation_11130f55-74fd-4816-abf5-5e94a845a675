package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 图片存储实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("image_storage")
public class ImageStorage {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的记录ID
     */
    @TableField("record_id")
    private Long recordId;

    /**
     * 图片类型：SOURCE-源图片, MASK-遮罩图片, GENERATED-生成的图片
     */
    @TableField("image_type")
    private ImageType imageType;

    /**
     * 原始文件名
     */
    @TableField("original_filename")
    private String originalFilename;

    /**
     * 存储的文件名（包含路径）
     */
    @TableField("stored_filename")
    private String storedFilename;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件类型（MIME类型）
     */
    @TableField("content_type")
    private String contentType;

    /**
     * 图片URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * MD5哈希值
     */
    @TableField("md5_hash")
    private String md5Hash;

    /**
     * 记录创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 记录更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;

    /**
     * 图片类型枚举
     */
    public enum ImageType {
        SOURCE,     // 源图片
        MASK,       // 遮罩图片
        GENERATED   // 生成的图片
    }
}
