package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class GroupRateLimit {

    @TableId(type=IdType.AUTO)
    private Long id;

    @TableField(value = "group_id")
    private Long groupId;

    @TableField(value = "model")
    private String model;

    @TableField(value = "rate")
    private Integer rate;

    @TableField(value = "period")
    private String period;

    @TableField(value = "multiplier")
    private Integer multiplier;

    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;
} 