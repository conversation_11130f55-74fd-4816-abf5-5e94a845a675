package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("forbidden_words")  // 指定数据库中的表名
public class ForbiddenWord {

    @TableId  // 标注主键
    private Long id;  // 主键 id

    private String word;  // 违禁词内容

    @TableField(value = "created_at")
    private LocalDateTime createdAt;  // 创建时间

    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;  // 最后更新时间

}
