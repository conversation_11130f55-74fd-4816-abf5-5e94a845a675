package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("system_notification")
public class SystemNotification {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String content;

    /**
     * <el-radio-button label="1">首页公告</el-radio-button>
     * <el-radio-button label="2">站内通知</el-radio-button>
     * <el-radio-button label="3">使用说明</el-radio-button>
     * <el-radio-button label="4">选车说明</el-radio-button>
     */
    private Integer type;

    /**
     * 状态:0-关闭,1-开启
     */
    private Integer status;

    /**
     * 内容类型:1-富文本,2-HTML
     */
    @TableField(value = "content_type")
    private Integer contentType;

    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;
}