package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("user_rate_limit")
public class UserRateLimit {

    @TableId(type=IdType.AUTO)
    private Long id;

    @TableField(value = "user_token")
    private String userToken;

    @TableField(value = "model")
    private String model;

    @TableField(value = "rate")
    private Integer rate;

    @TableField(value = "period")
    private String period;

    @TableField(value = "multiplier")
    private Integer multiplier;

    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;
}
