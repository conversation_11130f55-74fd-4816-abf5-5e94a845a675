package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现记录表实体类
 * 对应数据库表withdraw_record
 */
@Data
@TableName("withdraw_record")
public class WithdrawRecord {

    /**
     * 主键ID，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户标识
     */
    @TableField("user_token")
    private String userToken;

    /**
     * 提现金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 状态：0-待处理，1-成功，2-失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 提现时间
     */
    @TableField("withdraw_time")
    private LocalDateTime withdrawTime;

    /**
     * 二维码图片（存储路径或URL）
     */
    @TableField("qr_code_image")
    private String qrCodeImage;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
