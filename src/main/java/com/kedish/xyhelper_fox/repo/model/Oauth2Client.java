package com.kedish.xyhelper_fox.repo.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * CREATE TABLE `oauth2_client` (
 *                                          `client_id` VARCHAR(100) NOT NULL COMMENT '客户端唯一标识',
 *                                          `client_secret` VARCHAR(200) NOT NULL COMMENT '客户端密钥（加密存储）',
 *                                          `scope` VARCHAR(500) NOT NULL COMMENT '权限范围，多个用空格分隔',
 *                                          `type` VARCHAR(64) NOT NULL COMMENT 'oauth2类型，github',
 *                                          `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 *                                          `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 *                                          PRIMARY KEY (`client_id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OAuth2客户端配置表';
 */
@Data
@TableName("oauth2_client")
public class Oauth2Client {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("client_id")
    private String clientId;

    @TableField("client_secret")
    private String clientSecret;

    @TableField("redirect_uri")
    private String redirectUri;
    
    private String scope;

    private String type;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;
}
