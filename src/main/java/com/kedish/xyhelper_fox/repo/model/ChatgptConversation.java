package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
//@EqualsAndHashCode(callSuper = true)
@TableName("chatgpt_conversations")
public class ChatgptConversation {

    @TableId
    private Long id; // 主键

    @TableField("createTime")
    private LocalDateTime createTime; // 创建时间

    @TableField("updateTime")
    private LocalDateTime updateTime; // 更新时间

    //    @TableLogic
    @TableField("deleted_at")
    private LocalDateTime deletedAt; // 删除时间（逻辑删除字段）

    @TableField("usertoken")
    private String userToken; // 用户token

    @TableField("convid")
    private String convId; // 会话id

    @TableField("title")
    private String title; // 会话标题

    @TableField("email")
    private String email; // 官网账号邮箱

    @TableField("chatgptaccountid")
    private String chatgptAccountId;

    private String gizmoid;

    private String content;

    @TableField("is_archived")
    private Boolean isArchived;

    @TableField("need_update")
    private Boolean needUpdate;
}
