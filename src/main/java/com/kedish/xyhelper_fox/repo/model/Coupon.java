package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("coupon")  // 表示与数据库表 coupon 对应
public class Coupon {

    @TableId
    private Long id;  // 优惠券唯一ID

    @TableField(value = "coupon_code")
    private String couponCode;  // 优惠代码

    @TableField(value = "discount_amount")
    private BigDecimal discountAmount;  // 优惠金额

    @TableField(value = "discount_percentage")
    private BigDecimal discountPercentage;  // 优惠折扣

    @TableField(value = "sales_plan")
    private String salesPlan;  // 是否仅限订阅用户

    @TableField(value = "expiration_time")
    private LocalDateTime expirationTime;  // 优惠券过期时间

    private String remark;

    @TableField(fill = FieldFill.INSERT, value = "created_at")
    private LocalDateTime createdAt;  // 创建时间

    @TableField(fill = FieldFill.INSERT_UPDATE, value = "updated_at")
    private LocalDateTime updatedAt;  // 修改时间
}
