package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 图像生成记录实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("image_generation_records")
public class ImageGenerationRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户令牌
     */
    @TableField(value = "user_token")
    private String userToken;

    /**
     * 进度: 1-生成中，2-生成成功，3-生成失败
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 操作类型：GENERATE-生成图像, EDIT-编辑图像
     */
    @TableField(value = "operation_type")
    private OperationType operationType;

    /**
     * 提示词
     */
    @TableField(value = "prompt")
    private String prompt;

    /**
     * 使用的模型
     */
    @TableField(value = "model")
    private String model;

    /**
     * 请求的图片数量
     */
    @TableField(value = "requested_count")
    private Integer requestedCount;

    /**
     * 图片尺寸
     */
    @TableField(value = "size")
    private String size;

    /**
     * 图片质量
     */
    @TableField(value = "quality")
    private String quality;

    /**
     * 源图片文件名（用于编辑操作）
     * 使用JSON数组存储
     */
    @TableField(value = "source_image_files")
    private String sourceImageFiles;

    /**
     * 遮罩图片文件名（用于编辑操作）
     */
    @TableField(value = "mask_image_file")
    private String maskImageFile;

    /**
     * 生成的图片URL或Base64数据
     * 使用JSON数组存储
     */
    @TableField(value = "generated_images")
    private String generatedImages;

    /**
     * OpenAI返回的创建时间戳
     */
    @TableField(value = "openai_created_timestamp")
    private Long openaiCreatedTimestamp;

    /**
     * 记录创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 记录更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 操作是否成功
     */
    @TableField(value = "is_successful")
    private Boolean successful;

    /**
     * 错误信息（如果有）
     */
    @TableField(value = "error_message")
    private String errorMessage;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

    /**
     * 消耗积分
     */
    @TableField(value = "point")
    private Integer point;
    /**
     * 操作类型枚举
     */
    public enum OperationType {
        GENERATE,
        EDIT
    }
}
