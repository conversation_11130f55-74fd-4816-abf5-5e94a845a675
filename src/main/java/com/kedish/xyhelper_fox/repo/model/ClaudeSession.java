package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("claude_session")
public class ClaudeSession implements Serializable {


    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("carid")
    private String carID;

    @TableField("email")
    private String email;

    @TableField("account_type")
    private Integer accountType;

    @TableField("enabled")
    private Boolean enabled;

    @TableField("session")
    private String session;

    @TableField("remarks")
    private String remarks;

    @TableField("createdAt")
    private LocalDateTime createdAt;

    @TableField("updatedAt")
    private LocalDateTime updatedAt;
}