package com.kedish.xyhelper_fox.repo.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("chatgpt_session")
public class ChatGptSession {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @TableField(value = "deleted_at")
    private LocalDateTime deletedAt;

    private String email;

    private String password;

    private Integer status;

    private Integer isPlus;

    private String carID;

    private String officialSession;

    private String remark;

    private Long sort;

    private Long count;

    @TableField("iqStatus")
    private Integer iqStatus;
}
