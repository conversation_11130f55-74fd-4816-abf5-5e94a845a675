package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Data // Lombok 自动生成 Getter、Setter、toString、equals、hashCode 等方法
@EqualsAndHashCode
@ToString
@TableName("chatgpt_user") // MyBatis-Plus 注解，指定表名
public class ChatgptUser {

    @TableId(value = "id", type = IdType.AUTO)// 标识主键
    private Long id;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    @TableField("deleted_at")
    private LocalDateTime deletedAt;

    private String userToken;

    private LocalDateTime expireTime;

    private Boolean isPlus;

    private String remark;

    private LocalDateTime plusExpireTime;

    private String email;

    private String password;

    private Boolean isAdmin;

    @TableField("`limit`")
    private Long limit;

    private String per;

    @TableField("user_type")
    private Integer userType;

    @TableField("enable_claude")
    private Boolean enableClaude;

    private Integer status;

    @TableField("invite_code")
    private String inviteCode;

    @TableField("invite_by")
    private String inviteBy;

    @TableField(value = "group_id")
    private Long groupId = 1L;

    @TableField(value = "enable_userToken_login")
    private Boolean enableUserTokenLogin;

    public boolean isVisitor(){
        return this.userToken.startsWith("visitorId_");
    }
}
