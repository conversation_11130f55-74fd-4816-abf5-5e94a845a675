package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 用户积分记录实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("user_points_record")
public class UserPointsRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户Token
     */
    @TableField("user_token")
    private String userToken;

    /**
     * 积分数量，正数表示获得积分，负数表示消耗积分
     */
    @TableField("points_amount")
    private Integer pointsAmount;

    /**
     * 记录类型：1-获得积分，2-消耗积分
     */
    @TableField("record_type")
    private Integer recordType;

    /**
     * 积分来源/消耗类型
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * 关联的业务ID，如订单ID、图片生成记录ID等
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 描述信息
     */
    @TableField("description")
    private String description;

    /**
     * 变动后的积分余额
     */
    @TableField("balance_after")
    private Integer balanceAfter;

    /**
     * 记录创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 记录更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标记
     */
    @TableLogic
    @TableField("is_deleted")
    private Boolean deleted;

    /**
     * 记录类型枚举
     */
    public enum RecordType {
        EARN(1),     // 获得积分
        CONSUME(2);  // 消耗积分

        private final int value;

        RecordType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        public static RecordType fromValue(int value) {
            for (RecordType type : RecordType.values()) {
                if (type.value == value) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Invalid RecordType value: " + value);
        }
    }

    /**
     * 积分来源/消耗类型枚举
     */
    public enum SourceType {
        REGISTER_REWARD(1),       // 注册奖励
        ADMIN_ADDITION(2),       // 管理员新增
        INVITE_REWARD(3),        // 邀请奖励
        IMAGE_GEN_CONSUME(4),    // 图片生成预消耗
        IMAGE_GEN_REFUND(5),     // 图片生成失败退还
        POINTS_PURCHASE(6);      // 积分购买

        private final int value;

        SourceType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        public static SourceType fromValue(int value) {
            for (SourceType type : SourceType.values()) {
                if (type.value == value) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Invalid SourceType value: " + value);
        }
    }
}
