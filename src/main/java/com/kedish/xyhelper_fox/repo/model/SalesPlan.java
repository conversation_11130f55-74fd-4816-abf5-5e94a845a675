package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("sales_plan")  // 表示与数据库表 sales_plan 对应
public class SalesPlan {

    @TableId
    private Long id;  // 售卖计划唯一ID


    private String name;

    private BigDecimal amount;  // 金额

    @TableField(value = "valid_days")
    private Integer validDays;  // 有效天数

    @TableField(value = "membership_type")
    private String membershipType;  // 会员类型

    @TableField(value = "is_display_on_front")
    private Boolean isDisplayOnFront;  // 前台是否展示

    @TableField(value = "is_hot")
    private Boolean isHot;  // 是否热门

    private String per;  // 速率周期

    @TableField(value = "`limit`")
    private Integer limit;  // 速率

    @TableField(value = "`order`")
    private Integer order;

    private String tags;
    @TableField(fill = FieldFill.INSERT, value = "created_at")
    private LocalDateTime createdAt;  // 创建时间

    @TableField(fill = FieldFill.INSERT_UPDATE, value = "updated_at")
    private LocalDateTime updatedAt;  // 修改时间
}
