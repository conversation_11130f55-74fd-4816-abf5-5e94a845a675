package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserGroup {

    @TableId(type=IdType.AUTO)
    private Long id;

    @TableField(value = "name")
    private String name;

    @TableField(value = "title")
    private String title;

    @TableField(value = "description")
    private String description;

    @TableField(value = "is_deleted")
    private Boolean isDeleted;

    @TableField(value = "available_nodes")
    private String availableNodes;

    @TableField(value = "auto_mini_share_gpt4_limit")
    private Boolean autoMiniShareGpt4Limit;

    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;
} 