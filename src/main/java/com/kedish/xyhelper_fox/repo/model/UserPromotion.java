package com.kedish.xyhelper_fox.repo.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户推广表实体类
 * 对应数据库表user_promotion
 */
@Data
@TableName("user_promotion")
public class UserPromotion {

    /**
     * 主键ID，自增
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户标识
     */
    @TableField("user_token")
    private String userToken;

    /**
     * 推广金额
     */
    @TableField("promotion_amount")
    private BigDecimal promotionAmount;

    /**
     * 推广订单数量
     */
    @TableField("promotion_order_num")
    private Integer promotionOrderNum;

    /**
     * 提现金额
     */
    @TableField("withdraw_amount")
    private BigDecimal withdrawAmount;

    /**
     * 待提现金额
     */
    @TableField("wait_withdraw_amount")
    private BigDecimal waitWithdrawAmount;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
