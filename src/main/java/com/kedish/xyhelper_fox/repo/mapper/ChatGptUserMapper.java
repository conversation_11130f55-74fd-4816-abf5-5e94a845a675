package com.kedish.xyhelper_fox.repo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Mapper
public interface ChatGptUserMapper extends BaseMapper<ChatgptUser> {

    int getTotalCount();

    int getNewCount(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取指定时间之前的用户总数
     * @param endTime 截止时间
     * @return 用户总数
     */
    int getTotalCountBefore(@Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取指定用户类型的用户数量
     * @param userType 用户类型
     * @return 用户数量
     */
    int getCountByUserType(@Param("userType") Integer userType);

    // 获取某个分组下的所有用户
    java.util.List<ChatgptUser> selectByGroupId(@Param("groupId") Long groupId);


    List<Map<String, Object>> getDailyNewUsers(@Param("startTime") LocalDateTime startTime);
}
