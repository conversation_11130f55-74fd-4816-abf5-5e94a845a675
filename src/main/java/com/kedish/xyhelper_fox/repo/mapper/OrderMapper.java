package com.kedish.xyhelper_fox.repo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kedish.xyhelper_fox.repo.model.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;


@Mapper
public interface OrderMapper extends BaseMapper<Order> {

    List<Order> queryOrderByCreateTime(@Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查询所有已支付订单
     * @return 已支付订单列表
     */
    List<Order> queryPaidOrders();

    /**
     * 查询指定时间范围内的已支付订单
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 已支付订单列表
     */
    List<Order> queryPaidOrdersByTimeRange(@Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定时间范围内的所有订单
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单列表
     */
    List<Order> queryOrderByTimeRange(@Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);
}
