package com.kedish.xyhelper_fox.repo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kedish.xyhelper_fox.repo.model.ImageGenerationRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图像生成记录Mapper接口
 */
@Mapper
public interface ImageGenerationRecordMapper extends BaseMapper<ImageGenerationRecord> {

    /**
     * 查询指定天数之前的图像生成记录
     *
     * @param days 天数
     * @return 图像生成记录列表
     */
    List<ImageGenerationRecord> findRecordsOlderThanDays(@Param("days") int days);
}
