package com.kedish.xyhelper_fox.repo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kedish.xyhelper_fox.repo.model.UserPointsRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户积分记录Mapper接口
 */
@Mapper
public interface UserPointsRecordMapper extends BaseMapper<UserPointsRecord> {

    /**
     * 获取用户当前积分余额
     * @param userToken 用户Token
     * @return 积分余额
     */
    @Select("SELECT IFNULL(MAX(balance_after), 0) FROM user_points_record WHERE user_token = #{userToken} AND is_deleted = 0")
    Integer getUserPointsBalance(@Param("userToken") String userToken);

    /**
     * 获取用户在指定时间段内的积分记录
     * @param userToken 用户Token
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 积分记录列表
     */
    @Select("SELECT * FROM user_points_record WHERE user_token = #{userToken} AND created_at BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0 ORDER BY created_at DESC")
    List<UserPointsRecord> getUserPointsRecordsByTimeRange(
            @Param("userToken") String userToken,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 获取用户在指定时间段内获得的积分总数
     * @param userToken 用户Token
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 获得的积分总数
     */
    @Select("SELECT IFNULL(SUM(points_amount), 0) FROM user_points_record WHERE user_token = #{userToken} AND record_type = 1 AND created_at BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0")
    Integer getUserEarnedPointsInTimeRange(
            @Param("userToken") String userToken,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 获取用户在指定时间段内消耗的积分总数
     * @param userToken 用户Token
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 消耗的积分总数（正数）
     */
    @Select("SELECT IFNULL(SUM(ABS(points_amount)), 0) FROM user_points_record WHERE user_token = #{userToken} AND record_type = 2 AND created_at BETWEEN #{startTime} AND #{endTime} AND is_deleted = 0")
    Integer getUserConsumedPointsInTimeRange(
            @Param("userToken") String userToken,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
}
