package com.kedish.xyhelper_fox.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Slf4j
public class MachineCodeUtils {
    private static final String PROC_CPU_INFO = "/host/proc/cpuinfo";
    private static final String SYS_DMI_PRODUCT_UUID = "/host/sys/class/dmi/id/product_uuid";
    
    public static String getMachineCode() {
        StringBuilder sb = new StringBuilder();
        
        // Read CPU ID
        String cpuId = readCpuId();
        if (StringUtils.hasText(cpuId)) {
            sb.append(cpuId);
        }
        
        // Read motherboard UUID
        String motherboardUuid = readMotherboardUuid();
        if (StringUtils.hasText(motherboardUuid)) {
            sb.append(motherboardUuid);
        }
        
        // If we couldn't get any hardware info, throw exception
        if (sb.length() == 0) {
            throw new RuntimeException("无法获取机器硬件信息，请确保正确挂载了/proc和/sys目录");
        }
        
        // Generate SHA-256 hash of the hardware info
        return generateHash(sb.toString());
    }
    
    private static String readCpuId() {
        try {
            File file = new File(PROC_CPU_INFO);
            if (!file.exists()) {
                log.warn("CPU信息文件不存在: {}", PROC_CPU_INFO);
                return null;
            }
            
            try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    // Look for CPU serial number or unique identifier
                    if (line.contains("serial") || line.contains("Serial")) {
                        return line.split(":")[1].trim();
                    }
                }
            }
        } catch (IOException e) {
            log.error("读取CPU信息失败", e);
        }
        return null;
    }
    
    private static String readMotherboardUuid() {
        try {
            File file = new File(SYS_DMI_PRODUCT_UUID);
            if (!file.exists()) {
                log.warn("主板UUID文件不存在: {}", SYS_DMI_PRODUCT_UUID);
                return null;
            }
            
            try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
                return reader.readLine();
            }
        } catch (IOException e) {
            log.error("读取主板UUID失败", e);
        }
        return null;
    }
    
    private static String generateHash(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("生成哈希值失败", e);
        }
    }
}