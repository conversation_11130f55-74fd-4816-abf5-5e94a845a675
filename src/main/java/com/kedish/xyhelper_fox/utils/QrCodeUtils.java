package com.kedish.xyhelper_fox.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;

public class QrCodeUtils {

    public static String createQRCode(String text) throws WriterException, IOException {
        int height = 300;
        int width = 300;
        // 生成二维码的BitMatrix
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        BitMatrix bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height);

        // 创建BufferedImage对象
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        image.createGraphics();
        Graphics2D graphics = (Graphics2D) image.getGraphics();
        graphics.setColor(Color.WHITE); // 背景色
        graphics.fillRect(0, 0, width, height);
        graphics.setColor(Color.BLACK); // 二维码颜色

        // 将BitMatrix绘制到BufferedImage上
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, bitMatrix.get(x, y) ? Color.BLACK.getRGB() : Color.WHITE.getRGB());
            }
        }

        // 将图像写入ByteArrayOutputStream
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(image, "png", outputStream);
        byte[] imageBytes = outputStream.toByteArray();

        // 将字节数组转换为Base64字符串
        return Base64.getEncoder().encodeToString(imageBytes);
    }

    public static void main(String[] args) {
        String text = "https://www.example.com";  // 二维码内容
        int width = 300;                           // 图片宽度
        int height = 300;                          // 图片高度

        try {
            // 生成二维码的BitMatrix
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            BitMatrix bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height);

            // 创建BufferedImage对象
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            image.createGraphics();
            Graphics2D graphics = (Graphics2D) image.getGraphics();
            graphics.setColor(Color.WHITE); // 背景色
            graphics.fillRect(0, 0, width, height);
            graphics.setColor(Color.BLACK); // 二维码颜色

            // 将BitMatrix绘制到BufferedImage上
            for (int x = 0; x < width; x++) {
                for (int y = 0; y < height; y++) {
                    image.setRGB(x, y, bitMatrix.get(x, y) ? Color.BLACK.getRGB() : Color.WHITE.getRGB());
                }
            }

            // 将图像写入ByteArrayOutputStream
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "png", outputStream);
            byte[] imageBytes = outputStream.toByteArray();

            // 将字节数组转换为Base64字符串
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);

            // 输出Base64编码的二维码图像
            System.out.println("data:image/png;base64," + base64Image);

        } catch (WriterException | IOException e) {
            System.out.println("二维码生成失败: " + e.getMessage());
        }
    }
}
