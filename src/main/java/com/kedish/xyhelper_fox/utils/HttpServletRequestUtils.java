package com.kedish.xyhelper_fox.utils;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;

public class HttpServletRequestUtils {
    public static   String getGfSessionIdFromCookie(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals("gfsessionid")) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }
}
