package com.kedish.xyhelper_fox.utils;

/**
 * Snowflake ID Generator
 *
 * A distributed unique ID generator inspired by Twitter's Snowflake.
 *
 * The ID is composed of:
 * - 1 bit: Always 0 (reserved for sign bit)
 * - 41 bits: Timestamp (milliseconds since epoch or custom epoch)
 * - 5 bits: Datacenter ID
 * - 5 bits: Worker ID
 * - 12 bits: Sequence number (incremented for IDs generated in the same millisecond)
 *
 * This provides:
 * - Roughly 69 years of IDs from the epoch time
 * - 32 datacenters
 * - 32 workers per datacenter
 * - 4096 sequence numbers per millisecond
 * - IDs that sort chronologically
 */
public class SnowflakeIdGenerator {

    // Epoch timestamp (2024-01-01T00:00:00Z) - customize as needed
    private static final long EPOCH = 1704067200000L;

    // Bit lengths for each component
    private static final long DATACENTER_ID_BITS = 5L;
    private static final long WORKER_ID_BITS = 5L;
    private static final long SEQUENCE_BITS = 12L;

    // Maximum values for each component
    private static final long MAX_DATACENTER_ID = ~(-1L << DATACENTER_ID_BITS);
    private static final long MAX_WORKER_ID = ~(-1L << WORKER_ID_BITS);
    private static final long MAX_SEQUENCE = ~(-1L << SEQUENCE_BITS);

    // Bit shift amounts for each component
    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS;
    private static final long DATACENTER_ID_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;
    private static final long TIMESTAMP_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS + DATACENTER_ID_BITS;

    // Instance variables
    private final long datacenterId;
    private final long workerId;
    private long sequence = 0L;
    private long lastTimestamp = -1L;

    /**
     * Constructor
     *
     * @param datacenterId ID of the datacenter (0-31)
     * @param workerId ID of the worker (0-31)
     */
    public SnowflakeIdGenerator(long datacenterId, long workerId) {
        // Validate datacenter ID
        if (datacenterId > MAX_DATACENTER_ID || datacenterId < 0) {
            throw new IllegalArgumentException(
                    String.format("Datacenter ID must be between 0 and %d", MAX_DATACENTER_ID));
        }

        // Validate worker ID
        if (workerId > MAX_WORKER_ID || workerId < 0) {
            throw new IllegalArgumentException(
                    String.format("Worker ID must be between 0 and %d", MAX_WORKER_ID));
        }

        this.datacenterId = datacenterId;
        this.workerId = workerId;
    }

    /**
     * Generate a unique ID
     *
     * @return a unique snowflake ID
     */
    public synchronized long nextId() {
        long timestamp = timeGen();

        // Clock moved backwards, reject requests until we're past the last timestamp
        if (timestamp < lastTimestamp) {
            throw new RuntimeException(String.format(
                    "Clock moved backwards. Refusing to generate ID for %d milliseconds",
                    lastTimestamp - timestamp));
        }

        // Same timestamp as last time, increment sequence
        if (timestamp == lastTimestamp) {
            sequence = (sequence + 1) & MAX_SEQUENCE;
            // Sequence exhausted, wait for next millisecond
            if (sequence == 0) {
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            // Different timestamp, reset sequence
            sequence = 0L;
        }

        lastTimestamp = timestamp;

        // Combine all components into a single 64-bit ID
        return ((timestamp - EPOCH) << TIMESTAMP_SHIFT) |
               (datacenterId << DATACENTER_ID_SHIFT) |
               (workerId << WORKER_ID_SHIFT) |
               sequence;
    }

    /**
     * Wait until the next millisecond
     */
    private long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    /**
     * Get current timestamp in milliseconds
     */
    private long timeGen() {
        return System.currentTimeMillis();
    }
}
