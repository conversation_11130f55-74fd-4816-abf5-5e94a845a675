package com.kedish.xyhelper_fox.utils;

import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;

import java.awt.image.BufferedImage;
import java.util.Properties;

public class CaptchaUtils {
    static Config config;

    static {
        Properties properties = new Properties();
        // 图片宽高
        properties.setProperty("kaptcha.image.width", "150");
        properties.setProperty("kaptcha.image.height", "50");
        // 字符数
        properties.setProperty("kaptcha.textproducer.char.length", "4");
        properties.setProperty("kaptcha.border","no");
        // 字体颜色 (蓝色)
        properties.setProperty("kaptcha.textproducer.font.color", "230,230,250");
        // 背景渐变颜色 (浅灰 -> 浅蓝)
//        properties.setProperty("kaptcha.background.clear.from", "white");
//        properties.setProperty("kaptcha.background.clear.to", "lightBlue");
        // 字体
        properties.setProperty("kaptcha.textproducer.font.names", "Arial,Courier");
        // 字符间距
        properties.setProperty("kaptcha.textproducer.char.space", "5");
        // 不添加噪声，保持验证码简单易读
        properties.setProperty("kaptcha.noise.impl", "com.google.code.kaptcha.impl.NoNoise");
        config = new Config(properties);
    }

    //生成四位随机数字
    public static String createCaptchaCode() {
        return String.valueOf((int) ((Math.random() * 9 + 1) * 1000));
    }

    public static BufferedImage createCaptchaImage(String code) {
        DefaultKaptcha defaultKaptcha = new DefaultKaptcha();
        defaultKaptcha.setConfig(config);
        return defaultKaptcha.createImage(code);
    }
}
