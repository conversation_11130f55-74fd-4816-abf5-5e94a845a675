package com.kedish.xyhelper_fox.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

import java.security.Key;
import java.util.Date;

public class JwtUtils {
    // 定义一个密钥，用于签名 JWT
    private static final String SECRET_KEY = "secret_fox_kedish123sdagsdarewrqweqsdagasfrewr    q";
    private static final Key key = Keys.hmacShaKeyFor(SECRET_KEY.getBytes());

    // Token 有效期，设置为1小时
    private static final long EXPIRATION_TIME = 1000 * 60 * 60 * 24 * 7;

    // 生成 JWT Token
    public static String generateToken(String username) {
        return Jwts.builder()
                .setSubject(username)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + EXPIRATION_TIME))
                .signWith(key) // 使用密钥进行签名
                .compact();
    }

    // 验证并解析 Token
    public static String extractUsername(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody()
                .getSubject();
    }

    public static boolean isTokenExpired(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody()
                .getExpiration()
                .before(new Date());
    }
}
