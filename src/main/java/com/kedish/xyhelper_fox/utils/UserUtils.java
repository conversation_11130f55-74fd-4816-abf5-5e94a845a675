package com.kedish.xyhelper_fox.utils;

import com.kedish.xyhelper_fox.repo.model.ChatgptUser;

import java.time.LocalDateTime;

public class UserUtils {

    public  static boolean isVisitor(String token) {
        return token.startsWith("visitorId_");
    }

    public static ChatgptUser getVisitor(String token) {
        ChatgptUser visitor = new ChatgptUser();
        visitor.setUserToken(token);
        visitor.setExpireTime(LocalDateTime.now().plusDays(1));
        visitor.setPlusExpireTime(LocalDateTime.now().plusDays(1));
        visitor.setIsPlus(false);
        visitor.setRemark("游客");
        visitor.setIsAdmin(false);
        visitor.setLimit(10L);
        visitor.setPer("1h");
        visitor.setUserType(0);
        visitor.setEnableClaude(false);
        visitor.setStatus(0);
        visitor.setInviteCode("");
        visitor.setInviteBy("");
        visitor.setGroupId(-1L);
        return visitor;
    }
}
