package com.kedish.xyhelper_fox.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 国际化工具类
 */
@Component
public class I18nUtils {

    private static MessageSource messageSource;

    @Autowired
    public void setMessageSource(MessageSource messageSource) {
        I18nUtils.messageSource = messageSource;
    }

    /**
     * 获取国际化消息
     * @param key 消息键
     * @return 国际化消息
     */
    public static String getMessage(String key) {
        return getMessage(key, null);
    }

    /**
     * 获取国际化消息
     * @param key 消息键
     * @param args 参数
     * @return 国际化消息
     */
    public static String getMessage(String key, Object[] args) {
        return getMessage(key, args, LocaleContextHolder.getLocale());
    }

    /**
     * 获取国际化消息
     * @param key 消息键
     * @param args 参数
     * @param locale 语言环境
     * @return 国际化消息
     */
    public static String getMessage(String key, Object[] args, Locale locale) {
        try {
            return messageSource.getMessage(key, args, locale);
        } catch (Exception e) {
            // 记录警告日志，但不抛出异常
            System.err.println("Warning: Message key '" + key + "' not found for locale: " + locale);
            return key; // 如果找不到消息，返回键值
        }
    }

    /**
     * 获取当前语言环境
     * @return 当前语言环境
     */
    public static Locale getCurrentLocale() {
        return LocaleContextHolder.getLocale();
    }

    /**
     * 判断是否为中文环境
     * @return 是否为中文环境
     */
    public static boolean isChineseLocale() {
        Locale locale = getCurrentLocale();
        return Locale.SIMPLIFIED_CHINESE.equals(locale) ||
               Locale.TRADITIONAL_CHINESE.equals(locale) ||
               "zh".equals(locale.getLanguage());
    }

    /**
     * 判断是否为英文环境
     * @return 是否为英文环境
     */
    public static boolean isEnglishLocale() {
        Locale locale = getCurrentLocale();
        return Locale.ENGLISH.equals(locale) ||
               Locale.US.equals(locale) ||
               "en".equals(locale.getLanguage());
    }

    /**
     * 获取支持的语言列表
     * @return 支持的语言列表
     */
    public static List<Map<String, String>> getSupportedLanguages() {
        List<Map<String, String>> languages = new ArrayList<>();

        Map<String, String> zh = new HashMap<>();
        zh.put("code", "zh");
        zh.put("name", "中文");
        zh.put("nativeName", "中文");
        zh.put("displayName", "简体中文");
        languages.add(zh);

        Map<String, String> en = new HashMap<>();
        en.put("code", "en");
        en.put("name", "English");
        en.put("nativeName", "English");
        en.put("displayName", "English");
        languages.add(en);

        return languages;
    }

    /**
     * 验证语言代码是否支持
     * @param langCode 语言代码
     * @return 是否支持
     */
    public static boolean isSupportedLanguage(String langCode) {
        if (!StringUtils.hasText(langCode)) {
            return false;
        }
        String code = langCode.toLowerCase();
        return "zh".equals(code) || "zh-cn".equals(code) ||
               "en".equals(code) || "en-us".equals(code);
    }

    /**
     * 根据语言代码获取Locale对象
     * @param langCode 语言代码
     * @return Locale对象
     */
    public static Locale getLocaleByCode(String langCode) {
        if (!StringUtils.hasText(langCode)) {
            return Locale.SIMPLIFIED_CHINESE;
        }

        switch (langCode.toLowerCase()) {
            case "zh":
            case "zh-cn":
                return Locale.SIMPLIFIED_CHINESE;
            case "en":
            case "en-us":
                return Locale.ENGLISH;
            default:
                return Locale.SIMPLIFIED_CHINESE;
        }
    }

    /**
     * 获取当前语言的显示名称
     * @return 当前语言显示名称
     */
    public static String getCurrentLanguageDisplayName() {
        Locale locale = getCurrentLocale();
        if (isChineseLocale()) {
            return "中文";
        } else if (isEnglishLocale()) {
            return "English";
        }
        return "中文"; // 默认
    }

    /**
     * 获取格式化的国际化消息
     * @param key 消息键
     * @param params 格式化参数
     * @return 格式化后的消息
     */
    public static String getFormattedMessage(String key, Object... params) {
        return getMessage(key, params);
    }

    /**
     * 获取指定语言的消息
     * @param key 消息键
     * @param langCode 语言代码
     * @return 国际化消息
     */
    public static String getMessageByLang(String key, String langCode) {
        Locale locale = getLocaleByCode(langCode);
        return getMessage(key, null, locale);
    }
}
