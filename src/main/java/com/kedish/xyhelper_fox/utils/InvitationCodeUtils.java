package com.kedish.xyhelper_fox.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Random;

public class InvitationCodeUtils {

    // 定义字符集：大写英文字母和数字
    private static final String CHAR_SET = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

    // 生成哈希值
    public static String generateHash(String input) {
        try {
            // 使用SHA-256哈希算法
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));

            // 转换为16进制的字符串
            StringBuilder hexString = new StringBuilder(2 * hash.length);
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("无法生成哈希", e);
        }
    }

    // 根据用户名称和用户ID生成唯一的邀请码
    public static String generateUniqueCode(String username, String userId) {
        Random random = new Random();

        // 生成唯一的输入：用户名称 + 用户ID
        String input = username + userId;

        // 生成哈希值
        String hash = generateHash(input);

        // 从哈希值中选取前4位字符（将其转化为大写英文字母或数字）
        StringBuilder invitationCode = new StringBuilder();
        for (int i = 0; i < 4; i++) {
            // 将哈希值的字符与字符集的索引对应，确保生成字符在规定的字符集中
            char selectedChar = CHAR_SET.charAt(hash.charAt(i) % CHAR_SET.length());
            invitationCode.append(selectedChar);
        }

        // 随机再选择2个字符（大写字母或数字）
        for (int i = 0; i < 2; i++) {
            invitationCode.append(CHAR_SET.charAt(random.nextInt(CHAR_SET.length())));
        }

        // 返回生成的邀请码
        return invitationCode.toString();
    }

    public static void main(String[] args) {
        String username = "ldkgdfljwerw";  // 用户名称示例
        String userId = "ldkgdfljwerw";        // 用户ID示例
        String invitationCode = generateUniqueCode(username, userId);
        System.out.println("生成的唯一邀请码: " + invitationCode);
    }
}
