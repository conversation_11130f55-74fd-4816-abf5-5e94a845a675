package com.kedish.xyhelper_fox.utils;

import java.nio.ByteBuffer;
import java.util.UUID;

public class ShortUUID {

    /**
     * 生成短UUID字符串（22字符）
     * @return 短UUID字符串
     */
    public static String generate() {
        UUID uuid = UUID.randomUUID();
        return encode(uuid);
    }

    /**
     * 将标准UUID编码为短字符串
     * @param uuid 标准UUID
     * @return 短UUID字符串
     */
    public static String encode(UUID uuid) {
        ByteBuffer bb = ByteBuffer.wrap(new byte[16]);
        bb.putLong(uuid.getMostSignificantBits());
        bb.putLong(uuid.getLeastSignificantBits());

        // 使用URL安全的Base64编码并移除末尾的"=="
        return java.util.Base64.getUrlEncoder()
                .withoutPadding()
                .encodeToString(bb.array());
    }

    /**
     * 将短UUID解码为标准UUID
     * @param shortUuid 短UUID字符串
     * @return 标准UUID
     * @throws IllegalArgumentException 如果解码失败
     */
    public static UUID decode(String shortUuid) {
        // 添加缺失的填充字符
        String paddedUuid = pad(shortUuid);

        try {
            byte[] bytes = java.util.Base64.getUrlDecoder().decode(paddedUuid);
            ByteBuffer bb = ByteBuffer.wrap(bytes);
            long mostSignificant = bb.getLong();
            long leastSignificant = bb.getLong();
            return new UUID(mostSignificant, leastSignificant);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid short UUID format", e);
        }
    }

    /**
     * 为短UUID添加必要的填充字符
     */
    private static String pad(String shortUuid) {
        int padding = 4 - (shortUuid.length() % 4);
        if (padding == 4) return shortUuid;
        return shortUuid + "=".repeat(padding);
    }

    public static void main(String[] args) {
        // 生成示例
        String shortUuid = generate();
        System.out.println("Short UUID: " + shortUuid);

        // 解码验证
        UUID originalUuid = decode(shortUuid);
        System.out.println("Original UUID: " + originalUuid);

        // 编码验证
        String encoded = encode(originalUuid);
        System.out.println("Re-encoded: " + encoded);
        System.out.println("Matches original: " + encoded.equals(shortUuid));
    }
}