package com.kedish.xyhelper_fox.utils;

import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;
import java.util.Properties;

@Slf4j
public class EmailUtils {


    @Builder
    public static class EmailConfig {
        public String host;
        public String port;
        public String username;
        public String password;
        public String from;
    }

    public static void main(String[] args) {
        // 收件人邮箱
        String to = "<EMAIL>";
        // 发件人邮箱
        String from = "<EMAIL>";
        // 发件人的 SMTP 邮件服务器（这里使用Gmail的SMTP服务器为例）
        String host = "smtp.qq.com";

        // 设置 SMTP 服务器属性
        Properties properties = System.getProperties();
        properties.put("mail.smtp.host", host);         // SMTP 服务器地址
        properties.put("mail.smtp.port", "465");        // SMTP 端口号
        properties.put("mail.smtp.ssl.enable", "true"); // 启用 SSL
        properties.put("mail.smtp.auth", "true");       // 需要身份验证

        // 获取默认的 Session 对象，并传入带有身份验证信息的 Authenticator
        Session session = Session.getInstance(properties, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                // 发件人邮箱的用户名和密码
                return new PasswordAuthentication("<EMAIL>", "issxbxytofpkbddc");
            }
        });

        try {
            // 创建一个默认的 MimeMessage 对象
            MimeMessage message = new MimeMessage(session);

            // 设置发件人
            message.setFrom(new InternetAddress(from, "云龙心软"));

            // 设置收件人
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(to));

            // 设置邮件主题
            message.setSubject("This is the Subject Line!");

            // 设置邮件内容
            message.setText("This is the actual message.");
//            message.setContent(codeHtml, "text/html");
//
            // 发送邮件
            Transport.send(message);
            System.out.println("Email sent successfully!");
        } catch (MessagingException mex) {
            mex.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public static void sendEmail(String to, String subject, String content, String type, EmailConfig config) {

        String from = config.from;
        if (from == null) {
            from = config.username;
        }
        String host = config.host;
        String port = config.port;
        String password = config.password;
        // 设置 SMTP 服务器属性
        Properties properties = System.getProperties();
        properties.put("mail.smtp.host", host);         // SMTP 服务器地址
        properties.put("mail.smtp.port", port);        // SMTP 端口号
        properties.put("mail.smtp.ssl.enable", "true"); // 启用 SSL
        properties.put("mail.smtp.auth", "true");       // 需要身份验证

        // 获取默认的 Session 对象，并传入带有身份验证信息的 Authenticator
        String finalFrom = config.username;
        Session session = Session.getInstance(properties, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                // 发件人邮箱的用户名和密码
                return new PasswordAuthentication(finalFrom, password);
            }
        });

        try {
            // 创建一个默认的 MimeMessage 对象
            MimeMessage message = new MimeMessage(session);

            // 设置发件人
            message.setFrom(new InternetAddress(config.username, from));

            // 设置收件人
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(to));

            // 设置邮件主题
            message.setSubject(subject);

            // 设置邮件内容
//            message.setText("This is the actual message.");
            message.setContent(content, type);

            // 发送邮件
            Transport.send(message);
            log.info("发送邮件成功");
//            System.out.println("Email sent successfully!");
        } catch (MessagingException mex) {
            mex.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }
}
