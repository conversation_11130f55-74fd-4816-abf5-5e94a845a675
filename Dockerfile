FROM openjdk:17-jdk-alpine

LABEL maintainer="zhoujianyu"


RUN apk add --no-cache \
fontconfig \
freetype \
libxrender \
libxtst \
libxi \
ttf-dejavu

RUN mkdir -p  /tmp
ARG JAR_FILE=target/*.jar
COPY ${JAR_FILE} /tmp/app.jar

ARG SYSTEM_VER
ENV SYSTEM_VER=${SYSTEM_VER}

# 添加授权模式参数
ARG AUTH_MODE=online
ENV AUTH_MODE=${AUTH_MODE}

# 设置 JVM 启动参数来优化内存和垃圾回收性能
ENV JAVA_OPTS="-Xms512m -Xmx1024m \
-XX:+UseParallelGC \
-XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m \
-XX:+UseStringDeduplication \
-XX:+UnlockExperimentalVMOptions \
-XX:+HeapDumpOnOutOfMemoryError \
-XX:HeapDumpPath=/app/logs/heapdump.hprof \
-XX:MaxGCPauseMillis=200 \
-XX:GCTimeRatio=4 \
-Djava.security.egd=file:/dev/./urandom \
-Dspring.profiles.active=prod"
WORKDIR /tmp
VOLUME ["/tmp/logs"]
EXPOSE 8080
ENTRYPOINT ["sh","-c", "java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -Dsystem.ver=$SYSTEM_VER -Dauth.mode=$AUTH_MODE -jar /tmp/app.jar"]
#CMD ["--spring.profiles.active=prod"]


