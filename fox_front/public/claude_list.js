function init() {
    console.log('claude init');
  
    // 加载外部脚本
    function loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
  
    // 异步加载所需的脚本
    Promise.all([
        loadScript('https://cdnjs.cloudflare.com/ajax/libs/layer/3.5.1/layer.js'),
        loadScript('https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js')
    ]).then(async () => {
        await createButtons();
    }).catch(error => {
        console.error('Failed to load scripts:', error);
    });
  
    /**
     * 创建抽屉式按钮组
     */
    async function createButtons() {
        // 获取系统配置
        let enableBuy = false;
        let enableUserInfo = false;
        const token = localStorage.getItem('token');
        
        // 获取来源URL的host
        const referrerHost = document.referrer ? new URL(document.referrer).origin : '/';
        if (token) {
            try {
                const configResponse = await fetch(referrerHost + '/api/config/get', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token
                    },
                    body: JSON.stringify(["enableBuy","enableUserInfo"])
                });
                
                if (configResponse.ok) {
                    const configData = await configResponse.json();
                    if (configData.code === 0 && configData.data) {
                        enableBuy = configData.data.enableBuy || false;
                        enableUserInfo = configData.data.enableUserInfo || false;
                    }
                }
            } catch (error) {
                console.error('获取配置失败:', error);
            }
        }
  
        
        // 检测是否为移动设备
        const isMobile = window.innerWidth <= 768;
        const isLandscape = isMobile && window.innerWidth > window.innerHeight;
        
        // 创建主容器
        const containerStyle = {
            "position": "fixed",
            "right": isMobile ? "15px" : "20px",
            "z-index": "1000"
        };
        
        // 移动端放在右上角，桌面端保持右下角
        if (isMobile) {
            containerStyle.top = "80px";
            containerStyle.paddingTop = "env(safe-area-inset-top, 0px)";
        } else {
            containerStyle.bottom = "80px";
        }
        
        // 横屏时的特殊处理
        if (isLandscape) {
            containerStyle.right = "10px";
            if (isMobile) {
                containerStyle.top = "60px";
            } else {
                containerStyle.bottom = "15px";
            }
        }
        
        const $buttonContainer = $("<div id='buttonContainer'></div>").css(containerStyle);
  
        // 创建触发按钮（主按钮）
        const $triggerButton = $("<button id='drawerTrigger'>⚡</button>").css({
            "width": "50px",
            "height": "50px",
            "border-radius": "50%",
            "background": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            "color": "#ffffff",
            "font-size": "20px",
            "border": "none",
            "cursor": "pointer",
            "box-shadow": "0 4px 12px rgba(102, 126, 234, 0.4)",
            "transition": "all 0.3s ease",
            "display": "flex",
            "align-items": "center",
            "justify-content": "center",
            "position": "relative",
            "z-index": "1001"
        });
  
        // 创建抽屉容器（功能按钮容器）
        const drawerPosition = isMobile ? "top" : "bottom";
        const drawerOffset = "60px";
        
        const $drawer = $("<div id='buttonDrawer'></div>").css({
            "position": "absolute",
            [drawerPosition]: drawerOffset,
            "right": "0",
            "display": "flex",
            "flex-direction": "column",
            "gap": "8px",
            "opacity": "0",
            "transform": isMobile ? "translateY(-20px)" : "translateY(20px)",
            "transition": "all 0.3s ease",
            "pointer-events": "none"
        });
  
        // 抽屉内按钮样式
        const drawerButtonStyle = {
            "padding": "8px 12px",
            "border-radius": "12px",
            "background": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            "color": "#ffffff",
            "font-size": "12px",
            "font-weight": "500",
            "cursor": "pointer",
            "transition": "all 0.3s ease",
            "border": "none",
            "box-shadow": "0 2px 8px rgba(102, 126, 234, 0.3)",
            "min-width": "90px",
            "text-align": "center",
            "white-space": "nowrap",
            "transform": "scale(0.8)",
            "opacity": "0"
        };
  
        // 创建返回首页按钮
        const $goHomeBtn = $("<button>🏠 返回首页</button>")
            .css(drawerButtonStyle)
            .click(() => {
                window.location.href = referrerHost;
                toggleDrawer(false);
            });
  
        // 创建购买按钮（仅在有token时显示）
        let $purchaseBtn = null;
        if (token) {
            $purchaseBtn = $("<button>💎 在线购买</button>")
                .css(drawerButtonStyle)
                .click(() => {
                    if (enableBuy) {
                        window.location.href = referrerHost + "/#/purchase";
                    } else {
                        layer.alert("请联系站长购买", {
                            icon: 0,
                            title: '提示',
                            skin: 'layui-layer-molv',
                            closeBtn: 1,
                            anim: 1,
                            btn: ['确定'],
                            yes: function(index) {
                                layer.close(index);
                            }
                        });
                    }
                    toggleDrawer(false);
                });
        }
  
        // 将功能按钮添加到抽屉中
        $drawer.append($goHomeBtn);
        
        // 如果有购买按钮，添加到抽屉中
        if ($purchaseBtn) {
            $drawer.append($purchaseBtn);
        }
  
        // 组装整个按钮容器
        $buttonContainer
            .append($drawer)
            .append($triggerButton);
  
        // 将容器添加到页面
        $("body").append($buttonContainer);
        console.log('append claude buttonContainer');
        
        // 抽屉状态管理
        let isDrawerOpen = false;
        
        // 抽屉切换函数
        function toggleDrawer(forceState = null) {
            const shouldOpen = forceState !== null ? forceState : !isDrawerOpen;
            const openTransform = 'translateY(0)';
            const closeTransform = isMobile ? 'translateY(-20px)' : 'translateY(20px)';
            
            if (shouldOpen) {
                // 展开抽屉
                $drawer.css({
                    'opacity': '1',
                    'transform': openTransform,
                    'pointer-events': 'auto'
                });
                
                // 按钮依次出现动画
                $drawer.find('button').each(function(index) {
                    const $btn = $(this);
                    setTimeout(() => {
                        $btn.css({
                            'transform': 'scale(1)',
                            'opacity': '1'
                        });
                    }, index * 100);
                });
                
                // 触发按钮旋转
                $triggerButton.css('transform', 'rotate(45deg)');
                isDrawerOpen = true;
            } else {
                // 收起抽屉
                $drawer.css({
                    'opacity': '0',
                    'transform': closeTransform,
                    'pointer-events': 'none'
                });
                
                // 重置按钮状态
                $drawer.find('button').css({
                    'transform': 'scale(0.8)',
                    'opacity': '0'
                });
                
                // 触发按钮复位
                $triggerButton.css('transform', 'rotate(0deg)');
                isDrawerOpen = false;
            }
        }
        
        // 触发按钮点击事件
        $triggerButton.click(function(e) {
            e.stopPropagation();
            toggleDrawer();
        });
        
        // 点击页面其他地方收起抽屉
        $(document).on('click', function(e) {
            if (isDrawerOpen && !$(e.target).closest('#buttonContainer').length) {
                toggleDrawer(false);
            }
        });
        
        // 添加触发按钮的悬停/触摸效果
        if (isMobile) {
            $triggerButton.on('touchstart', function() {
                const currentRotate = isDrawerOpen ? 'rotate(45deg)' : 'rotate(0deg)';
                $(this).css({
                    'transform': currentRotate + ' scale(0.9)',
                    'box-shadow': '0 2px 8px rgba(102, 126, 234, 0.6)'
                });
            }).on('touchend touchcancel', function() {
                const currentRotate = isDrawerOpen ? 'rotate(45deg)' : 'rotate(0deg)';
                $(this).css({
                    'transform': currentRotate,
                    'box-shadow': '0 4px 12px rgba(102, 126, 234, 0.4)'
                });
            });
        } else {
            $triggerButton.hover(
                function() {
                    const currentRotate = isDrawerOpen ? 'rotate(45deg)' : 'rotate(0deg)';
                    $(this).css({
                        'box-shadow': '0 6px 16px rgba(102, 126, 234, 0.5)',
                        'transform': currentRotate + ' scale(1.1)'
                    });
                },
                function() {
                    const currentRotate = isDrawerOpen ? 'rotate(45deg)' : 'rotate(0deg)';
                    $(this).css({
                        'box-shadow': '0 4px 12px rgba(102, 126, 234, 0.4)',
                        'transform': currentRotate
                    });
                }
            );
        }
        
        // 抽屉内按钮的悬停效果
        $drawer.find('button').hover(
            function() {
                $(this).css({
                    'background': 'linear-gradient(135deg, #764ba2 0%, #667eea 100%)',
                    'transform': 'scale(1.05)',
                    'box-shadow': '0 4px 12px rgba(102, 126, 234, 0.4)'
                });
            },
            function() {
                $(this).css({
                    'background': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    'transform': 'scale(1)',
                    'box-shadow': '0 2px 8px rgba(102, 126, 234, 0.3)'
                });
            }
        );
        
        // 添加窗口大小变化监听
        let resizeTimeout;
        $(window).on('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(async function() {
                const newIsMobile = window.innerWidth <= 768;
                if (newIsMobile !== isMobile) {
                    $('#buttonContainer').remove();
                    await createButtons();
                }
            }, 250);
        });
    }
  }
  
  window.addEventListener('load', function() {
    console.log('claude load');
    setTimeout(() => {
        init();
    }, 1000);
  });
  