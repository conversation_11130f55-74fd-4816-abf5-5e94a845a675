import {defineConfig} from 'vite'
import vue from '@vitejs/plugin-vue'
import svgLoader from 'vite-svg-loader'
import { viteExternalsPlugin } from 'vite-plugin-externals'

// https://vitejs.dev/config/
export default defineConfig({
    publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
    plugins: [vue(), svgLoader(), viteExternalsPlugin({
        echarts: 'echarts'
    })],
    resolve: {
        alias: {
            '@': '/src'
        }
    },
    build: {
        assetsDir: 'static_assets'
    },
   
})
