:root {
    font-family: 'v-mono','v-sans', Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;
}

a {
    font-weight: 500;
    color: #646cff;
    text-decoration: inherit;
}

a:hover {
    color: #535bf2;
}

body {
    margin: 0;
    display: flex;
    place-items: center;
    min-width: 320px;
    min-height: 100vh;
}

h1 {
    font-size: 3.2em;
    line-height: 1.1;
}

button {
    border-radius: 8px;
    border: 1px solid transparent;
    padding: 0.6em 1.2em;
    font-size: 1em;
    font-weight: 500;
    font-family: inherit;
    background-color: #1a1a1a;
    cursor: pointer;
    transition: border-color 0.25s;
}

button:hover {
    border-color: #646cff;
}


.card {
    padding: 2em;
}

#app {
    max-width: 1280px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

@media (prefers-color-scheme: light) {
    :root {
        color: #213547;
        background-color: #ffffff;
    }

    a:hover {
        color: #747bff;
    }

    button {
        background-color: #f9f9f9;
    }
}
footer{
    background-color: white;
}
/* 解决按钮黑边框bug */
.el-button:focus {
    outline: none;
}

@media (max-width: 768px) {
    .el-dialog {
        --el-dialog-width: 100%;
    }
}
html.dark{
    /* 背景颜色 */
    --el-bg-color: #141414;
    --el-bg-color-overlay: #1d1e1f;
    --el-bg-color-page: #0a0a0a;
    --el-bg-color-container: #1d1e1f;
    
    /* 文字颜色 */
    --el-text-color-primary: #E5EAF3;
    --el-text-color-regular: #CFD3DC;
    --el-text-color-secondary: #A3A6AD;
    --el-text-color-placeholder: #8D9095;
    --el-text-color-disabled: #6C6E72;
    
    /* 边框颜色 */
    --el-border-color: #4C4D4F;
    --el-border-color-light: #363637;
    --el-border-color-lighter: #2B2B2C;
    --el-border-color-dark: #636466;
    --el-border-color-extra-light: #2B2B2C;

    /* 填充颜色 */
    --el-fill-color: #303030;
    --el-fill-color-light: #262727;
    --el-fill-color-lighter: #1D1D1D;
    --el-fill-color-dark: #39393A;
    --el-fill-color-blank: #000000;
    
    /* 遮罩颜色 */
    --el-mask-color: rgba(0, 0, 0, 0.8);
    --el-mask-color-extra-light: rgba(0, 0, 0, 0.3);

    /* 组件盒子阴影 */
    --el-box-shadow: 0 12px 32px 4px rgba(0, 0, 0, 0.36), 0 8px 20px rgba(0, 0, 0, 0.72);
    --el-box-shadow-light: 0 0 12px rgba(0, 0, 0, 0.72);
    --el-box-shadow-lighter: 0 0 6px rgba(0, 0, 0, 0.72);
    --el-box-shadow-dark: 0 16px 48px 16px rgba(0, 0, 0, 0.72), 0 12px 32px rgba(0, 0, 0, 0.72);

    /* 弹出层背景 */
    --el-popup-modal-bg-color: #000000dd;
    --el-popup-modal-opacity: 0.5;

    /* 自定义组件颜色覆盖 */
    color: var(--el-text-color-primary);
    background-color: var(--el-bg-color);
}
/* 针对你之前定义的组件样式的暗色模式适配 */
html.dark footer {
    background-color: var(--el-bg-color);
}

html.dark button {
    background-color: var(--el-fill-color);
    color: var(--el-text-color-primary);
}

html.dark button:hover {
    border-color: var(--el-color-primary);
}

html.dark a {
    color: var(--el-color-primary);
}

html.dark a:hover {
    color: var(--el-color-primary-light-3);
}

html.light {
    /* 背景颜色 */
    --el-bg-color: #ffffff;
    --el-bg-color-overlay: #ffffff;
    --el-bg-color-page: #ffffff;
    --el-bg-color-container: #ffffff;
    
    /* 文字颜色 */
    --el-text-color-primary: #303133;
    --el-text-color-regular: #606266;
    --el-text-color-secondary: #909399;
    --el-text-color-placeholder: #a8abb2;
    --el-text-color-disabled: #c0c4cc;
    
    /* 边框颜色 */
    --el-border-color: #dcdfe6;
    --el-border-color-light: #e4e7ed;
    --el-border-color-lighter: #ebeef5;
    --el-border-color-extra-light: #f2f6fc;
    --el-border-color-dark: #d4d7de;
    
    /* 填充颜色 */
    --el-fill-color: #f0f2f5;
    --el-fill-color-light: #f5f7fa;
    --el-fill-color-lighter: #fafafa;
    --el-fill-color-dark: #ebedf0;
    --el-fill-color-blank: #ffffff;
    
    /* 遮罩颜色 */
    --el-mask-color: rgba(255, 255, 255, 0.9);
    --el-mask-color-extra-light: rgba(255, 255, 255, 0.3);

    /* 组件盒子阴影 */
    --el-box-shadow: 0 12px 32px 4px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.08);
    --el-box-shadow-light: 0 0 12px rgba(0, 0, 0, 0.12);
    --el-box-shadow-lighter: 0 0 6px rgba(0, 0, 0, 0.12);
    --el-box-shadow-dark: 0 16px 48px 16px rgba(0, 0, 0, 0.08), 0 12px 32px rgba(0, 0, 0, 0.12);

    /* 弹出层背景 */
    --el-popup-modal-bg-color: #ffffff;
    --el-popup-modal-opacity: 0.5;

    /* 主色调 */
    --el-color-primary: #409eff;
    --el-color-primary-light-3: #79bbff;
    --el-color-primary-light-5: #a0cfff;
    --el-color-primary-light-7: #c6e2ff;
    --el-color-primary-light-8: #d9ecff;
    --el-color-primary-light-9: #ecf5ff;
    --el-color-primary-dark-2: #337ecc;

    /* 成功色 */
    --el-color-success: #67c23a;
    --el-color-success-light-3: #95d475;
    --el-color-success-light-5: #b3e19d;
    --el-color-success-light-7: #d1edc4;
    --el-color-success-light-8: #e1f3d8;
    --el-color-success-light-9: #f0f9eb;
    --el-color-success-dark-2: #529b2e;

    /* 警告色 */
    --el-color-warning: #e6a23c;
    --el-color-warning-light-3: #eebe77;
    --el-color-warning-light-5: #f3d19e;
    --el-color-warning-light-7: #f8e3c5;
    --el-color-warning-light-8: #faecd8;
    --el-color-warning-light-9: #fdf6ec;
    --el-color-warning-dark-2: #b88230;

    /* 危险色 */
    --el-color-danger: #f56c6c;
    --el-color-danger-light-3: #f89898;
    --el-color-danger-light-5: #fab6b6;
    --el-color-danger-light-7: #fcd3d3;
    --el-color-danger-light-8: #fde2e2;
    --el-color-danger-light-9: #fef0f0;
    --el-color-danger-dark-2: #c45656;

    /* 错误色 */
    --el-color-error: #f56c6c;
    --el-color-error-light-3: #f89898;
    --el-color-error-light-5: #fab6b6;
    --el-color-error-light-7: #fcd3d3;
    --el-color-error-light-8: #fde2e2;
    --el-color-error-light-9: #fef0f0;
    --el-color-error-dark-2: #c45656;

    /* 信息色 */
    --el-color-info: #909399;
    --el-color-info-light-3: #b1b3b8;
    --el-color-info-light-5: #c8c9cc;
    --el-color-info-light-7: #dedfe0;
    --el-color-info-light-8: #e9e9eb;
    --el-color-info-light-9: #f4f4f5;
    --el-color-info-dark-2: #73767a;

    /* 自定义组件颜色覆盖 */
    color: var(--el-text-color-primary);
    background-color: var(--el-bg-color);
}

/* 针对你之前定义的组件样式的亮色模式适配 */
html.light footer {
    background-color: var(--el-bg-color);
}


html.light button:hover {
    border-color: var(--el-color-primary);
}

html.light a {
    color: var(--el-color-primary);
}

html.light a:hover {
    color: var(--el-color-primary-light-3);
}