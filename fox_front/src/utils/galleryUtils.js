/**
 * 画廊工具函数集合
 */

/**
 * 根据图片URL自动检测尺寸并生成合适的span类名
 * @param {string} imageUrl - 图片URL
 * @param {string} type - 媒体类型 ('image' | 'video')
 * @returns {Promise<string>} - span类名
 */
export async function getSmartSpan(imageUrl, type = 'image') {
  try {
    if (type === 'video') {
      // 视频默认使用横版布局
      return "md:col-span-2 md:row-span-2 sm:col-span-2 sm:row-span-2";
    }

    const dimensions = await getImageDimensions(imageUrl);
    return generateSpanFromDimensions(dimensions.width, dimensions.height);
  } catch (error) {
    console.warn('无法获取图片尺寸，使用默认布局:', error);
    return getRandomSpan();
  }
}

/**
 * 获取图片的真实尺寸
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<{width: number, height: number}>} - 图片尺寸
 */
function getImageDimensions(imageUrl) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = function() {
      resolve({
        width: this.naturalWidth,
        height: this.naturalHeight
      });
    };
    img.onerror = function() {
      reject(new Error('图片加载失败'));
    };
    img.src = imageUrl;
  });
}

/**
 * 根据图片宽高比生成合适的span类名
 * @param {number} width - 图片宽度
 * @param {number} height - 图片高度
 * @returns {string} - span类名
 */
function generateSpanFromDimensions(width, height) {
  const aspectRatio = width / height;
  
  if (aspectRatio > 1.5) {
    // 宽图片（横版）
    return "md:col-span-2 md:row-span-2 sm:col-span-2 sm:row-span-2";
  } else if (aspectRatio < 0.7) {
    // 高图片（竖版）
    return "md:col-span-1 md:row-span-3 sm:col-span-1 sm:row-span-2";
  } else {
    // 接近正方形
    return "md:col-span-1 md:row-span-2 sm:col-span-1 sm:row-span-2";
  }
}

/**
 * 预定义的span模式（当无法获取尺寸时使用）
 */
const SPAN_PATTERNS = [
  "md:col-span-1 md:row-span-2 sm:col-span-1 sm:row-span-2", // 正方形
  "md:col-span-1 md:row-span-3 sm:col-span-1 sm:row-span-2", // 竖版
  "md:col-span-2 md:row-span-2 sm:col-span-2 sm:row-span-2", // 横版
  "md:col-span-1 md:row-span-1 sm:col-span-1 sm:row-span-1", // 小卡片
  "md:col-span-2 md:row-span-3 sm:col-span-2 sm:row-span-2", // 大卡片
];

/**
 * 随机获取一个span模式（确保布局多样性）
 * @returns {string} - span类名
 */
export function getRandomSpan() {
  return SPAN_PATTERNS[Math.floor(Math.random() * SPAN_PATTERNS.length)];
}

/**
 * 智能分配span模式（避免连续相同布局）
 * @param {number} index - 当前项目索引
 * @returns {string} - span类名
 */
export function getSmartRandomSpan(index) {
  // 确保大卡片不会连续出现
  if (index % 5 === 0) {
    return "md:col-span-2 md:row-span-3 sm:col-span-2 sm:row-span-2"; // 大卡片
  } else if (index % 3 === 0) {
    return "md:col-span-2 md:row-span-2 sm:col-span-2 sm:row-span-2"; // 横版
  } else if (index % 4 === 0) {
    return "md:col-span-1 md:row-span-3 sm:col-span-1 sm:row-span-2"; // 竖版
  } else {
    return "md:col-span-1 md:row-span-2 sm:col-span-1 sm:row-span-2"; // 正方形
  }
}

/**
 * 批量处理媒体项目，自动分配span
 * @param {Array} mediaItems - 媒体项目数组
 * @returns {Promise<Array>} - 处理后的媒体项目数组
 */
export async function processMediaItems(mediaItems) {
  const processedItems = [];
  
  for (let i = 0; i < mediaItems.length; i++) {
    const item = { ...mediaItems[i] };
    
    if (!item.span) {
      try {
        // 尝试智能检测
        item.span = await getSmartSpan(item.url, item.type);
      } catch (error) {
        // 如果检测失败，使用智能随机分配
        item.span = getSmartRandomSpan(i);
      }
    }
    
    processedItems.push(item);
  }
  
  return processedItems;
}

/**
 * 根据文件名或URL猜测图片类型和可能的宽高比
 * @param {string} url - 图片URL或文件名
 * @returns {string} - 推荐的span类名
 */
export function guessSpanFromUrl(url) {
  const fileName = url.toLowerCase();
  
  // 根据常见的命名模式猜测
  if (fileName.includes('portrait') || fileName.includes('vertical')) {
    return "md:col-span-1 md:row-span-3 sm:col-span-1 sm:row-span-2";
  } else if (fileName.includes('landscape') || fileName.includes('horizontal') || fileName.includes('banner')) {
    return "md:col-span-2 md:row-span-2 sm:col-span-2 sm:row-span-2";
  } else if (fileName.includes('square') || fileName.includes('avatar')) {
    return "md:col-span-1 md:row-span-2 sm:col-span-1 sm:row-span-2";
  } else if (fileName.includes('hero') || fileName.includes('featured')) {
    return "md:col-span-2 md:row-span-3 sm:col-span-2 sm:row-span-2";
  }
  
  // 默认返回正方形布局
  return "md:col-span-1 md:row-span-2 sm:col-span-1 sm:row-span-2";
}

/**
 * 为现有的媒体项目数组批量添加span属性
 * @param {Array} items - 没有span的媒体项目数组
 * @param {boolean} useSmartDetection - 是否使用智能检测（需要网络请求）
 * @returns {Promise<Array>} - 添加了span的媒体项目数组
 */
export async function addSpanToItems(items, useSmartDetection = false) {
  if (useSmartDetection) {
    return await processMediaItems(items);
  }
  
  // 快速模式：基于索引和URL猜测
  return items.map((item, index) => ({
    ...item,
    span: item.span || guessSpanFromUrl(item.url) || getSmartRandomSpan(index)
  }));
} 