import { defineStore } from 'pinia';
import api from '../axios';
import { ElMessage } from 'element-plus'; // Import ElMessage for notifications

const useUserStore = defineStore('user', {
    state: () => ({
        token: localStorage.getItem('token') || null,
        user: null,
    }),
    getters: {
        isLoggedIn: (state) => !!state.token,
    },
    actions: {
        async login(username, password, imgId, captchaCode) {
            try {
                const response = await api.post('/api/chatGptUser/login', { username, password, imgId, captchaCode });
                if (response.status != 200 || response.data.code != 0) {
                    const msg = response.data.msg || 'Login failed. Please try again.';
                    ElMessage.error(msg); // Display an error message
                    return false;
                }
                const token = response.data.data;
                // console.log(token)
                this.setToken(token);
                await this.fetchUserInfo();
                
                const userToken = this.user.userToken
                ElMessage.success(`欢迎登录，${userToken}`); // Display a success message
                return true;
            } catch (error) {
                console.error('<PERSON><PERSON> failed:', error);
                ElMessage.error('An error occurred during login. Please try again later.'); // Display a generic error message
                return false;
            }
        },
        async loginByUserToken(userToken) {
            try {
                const response = await api.post('/api/chatGptUser/loginByUserToken', { username: userToken });
                if (response.status != 200 || response.data.code != 0) {
                    const msg = response.data.msg || 'Login failed. Please try again.';
                    ElMessage.error(msg); // Display an error message
                    return false;
                }
                const responseToken = response.data.data;
                // console.log(token)
                this.setToken(responseToken);
                await this.fetchUserInfo();
                return true;
            } catch (error) {
                console.error('Login failed:', error);
                ElMessage.error('An error occurred during login. Please try again later.'); // Display a generic error message
                return false;
            }
        },
        setToken(token) {
            this.token = token;
            localStorage.setItem('token', token);
            api.defaults.headers.common['Authorization'] = token;
        },
        async fetchUserInfo() {
            try {
                // console.log('fetchUserInfo');
                const response = await api.get('/api/chatGptUser/getChatGptUser');
                if(response.status != 200 || response.data.code != 0){
                    ElMessage.error(response.data.msg || 'Failed to fetch user info');
                    return;
                }
                this.user = response.data.data;
                console.log(this.user)
            } catch (error) {
                console.error('Failed to fetch user info:', error);
            }
        },
        logout() {
            this.token = null;
            this.user = null;
            localStorage.removeItem('token');
            delete api.defaults.headers.common['Authorization'];
            // ElMessage.success('You have successfully logged out.'); // Display a logout success message
        },
        async init(){
            if (this.isLoggedIn) {
                api.defaults.headers.common['Authorization'] = this.token;
                await this.fetchUserInfo();
                return;
            }
            // await this.fetchUserInfo();
        }
    },
});

export default useUserStore;
