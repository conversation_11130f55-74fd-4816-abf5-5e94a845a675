// stores/notificationStore.js
import { defineStore } from 'pinia'

export const useNotificationStore = defineStore('notification', {
  state: () => ({
    notification: {
      id: '',
      content: '',
      updateTime: null,
      title: '',  // 添加标题字段
      type: 'info' // 添加类型字段，可以是 info, warning, error 等
    },
    readStatus: {
      lastReadTime: null,
      notificationId: null,
      lastModifiedTime: null
    }
  }),
  
  actions: {
    setNotification(notification) {
      this.notification = notification
    },
    
    markAsRead(notificationId,lastModifiedTime) {
      this.readStatus = {
        lastReadTime: new Date().getTime(),
        notificationId,
        lastModifiedTime
      }
      localStorage.setItem('notificationReadStatus_'+notificationId, JSON.stringify(this.readStatus))
    },
    
    initReadStatus(notificationId) {
      const saved = localStorage.getItem('notificationReadStatus_'+
        notificationId
      )
      if (saved) {
        this.readStatus = JSON.parse(saved)
      }
    },
    isVisible(newLastModifiedTime) {
      if ( !this.readStatus.lastReadTime) return true
      
      // 检查是否是新通知
      if (this.readStatus.lastModifiedTime !=newLastModifiedTime) return true
      
      // 检查24小时是否已过
      const now = new Date().getTime()
      const hoursPassed = (now - this.readStatus.lastReadTime) / (1000 * 60 * 60)
      
      return hoursPassed >= 24
    }
  },
  
})