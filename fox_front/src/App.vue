<template>
  <router-view v-slot="{ Component }">
    <transition name="fade">
      <component :is="Component" />
    </transition>
  </router-view>

  <!-- <footer class="footer">
    © 2024 Your Company. All rights reserved.
  </footer> -->
</template>

<script>
import useUserStore from './store/user';
import { useDark, useToggle } from '@vueuse/core'

export default {
  name: 'App',
  components: {},
  // async created() {
  //   const userStore = useUserStore();
  //   await userStore.init()
  // }
  setup() {
    const isDark = useDark()
    const toggleDark = useToggle(isDark)
    return { isDark, toggleDark }
  }
};
</script>

<style>
:root {
  --free-color: #42b983;
  --4o-color: #409eff;
  --plus-color: #f56c6c;
  --claude-color: #f56c6c;
}

/* 页脚 */
footer {
  text-align: center;
  padding: 10px;
  background-color: #f5f5f5;
  color: #666;
  overflow: hidden;
}
</style>
