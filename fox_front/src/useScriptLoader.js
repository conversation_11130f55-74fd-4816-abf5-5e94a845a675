// useScriptLoader.js
import { ref } from 'vue'

export function useScriptLoader() {
  const loading = ref(false)
  const error = ref(null)
  const loadedScripts = ref(new Set())

  // 生成唯一的脚本ID
  const generateScriptId = (content) => {
    return btoa(encodeURIComponent(content)).slice(0, 32)
  }

  const parseScriptTags = (html) => {
    const parser = new DOMParser()
    const doc = parser.parseFromString(html, 'text/html')
    const scripts = doc.querySelectorAll('script')
    
    return Array.from(scripts).map(script => ({
      src: script.src,
      content: script.textContent,
      type: script.type || 'text/javascript'
    }))
  }

  const loadExternalScript = (src) => {
    if (loadedScripts.value.has(src)) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = src
      script.onload = () => {
        loadedScripts.value.add(src)
        resolve()
      }
      script.onerror = reject
      document.head.appendChild(script)
    })
  }

  const executeInlineScript = (content, type = 'text/javascript') => {
    const scriptId = generateScriptId(content)
    
    if (loadedScripts.value.has(scriptId)) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      try {
        const script = document.createElement('script')
        script.type = type
        script.textContent = content
        document.head.appendChild(script)
        loadedScripts.value.add(scriptId)
        resolve()
      } catch (err) {
        reject(err)
      }
    })
  }

  const loadScript = async (scriptInfo) => {
    if (scriptInfo.src) {
      return loadExternalScript(scriptInfo.src)
    } else if (scriptInfo.content) {
      return executeInlineScript(scriptInfo.content, scriptInfo.type)
    }
    return Promise.resolve()
  }

  const loadScriptTags = async (scriptString) => {
    loading.value = true
    error.value = null

    try {
      const scripts = parseScriptTags(scriptString)
      if (scripts.length === 0) {
        throw new Error('No valid scripts found')
      }

      // 按顺序执行脚本
      for (const script of scripts) {
        await loadScript(script)
      }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    loadedScripts,
    loadScriptTags
  }
}