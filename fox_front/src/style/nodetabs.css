label,
a {
  cursor: pointer;
  user-select: none;
  text-decoration: none;
  display: inline-block;
  color: inherit;
  transition: border 0.2s;
  border-bottom: 5px solid rgba(#8e44ad, 0.2);
  padding: 3px 2px;

  &:hover {
      border-bottom-color: #9b59b6;
  }
}

.layout {
  margin-top: 20px;
  display: grid;
  grid-template-rows: 50px 1fr;
  grid-template-columns: 1fr 1fr 1fr;
}

input[type="radio"] {
  display: none;
}

label.nav {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-bottom: 2px solid #8e44ad;
  user-select: none;
  transition: background 0.4s, padding-left 0.2s;
  padding-left: 0;
  border-radius: 10px;

  input[type="radio"]:checked+.page+& {
      background: #9b59b6;
      color: #ffffff;
      /* padding-left: 20px; */

      /* span {
          padding-left: 20px
      } */

      svg {
          opacity: 1;
      }
  }

  span {
      padding-left: 0px;
      position: relative;
  }

  svg {
      left: 0;
      top: -3px;
      position: absolute;
      width: 15px;
      opacity: 0;
      transition: opacity 0.2s;
  }
}

label.nav.first {
  border-bottom: 2px solid rgb(36, 212, 174);
  margin-left: 20px;
  input[type="radio"]:checked+.page+& {
      background-color: rgb(36, 212, 174);
  }
}

label.nav.second {
  border-bottom: 2px solid #79bccf;

  input[type="radio"]:checked+.page+& {
      background-color: #79bccf;
  }
}

label.nav.third {
  border-bottom: 2px solid rgb(160, 123, 230);
  margin-right: 20px;
  input[type="radio"]:checked+.page+& {
      background-color: rgb(160, 123, 230);
  }
}
@media (max-width: 768px) {
  label.nav.first {
    margin-left: 0px;

  }
  label.nav.second {
    margin-right: 0px;
  }
  label.nav.third {
    margin-right: 0px;
  }
}
.page {
  grid-column-start: 1;
  grid-row-start: 2;
  grid-column-end: span 3;
  padding: 0px 20px;
  display: flex;
  align-items: center;

  @media (max-width: 768px) {
    padding: 0px 0px;
    /* grid-column-end: span 1; */
  }
}

.page-contents>* {
  /* opacity: 0; */
  transform: translateY(20px);
  transition: opacity 0.2s, transform 0.2s;

  @for $c from 1 through 20 {
      &:nth-child(#{$c}) {
          transition-delay: 0.2s + ($c * 0.2s);
      }
  }
}

input[type="radio"]+.page {
  transition: transform 0.2s;
  transform: translateX(100%);
}

input[type="radio"]:checked+.page {
  transform: translateX(0%);

  .page-contents>* {
      opacity: 1;
      transform: translateY(0px);
  }
}

.page-contents {
  max-width: 100%;
  width: 100%;
  /* width: 500px; */
  /* margin: 0 auto; */
}

.tab-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tab-group {
  height: 48px;
  padding: 2px;
  background: rgba(0, 0, 0, 0.08);
  border-radius: 18px;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
  position: relative;
}

.slider {
  position: absolute;
  height: calc(100% - 4px);
  background: #65C7FC;
  border-radius: 16px;
  transition: all 0.3s ease;
  z-index: 1;
}

.tab-button {
  position: relative;
  z-index: 2;
  align-self: stretch;
  padding: 10px 24px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-button span {
  color: black;
  font-size: 15px;
  font-family: 'Noto Sans SC', sans-serif;
  font-weight: 500;
}

.tab-button svg {
  color: black;
}

.tab-button.active {
  background: transparent;
}

.tab-button.active span,
.tab-button.active svg {
  color: white;
  position: relative;
  z-index: 2;
}

.divider {
  width: 1px;
  height: 20px;
  background: rgba(0, 0, 0, 0.1);
}

.tab-content {
  width: 100%;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.page-contents {
  width: 100%;
}

@media screen and (max-width: 768px) {
  .tab-container {
    gap: 12px;
  }

  .tab-group {
    width: 100%;
    height: 40px;
    border-radius: 14px;
  }

  .tab-button {
    padding: 8px 12px;
    font-size: 13px;
  }

  .tab-button span {
    font-size: 13px;
  }

  .tab-button svg {
    width: 16px;
    height: 16px;
  }

  .slider {
    border-radius: 12px;
  }


  .divider {
    height: 16px;
  }
}