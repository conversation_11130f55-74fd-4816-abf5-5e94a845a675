<template>
  <div class="min-h-screen overflow-y-auto bg-gray-50 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-8 max-w-4xl">
      <!-- 控制面板 -->
      <div class="mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md">
        <h2 class="text-lg font-semibold mb-4">智能布局选项</h2>
        <div class="flex flex-wrap gap-4">
          <button 
            @click="useSmartDetection" 
            :disabled="loading"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {{ loading ? '智能检测中...' : '🔍 智能检测尺寸' }}
          </button>
          <button 
            @click="useRandomLayout" 
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            🎲 随机布局
          </button>
          <button 
            @click="useUrlGuessing" 
            class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            🤖 URL智能猜测
          </button>
        </div>
      </div>

      <!-- 画廊展示 -->
      <InteractiveBentoGallery
        :media-items="processedItems"
        title="智能布局画廊"
        description="自动处理未知尺寸的图片，创建完美布局 ✨"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import InteractiveBentoGallery from '@/components/InteractiveBentoGallery.vue'
import { 
  processMediaItems, 
  addSpanToItems, 
  getSmartRandomSpan,
  guessSpanFromUrl 
} from '@/utils/galleryUtils.js'

const loading = ref(false)
const processedItems = ref([])

// 原始数据（没有span属性）
const rawMediaItems = [
  {
    id: 1,
    type: "image",
    title: "未知尺寸风景",
    desc: "从API获取的随机图片",
    url: "https://picsum.photos/800/1200", // 竖版比例
  },
  {
    id: 2,
    type: "image", 
    title: "随机照片",
    desc: "尺寸未知的网络图片",
    url: "https://picsum.photos/1200/800", // 横版比例
  },
  {
    id: 3,
    type: "image",
    title: "正方形图片",
    desc: "接近正方形的图片",
    url: "https://picsum.photos/800/800", // 正方形
  },
  {
    id: 4,
    type: "image",
    title: "portrait_photo.jpg",
    desc: "从文件名猜测的竖版照片",
    url: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=900",
  },
  {
    id: 5,
    type: "image",
    title: "landscape_banner.jpg", 
    desc: "从文件名猜测的横版图片",
    url: "https://images.unsplash.com/photo-1506744038136-46273834b3fb?w=1200&h=600",
  },
  {
    id: 6,
    type: "video",
    title: "未知尺寸视频",
    desc: "视频文件一般使用横版布局",
    url: "https://cdn.pixabay.com/video/2016/05/31/3571-168916140_large.mp4",
  },
  {
    id: 7,
    type: "image",
    title: "hero_image.jpg",
    desc: "英雄图片通常使用大布局",
    url: "https://picsum.photos/1000/1200",
  },
  {
    id: 8,
    type: "image",
    title: "avatar_square.jpg", 
    desc: "头像通常是正方形",
    url: "https://picsum.photos/600/600",
  },
  {
    id: 9,
    type: "image",
    title: "random_pic.jpg",
    desc: "完全未知的图片",
    url: "https://picsum.photos/900/1100",
  },
  {
    id: 10,
    type: "image",
    title: "another_random.jpg",
    desc: "另一个未知尺寸图片", 
    url: "https://picsum.photos/1100/700",
  }
]

// 🔍 智能检测模式（推荐）
const useSmartDetection = async () => {
  loading.value = true
  try {
    console.log('🔍 开始智能检测图片尺寸...')
    // 这会实际加载图片获取真实尺寸
    const items = await processMediaItems(rawMediaItems)
    processedItems.value = items
    console.log('✅ 智能检测完成!')
  } catch (error) {
    console.error('智能检测失败:', error)
    useRandomLayout() // 降级到随机布局
  }
  loading.value = false
}

// 🎲 随机布局模式（快速）
const useRandomLayout = () => {
  console.log('🎲 使用随机布局模式...')
  const items = rawMediaItems.map((item, index) => ({
    ...item,
    span: getSmartRandomSpan(index)
  }))
  processedItems.value = items
  console.log('✅ 随机布局完成!')
}

// 🤖 URL猜测模式（智能快速）
const useUrlGuessing = () => {
  console.log('🤖 基于URL和文件名猜测布局...')
  const items = rawMediaItems.map((item, index) => ({
    ...item,
    span: guessSpanFromUrl(item.url) || getSmartRandomSpan(index)
  }))
  processedItems.value = items
  console.log('✅ URL猜测完成!')
}

// 组件挂载时使用默认模式
onMounted(() => {
  // 默认使用URL猜测模式（快速且相对准确）
  useUrlGuessing()
})
</script>

<style scoped>
.container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 按钮样式增强 */
button:disabled {
  cursor: not-allowed;
  transform: none !important;
}

button:not(:disabled):hover {
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* 加载状态 */
button:disabled {
  position: relative;
}

button:disabled::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 