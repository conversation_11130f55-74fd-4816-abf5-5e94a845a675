<template>
  <div class="min-h-screen overflow-y-auto bg-gray-50 dark:bg-gray-900">
    <InteractiveBentoGallery
      :media-items="mediaItems"
      title="精美画廊作品集"
      description="拖拽重排，点击预览，探索精彩瞬间 ✨"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import InteractiveBentoGallery from '@/components/InteractiveBentoGallery.vue'

// 精美的示例媒体数据
const mediaItems = ref([
  {
    id: 1,
    type: "image",
    title: "梦幻极光",
    desc: "冰岛上空绚烂的北极光舞动",
    url: "https://images.unsplash.com/photo-1531366936337-7c912a4589a7?w=800&q=80",
    span: "md:col-span-1 md:row-span-3 sm:col-span-1 sm:row-span-2",
  },
  {
    id: 2,
    type: "video",
    title: "海浪轻拍",
    desc: "宁静海滩的温柔波浪",
    url: "https://cdn.pixabay.com/video/2016/05/31/3571-168916140_large.mp4",
    span: "md:col-span-2 md:row-span-2 col-span-1 sm:col-span-2 sm:row-span-2",
  },
  {
    id: 3,
    type: "image",
    title: "樱花盛开",
    desc: "日本春日粉色樱花满枝头",
    url: "https://images.unsplash.com/photo-1522383225653-ed111181a951?w=800&q=80",
    span: "md:col-span-1 md:row-span-3 sm:col-span-2 sm:row-span-2",
  },
  {
    id: 4,
    type: "image",
    title: "星空银河",
    desc: "浩瀚星空下的银河之美",
    url: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&q=80",
    span: "md:col-span-2 md:row-span-2 sm:col-span-1 sm:row-span-2",
  },
  {
    id: 5,
    type: "video",
    title: "雨滴涟漪",
    desc: "雨滴落在平静湖面的涟漪",
    url: "https://cdn.pixabay.com/video/2021/08/30/87937-600003092_large.mp4",
    span: "md:col-span-1 md:row-span-3 sm:col-span-1 sm:row-span-2",
  },
  {
    id: 6,
    type: "image",
    title: "沙漠孤驼",
    desc: "撒哈拉沙漠中的驼队剪影",
    url: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800&q=80",
    span: "md:col-span-2 md:row-span-2 sm:col-span-1 sm:row-span-2",
  },
  {
    id: 7,
    type: "image",
    title: "雪山倒影",
    desc: "阿尔卑斯山在湖中的完美倒影",
    url: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&q=80",
    span: "md:col-span-1 md:row-span-3 sm:col-span-1 sm:row-span-2",
  },
  {
    id: 8,
    type: "image",
    title: "都市霓虹",
    desc: "东京夜晚的繁华街景",
    url: "https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=800&q=80",
    span: "md:col-span-2 md:row-span-2 sm:col-span-2 sm:row-span-2",
  },
  {
    id: 9,
    type: "image",
    title: "薰衣草田",
    desc: "普罗旺斯紫色薰衣草海洋",
    url: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=800&q=80",
    span: "md:col-span-1 md:row-span-2 sm:col-span-1 sm:row-span-2",
  },
  {
    id: 10,
    type: "video",
    title: "森林阳光",
    desc: "阳光透过茂密森林的光影",
    url: "https://cdn.pixabay.com/video/2022/10/30/137306-765748296_large.mp4",
    span: "md:col-span-1 md:row-span-3 sm:col-span-2 sm:row-span-2",
  },
  {
    id: 11,
    type: "image",
    title: "海底珊瑚",
    desc: "马尔代夫清澈海水下的珊瑚礁",
    url: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800&q=80",
    span: "md:col-span-2 md:row-span-2 sm:col-span-1 sm:row-span-2",
  },
  {
    id: 12,
    type: "image",
    title: "古堡黄昏",
    desc: "苏格兰高地古堡的黄昏时光",
    url: "https://images.unsplash.com/photo-1520637836862-4d197d17c782?w=800&q=80",
    span: "md:col-span-1 md:row-span-2 sm:col-span-1 sm:row-span-2",
  },
  {
    id: 13,
    type: "image",
    title: "咖啡时光",
    desc: "慵懒午后的咖啡与阳光",
    url: "https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=800&q=80",
    span: "md:col-span-1 md:row-span-2 sm:col-span-2 sm:row-span-1",
  },
  {
    id: 14,
    type: "video",
    title: "蝴蝶翩翩",
    desc: "花丛中翩翩起舞的蝴蝶",
    url: "https://cdn.pixabay.com/video/2019/06/25/24449-343691230_large.mp4",
    span: "md:col-span-2 md:row-span-3 sm:col-span-1 sm:row-span-2",
  },
  {
    id: 15,
    type: "image",
    title: "火烧云",
    desc: "大峡谷上空壮观的火烧云",
    url: "https://images.unsplash.com/photo-1506521781263-d8422e82f27a?w=800&q=80",
    span: "md:col-span-1 md:row-span-2 sm:col-span-1 sm:row-span-2",
  }
])
</script>

<style scoped>
/* 背景渐变动画 */
.min-h-screen {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .min-h-screen {
    background: linear-gradient(-45deg, #1a1a2e, #16213e, #0f3460, #533483);
    background-size: 400% 400%;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .min-h-screen {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 200% 200%;
    animation: gradientShift 10s ease infinite;
  }
}
</style> 