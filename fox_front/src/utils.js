export function isMobile() {
    return window.matchMedia('(max-width: 768px)').matches; // 根据屏幕宽度区分
  }
  
  export const shortcuts = [
    {
      text: '1天后',
      value: () => {
        const date = new Date()
        date.setTime(date.getTime() + 3600 * 1000 * 24)
        return date
      },
    },
    {
      text: '7天后',
      value: () => {
        const date = new Date()
        date.setTime(date.getTime() + 3600 * 1000 * 24 * 7)
        return date
      },
    },
    {
      text: '30天后',
      value: () => {
        const date = new Date()
        date.setTime(date.getTime() + 3600 * 1000 * 24 * 30)
        return date
      },
    },
    {
      text: '60天后',
      value: () => {
        const date = new Date()
        date.setTime(date.getTime() + 3600 * 1000 * 24 * 30)
        return date
      },
    },
    {
      text: '90天后',
      value: () => {
        const date = new Date()
        date.setTime(date.getTime() + 3600 * 1000 * 24 * 90)
        return date
      },
    },
    {
      text: '180天后',
      value: () => {
        const date = new Date()
        date.setTime(date.getTime() + 3600 * 1000 * 24 * 180)
        return date
      },
    },
    {
      text: '365天后',
      value: () => {
        const date = new Date()
        date.setTime(date.getTime() + 3600 * 1000 * 24 * 365)
        return date
      },
    },
  ]
