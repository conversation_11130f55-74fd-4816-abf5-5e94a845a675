/* 容器和布局类 */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.max-w-4xl {
  max-width: 56rem;
}

/* 内边距和外边距 */
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.mb-8 { margin-bottom: 2rem; }
.mt-2 { margin-top: 0.5rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }

/* 文本样式 */
.text-center { text-align: center; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

/* 背景渐变 */
.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

.text-transparent {
  color: transparent;
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

/* 颜色变量 */
:root {
  --gray-900: #111827;
  --gray-800: #1f2937;
  --gray-600: #4b5563;
  --gray-400: #9ca3af;
  --gray-200: #e5e7eb;
  --white: #ffffff;
}

.from-gray-900 { --tw-gradient-from: var(--gray-900); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(17, 24, 39, 0)); }
.via-gray-800 { --tw-gradient-via: var(--gray-800); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-via), var(--tw-gradient-to, rgba(31, 41, 55, 0)); }
.to-gray-900 { --tw-gradient-to: var(--gray-900); }

.text-gray-600 { color: var(--gray-600); }
.text-gray-400 { color: var(--gray-400); }
.text-white { color: var(--white); }

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .dark\:from-white { --tw-gradient-from: var(--white); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(255, 255, 255, 0)); }
  .dark\:via-gray-200 { --tw-gradient-via: var(--gray-200); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-via), var(--tw-gradient-to, rgba(229, 231, 235, 0)); }
  .dark\:to-white { --tw-gradient-to: var(--white); }
  .dark\:text-gray-400 { color: var(--gray-400); }
}

/* 网格布局 */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-3 {
  gap: 0.75rem;
}

.auto-rows-\[60px\] {
  grid-auto-rows: 60px;
}

/* 响应式网格 */
@media (min-width: 640px) {
  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .sm\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .sm\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .sm\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .sm\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
}

@media (min-width: 768px) {
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .md\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
  .md\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .md\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .md\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
}

/* 位置和布局 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.overflow-hidden { overflow: hidden; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }

/* Flexbox */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.flex-1 { flex: 1 1 0%; }

/* 尺寸 */
.w-full { width: 100%; }
.h-full { height: 100%; }
.w-3 { width: 0.75rem; }
.h-3 { height: 0.75rem; }
.w-6 { width: 1.5rem; }
.h-6 { height: 1.5rem; }
.w-8 { width: 2rem; }
.h-8 { height: 2rem; }
.w-9 { width: 2.25rem; }
.h-9 { height: 2.25rem; }
.w-10 { width: 2.5rem; }
.h-10 { height: 2.5rem; }

.min-h-screen { min-height: 100vh; }
.max-w-3xl { max-width: 48rem; }
.max-h-\[70vh\] { max-height: 70vh; }

/* 响应式尺寸 */
@media (min-width: 640px) {
  .sm\:max-w-\[85\%\] { max-width: 85%; }
  .sm\:h-\[90vh\] { height: 90vh; }
  .sm\:w-9 { width: 2.25rem; }
  .sm\:h-9 { height: 2.25rem; }
  .sm\:rounded-lg { border-radius: 0.5rem; }
}

@media (min-width: 768px) {
  .md\:max-w-3xl { max-width: 48rem; }
  .md\:h-\[600px\] { height: 600px; }
  .md\:w-10 { width: 2.5rem; }
  .md\:h-10 { height: 2.5rem; }
  .md\:rounded-xl { border-radius: 0.75rem; }
}

/* 对象适配 */
.object-cover { object-fit: cover; }
.object-contain { object-fit: contain; }

/* 光标 */
.cursor-pointer { cursor: pointer; }
.cursor-move { cursor: move; }
.cursor-grab { cursor: grab; }
.cursor-grabbing { cursor: grabbing; }

/* 背景和边框 */
.bg-gray-50\/50 { background-color: rgba(249, 250, 251, 0.5); }
.bg-gray-900\/20 { background-color: rgba(17, 24, 39, 0.2); }
.bg-black\/10 { background-color: rgba(0, 0, 0, 0.1); }
.bg-black\/50 { background-color: rgba(0, 0, 0, 0.5); }
.bg-black\/80 { background-color: rgba(0, 0, 0, 0.8); }
.bg-gray-200\/80 { background-color: rgba(229, 231, 235, 0.8); }
.bg-sky-400\/20 { background-color: rgba(56, 189, 248, 0.2); }
.bg-white\/20 { background-color: rgba(255, 255, 255, 0.2); }
.bg-white\/70 { background-color: rgba(255, 255, 255, 0.7); }

.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-blue-400\/30 { border-color: rgba(96, 165, 250, 0.3); }
.border-white\/30 { border-color: rgba(255, 255, 255, 0.3); }
.border-white\/70 { border-color: rgba(255, 255, 255, 0.7); }
.border-t-white { border-top-color: rgb(255, 255, 255); }

/* 阴影和效果 */
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.backdrop-blur-lg { backdrop-filter: blur(16px); }
.backdrop-blur-xl { backdrop-filter: blur(24px); }
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.blur-xl { filter: blur(24px); }

/* 过渡和动画 */
.transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }

.hover\:scale-\[1\.02\]:hover { transform: scale(1.02); }
.hover\:scale-110:hover { transform: scale(1.1); }
.hover\:bg-gray-300\/80:hover { background-color: rgba(209, 213, 219, 0.8); }
.hover\:z-20:hover { z-index: 20; }
.hover\:ring-2:hover { --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); }

.active\:scale-90:active { transform: scale(0.9); }
.active\:cursor-grabbing:active { cursor: grabbing; }

/* 环形选择器 */
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

/* Z-index */
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-50 { z-index: 50; }

/* 背景渐变 */
.bg-gradient-to-t { background-image: linear-gradient(to top, var(--tw-gradient-stops)); }
.bg-gradient-to-b { background-image: linear-gradient(to bottom, var(--tw-gradient-stops)); }

.from-transparent { --tw-gradient-from: transparent; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(0, 0, 0, 0)); }
.via-black\/40 { --tw-gradient-via: rgba(0, 0, 0, 0.4); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-via), var(--tw-gradient-to, rgba(0, 0, 0, 0)); }
.via-white\/5 { --tw-gradient-via: rgba(255, 255, 255, 0.05); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-via), var(--tw-gradient-to, rgba(255, 255, 255, 0)); }
.to-transparent { --tw-gradient-to: transparent; }
.to-white\/20 { --tw-gradient-to: rgba(255, 255, 255, 0.2); }

/* 文本颜色和透明度 */
.text-white\/80 { color: rgba(255, 255, 255, 0.8); }
.text-white\/70 { color: rgba(255, 255, 255, 0.7); }
.text-gray-700 { color: rgb(55, 65, 81); }

/* 动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 用户选择 */
.select-none {
  user-select: none;
}

.touch-none {
  touch-action: none;
}

/* 变换 */
.-translate-x-1\/2 { transform: translateX(-50%); }
.-translate-y-1\/2 { transform: translateY(-50%); }
.translate-x-1\/2 { transform: translateX(50%); }

/* 负间距 */
.-space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.-inset-2 { top: -0.5rem; right: -0.5rem; bottom: -0.5rem; left: -0.5rem; }

/* 长宽比 */
.aspect-\[16\/9\] {
  aspect-ratio: 16 / 9;
}

/* 灵活性 */
.flex-shrink-0 { flex-shrink: 0; }

/* 组 */
.group { }

/* 底部定位 */
.bottom-0 { bottom: 0; }
.bottom-4 { bottom: 1rem; }
.left-0 { left: 0; }
.left-1\/2 { left: 50%; }
.right-0 { right: 0; }
.right-2 { right: 0.5rem; }
.top-2 { top: 0.5rem; }

/* 响应式定位 */
@media (min-width: 640px) {
  .sm\:top-2\.5 { top: 0.625rem; }
  .sm\:right-2\.5 { right: 0.625rem; }
}

@media (min-width: 768px) {
  .md\:top-3 { top: 0.75rem; }
  .md\:right-3 { right: 0.75rem; }
}

/* 最大宽度响应式 */
@media (max-width: 639px) {
  .max-w-\[95\%\] { max-width: 95%; }
}

/* 高度自动 */
.h-auto { height: auto; }

/* 溢出滚动 */
.overflow-y-auto { overflow-y: auto; }

/* 圆角响应式 */
.rounded-none { border-radius: 0; }

@media (min-width: 640px) {
  .sm\:rounded-lg { border-radius: 0.5rem; }
}

@media (min-width: 768px) {
  .md\:rounded-xl { border-radius: 0.75rem; }
} 