import axios from 'axios';
import useUserStore from './store/user';
import router from './route';
import { ElMessage } from 'element-plus';
console.log(import.meta.env)
const API_BASE_URL = import.meta.env.VITE_API_HOST;
console.log(API_BASE_URL)

// 创建 Axios 实例
const api = axios.create({
  baseURL: API_BASE_URL, // 替换为你的后端 URL
  timeout: 5000, // 请求超时时间
});

api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('token');
        if (token) {
            config.headers['Authorization'] = token;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

api.interceptors.response.use(
    (response) => {
        // 如果返回的 response 是 JSON，并且 code 不等于 0，则提示错误信息
        // if (response.data && response.data.code !== 0) {
        //     ElMessage.error(response.data.msg || '请求出错');
        //     return Promise.reject(new Error(response.data.msg || '请求出错'));
        // }
        return response;
    },
    (error) => {
        if (error.response && error.response.status === 401) {
            // Token expired or invalid
            const userStore = useUserStore();
            userStore.logout();
            ElMessage.info("登录信息已过期，请重新登录");
            router.push('/login');
        } else if (error.response && error.response.status >= 500) {
            ElMessage.error("服务器内部错误，请联系管理员");
        }
        return Promise.reject(error);
    }
);

export default api;
