export default {
  // 通用
  common: {
    confirm: '确定',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    search: '搜索',
    reset: '重置',
    submit: '提交',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    loading: '加载中...',
    success: '操作成功',
    error: '操作失败',
    warning: '警告',
    info: '提示',
    close: '关闭',
    refresh: '刷新',
    export: '导出',
    import: '导入',
    copy: '复制',
    paste: '粘贴',
    cut: '剪切',
    selectAll: '全选',
    clear: '清空',
    upload: '上传',
    download: '下载',
    preview: '预览',
    print: '打印',
    settings: '设置',
    help: '帮助',
    about: '关于',
    version: '版本',
    language: '语言',
    theme: '主题',
    darkMode: '深色模式',
    lightMode: '浅色模式',
    goToLogin: '去登录',
    loginRequired: '你需要登录后才能访问',
    guestAccess: '游客访问',
    chooseAccessMethod: '请选择访问方式',
    authCodeAccess: '授权码直接使用',
    authCodeLogin: '授权码登录',
    enterAuthCode: '请输入您的授权码',
    authCodePlaceholder: '请输入授权码',
    authCodeRequired: '授权码不能为空',
    authCodeLogging: '正在使用授权码登录...',
    authCodeSuccess: '授权码登录成功！',
    authCodeFailed: '授权码登录失败: ',
    guestAccessing: '正在以游客身份访问...',
    accessRestricted: '访问受限: ',
    accessRestrictedLogin: '访问受限，请尝试登录',
    carSelectionFailed: '选车失败，请刷新重试',
    accessFailed: '访问失败: ',
    tip: '提示',
    seconds: '秒',
    termsOfService: '使用条款',
    privacyPolicy: '隐私政策'
  },

  // 导航菜单
  menu: {
    chatgpt: 'ChatGPT',
    claude: 'Claude',
    grok: 'Grok',
    drawing: '4O绘图',
    purchase: '站内购买',
    useNote: '使用说明',
    announcement: '站内公告',
    exchange: '站内兑换',
    userCenter: '用户中心',
    myWorks: '我的作品',
    backend: '后台管理',
    logout: '退出登录',
    membership: '购买会员',
    notification: '站内通知',
    changePassword: '修改密码',
    theme: '切换主题',
    clickToLogin: '点击头像登录'
  },

  // 问候语
  greetings: {
    dawn: '凌晨好',
    morning: '早上好',
    noon: '中午好',
    afternoon: '下午好',
    evening: '晚上好'
  },

  // 用户相关
  user: {
    login: '登录',
    register: '注册',
    logout: '退出登录',
    username: '用户名',
    password: '密码',
    confirmPassword: '确认密码',
    email: '邮箱',
    phone: '手机号',
    captcha: '验证码',
    rememberMe: '记住我',
    forgotPassword: '忘记密码',
    resetPassword: '重置密码',
    changePassword: '修改密码',
    oldPassword: '旧密码',
    newPassword: '新密码',
    profile: '个人资料',
    avatar: '头像',
    nickname: '昵称',
    gender: '性别',
    birthday: '生日',
    address: '地址',
    bio: '个人简介',
    loginSuccess: '登录成功',
    loginFailed: '登录失败',
    registerSuccess: '注册成功',
    registerFailed: '注册失败',
    logoutSuccess: '退出成功',
    passwordChanged: '密码修改成功',
    profileUpdated: '资料更新成功',
    emailVerification: '邮箱验证',
    sendVerificationCode: '发送验证码',
    verificationCodeSent: '验证码已发送',
    invalidCredentials: '用户名或密码错误',
    accountLocked: '账户已被锁定',
    accountExpired: '账户已过期',
    permissionDenied: '权限不足',
    memberTitle: '尊贵的会员用户',
    premiumMemberTitle: '尊贵的高级会员用户'
  },

  // 系统配置
  config: {
    systemName: '系统名称',
    systemLogo: '系统Logo',
    siteNotice: '站点公告',
    emailConfig: '邮件配置',
    smtpHost: 'SMTP服务器',
    smtpPort: 'SMTP端口',
    senderEmail: '发件人邮箱',
    emailPassword: '邮箱密码',
    emailWhiteList: '邮箱白名单',
    canRegister: '允许注册',
    registerGift: '注册赠送',
    systemMaintenance: '系统维护',
    configUpdated: '配置更新成功',
    configFailed: '配置更新失败'
  },

  // 车队管理
  car: {
    carList: '车队列表',
    carName: '车队名称',
    carStatus: '车队状态',
    carType: '车队类型',
    carLimit: '使用限制',
    carExpire: '过期时间',
    addCar: '添加车队',
    editCar: '编辑车队',
    deleteCar: '删除车队',
    carNotFound: '车队不存在',
    carAccessDenied: '车队访问被拒绝',
    carLimitExceeded: '车队使用次数已达上限',
    carAdded: '车队添加成功',
    carUpdated: '车队更新成功',
    carDeleted: '车队删除成功'
  },

  // 绘图功能
  drawing: {
    generateImage: '生成图片',
    imagePrompt: '图片描述',
    imageSize: '图片尺寸',
    imageCount: '生成数量',
    imageStyle: '图片风格',
    generating: '正在生成...',
    generateSuccess: '图片生成成功',
    generateFailed: '图片生成失败',
    downloadImage: '下载图片',
    saveImage: '保存图片',
    shareImage: '分享图片',
    deleteImage: '删除图片',
    imageHistory: '生成历史',
    noImages: '暂无图片',
    limitExceeded: '绘图次数已达上限'
  },

  // 支付相关
  payment: {
    purchase: '购买',
    price: '价格',
    discount: '折扣',
    total: '总计',
    paymentMethod: '支付方式',
    alipay: '支付宝',
    wechat: '微信支付',
    bankCard: '银行卡',
    paymentSuccess: '支付成功',
    paymentFailed: '支付失败',
    paymentPending: '支付处理中',
    refund: '退款',
    refundSuccess: '退款成功',
    refundFailed: '退款失败',
    orderNumber: '订单号',
    orderStatus: '订单状态',
    orderHistory: '订单历史'
  },

  // 激活码
  activation: {
    activationCode: '激活码',
    exchange: '兑换',
    exchangeSuccess: '兑换成功',
    exchangeFailed: '兑换失败',
    invalidCode: '激活码无效',
    usedCode: '激活码已使用',
    expiredCode: '激活码已过期',
    codeHistory: '兑换历史',
    generateCode: '生成激活码',
    batchGenerate: '批量生成',
    codeType: '激活码类型',
    codeValue: '激活码价值',
    codeExpire: '过期时间',
    enterCode: '输入兑换码',
    exchangeNow: '立即兑换',
    exchanging: '兑换中...',
    tips: {
      title: '激活码兑换',
      timeLimit: '激活码可能有时间限制，请尽快使用',
      oneTime: '每个激活码仅能使用一次',
      contact: '如遇到问题，请联系客服人员'
    }
  },

  // 文件上传
  file: {
    upload: '上传文件',
    uploadSuccess: '上传成功',
    uploadFailed: '上传失败',
    fileSize: '文件大小',
    fileType: '文件类型',
    fileName: '文件名称',
    fileNotFound: '文件不存在',
    fileTooLarge: '文件过大',
    invalidFileType: '文件类型不支持',
    selectFile: '选择文件',
    dragToUpload: '拖拽文件到此处上传',
    uploadProgress: '上传进度'
  },

  // 表单验证
  validation: {
    required: '此字段为必填项',
    email: '请输入有效的邮箱地址',
    phone: '请输入有效的手机号',
    password: '密码长度至少6位',
    confirmPassword: '两次密码输入不一致',
    minLength: '最少输入{min}个字符',
    maxLength: '最多输入{max}个字符',
    numeric: '请输入数字',
    url: '请输入有效的URL',
    date: '请输入有效的日期',
    time: '请输入有效的时间'
  },

  // 分页
  pagination: {
    total: '共 {total} 条',
    page: '第 {current} 页',
    pageSize: '每页 {size} 条',
    goto: '跳转到',
    prev: '上一页',
    next: '下一页',
    first: '首页',
    last: '末页'
  },

  // 时间相关
  time: {
    now: '刚刚',
    minutesAgo: '{minutes} 分钟前',
    hoursAgo: '{hours} 小时前',
    daysAgo: '{days} 天前',
    weeksAgo: '{weeks} 周前',
    monthsAgo: '{months} 个月前',
    yearsAgo: '{years} 年前',
    today: '今天',
    yesterday: '昨天',
    tomorrow: '明天',
    thisWeek: '本周',
    lastWeek: '上周',
    thisMonth: '本月',
    lastMonth: '上月',
    thisYear: '今年',
    lastYear: '去年'
  },

  // 状态
  status: {
    active: '激活',
    inactive: '未激活',
    enabled: '启用',
    disabled: '禁用',
    online: '在线',
    offline: '离线',
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
    expired: '已过期',
    valid: '有效',
    invalid: '无效'
  },

  // 节点类型
  nodes: {
    free: '免费节点',
    plus: 'Plus节点',
    fouro: '4O节点',
    claude: 'Claude节点',
    grok: 'Grok节点',
    recommended: '推荐',
    clearTime: '将于{time}后清理',
    neverClear: '永不清理',
    teamAvailable: '团队可用',
    teamNotAvailable: '团队不可用',
    noRecommendedNodes: '暂无推荐节点',
    selectingNode: '正在选择节点...',
    selectionFailed: '选择节点失败：',
    fetchError: '获取节点失败'
  },

  login: {
    welcome: '欢迎回来',
    emailOrUsername: '电子邮件地址/用户名*',
    password: '密码*',
    loginButton: '登录',
    noAccount: '还没有帐户？',
    register: '注册',
    or: '或者',
    forgotPassword: '忘记密码?',
    termsOfService: '使用条款',
    privacyPolicy: '隐私政策',
    loginAnnouncement: '登录公告',
    iKnow: '我知道了',
    goToHome: '返回首页',
    errors: {
      enterUsername: '请输入用户名或者邮箱',
      enterPassword: '请输入密码',
      usernameError: '用户名错误',
      passwordError: '密码错误',
      usernameTooLong: '用户名错误',
      passwordTooLong: '密码错误'
    }
  },

  register: {
    createAccount: '创建账户',
    email: '电子邮件地址*',
    verificationCode: '验证码*',
    username: '用户名*',
    password: '密码*',
    inviteCode: '邀请码(可选)',
    registerButton: '注册',
    hasAccount: '已有帐户？',
    login: '登录',
    continue: '继续',
    emailSent: '已发送验证邮件',
    resend: '重新发送',
    resendCountdown: '{seconds}秒后重新发送',
    termsOfService: '使用条款',
    privacyPolicy: '隐私政策',
    welcome: '欢迎来到本站',
    createAccount: '创建账户',
    emailPlaceholder: '请输入邮箱地址',
    verificationCodePlaceholder: '请输入验证码',
    usernamePlaceholder: '请输入用户名',
    passwordPlaceholder: '请输入密码',
    inviteCodePlaceholder: '请输入邀请码',
    registrationSuccess: '注册成功',
    errors: {
      enterEmail: '请先输入邮箱地址',
      invalidEmail: '请输入正确的邮箱地址',
      enterUsername: '请输入用户名',
      enterPassword: '请输入密码',
      verificationSent: '验证码已发送，请查收',
      verificationSuccess: '注册成功',
      noChineseAllowed: '用户名不能包含中文字符'
    }
  },
  resetPassword: {
    subtitle: '重置密码',
    email: '邮箱地址*',
    emailPlaceholder: '请输入邮箱地址',
    verificationCode: '验证码*',
    username: '用户名*',
    password: '密码*',
    inviteCode: '邀请码(可选)',
    registerButton: '注册',
    rememberPassword: '记住密码?',
    resetButton: '重置密码',
    continue: '继续',
    emailSent: '已发送验证邮件',
    resend: '重新发送',
    resendCountdown: '{seconds}秒后重新发送',
    confirmPassword: '确认密码',
    confirmPasswordPlaceholder: '请再次输入密码',
    verificationCodePlaceholder: '请输入验证码',
    newPassword: '新密码',
    newPasswordPlaceholder: '请输入新密码',
    errors: {
      enterEmail: '请输入邮箱地址',
      invalidEmail: '请输入正确的邮箱地址',
      enterUsername: '请输入用户名',
      enterPassword: '请输入密码',
      verificationSent: '验证码已发送，请查收',
      verificationSuccess: '注册成功',
      noChineseAllowed: '用户名不能包含中文字符'
    }
  }
}
