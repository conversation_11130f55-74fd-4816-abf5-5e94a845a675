<template>
  <register v-if="config.uiStyle === 'oaiLogin'"></register>
  <NewRegister v-if="config.uiStyle === 'newLogin'"></NewRegister>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import register from './oaiLogin/register.vue';
import NewRegister from './newLogin/NewRegister.vue';

import api from '@/axios'
const systemName = ref('')
const systemLogo = ref('')
const config = ref({})
const getConfig = async () => {
  const res = await api.post('/api/config/get',
      ["systemName", "systemLogo", "uiStyle"
      ])
  if (res.status !== 200) {
      ElMessage.error('获取配置失败')
      return
  }
  config.value = res.data.data
  console.log(config.value)
  systemLogo.value = config.systemLogo
  systemName.value = config.systemName
}
onMounted(() => {
  getConfig()
})
</script>

<style scoped></style>