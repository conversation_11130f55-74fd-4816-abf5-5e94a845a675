<template>
    <div class="page-container">
        <div class="gradient-header"></div>
        <div class="login-content">
            <div class="login-header">
                <div class="logo-container">
                    <el-image :src="config.systemLogo" class="logo" /> <span class="system-name">{{ config.systemName || 'ChatGPT' }}</span>
                </div>
                <h1 class="welcome-text">{{ $t('register.welcome') }}<br/>{{ $t('register.createAccount') }}</h1>
            </div>
            <div class="login-form">
                <!-- Email Input -->
                <div class="form-group">
                    <label class="form-label">{{ $t('register.email') }}</label>
                    <div class="input-container">
                        <input 
                            v-model="registerForm.email"
                            type="email"
                            :placeholder="$t('register.emailPlaceholder')"
                            class="form-input"
                            :disabled="isEmailSent"
                        />
                    </div>
                </div>

                <!-- Verification Code (shows after email is sent) -->
                <div class="form-group" v-if="isEmailSent">
                    <label class="form-label">{{ $t('register.verificationCode') }}</label>
                    <div class="input-container">
                        <input 
                            v-model="registerForm.captchaCode"
                            type="text"
                            :placeholder="$t('register.verificationCodePlaceholder')"
                            class="form-input"
                            maxlength="6"
                        />
                        <span class="resend-code" @click="handleResendCode" :class="{ disabled: countdown > 0 }">
                            {{ countdown > 0 ? $t('register.resendCountdown', { seconds: countdown }) : $t('register.resend') }}
                        </span>
                    </div>
                </div>

                <!-- Username Input (shows after email is sent) -->
                <div class="form-group" v-if="isEmailSent">
                    <label class="form-label">{{ $t('register.username') }}</label>
                    <div class="input-container">
                        <input 
                            v-model="registerForm.username"
                            type="text"
                            :placeholder="$t('register.usernamePlaceholder')"
                            class="form-input"
                            @input="validateUsername"
                        />
                    </div>
                </div>

                <!-- Password Input (shows after email is sent) -->
                <div class="form-group" v-if="isEmailSent">
                    <label class="form-label">{{ $t('register.password') }}</label>
                    <div class="input-container">
                        <input
                            v-model="registerForm.password"
                            :type="passwordVisible ? 'text' : 'password'"
                            :placeholder="$t('register.passwordPlaceholder')"
                            class="form-input"
                        />
                        <span class="eye-icon" @click="handlePasswordVisible">
                            <eye v-show="passwordVisible" />
                            <eyeclose v-show="!passwordVisible" />
                        </span>
                    </div>
                </div>

                <!-- Invite Code Input (shows after email is sent) -->
                <div class="form-group" v-if="isEmailSent">
                    <label class="form-label">{{ $t('register.inviteCode') }}</label>
                    <div class="input-container">
                        <input 
                            v-model="registerForm.inviteCode"
                            type="text"
                            :placeholder="$t('register.inviteCodePlaceholder')"
                            class="form-input"
                        />
                    </div>
                </div>

                <button class="login-button" @click="isEmailSent ? handleRegister() : getEmailCode()">
                    {{ isEmailSent ? $t('register.registerButton') : $t('register.continue') }}
                </button>

                <div class="register-link">
                    <span @click="router.push('/login')">{{ $t('register.hasAccount') }}</span>
                    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.5 13.8447L10.5 8.84473L6.5 3.84473" stroke="#02A6FF" stroke-width="1.5"/>
                    </svg>
                </div>
            </div>
        </div>
        <footer class="footer">
            <span>{{ $t('register.termsOfService') }} ｜ {{ $t('register.privacyPolicy') }}</span>
        </footer>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import chatGptLogo from '@/assets/chatgpt.svg'
import { useRouter } from 'vue-router'
import useUserStore from '@/store/user'
import { ElMessage } from 'element-plus'
import api from '@/axios'
import eye from '@/assets/eye.svg'
import eyeclose from '@/assets/eyeclose.svg'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const userStore = useUserStore()
const router = useRouter()
const isEmailSent = ref(false)
const countdown = ref(0)

const registerForm = reactive({
    username: '',
    password: '',
    inviteCode: '',
    email: '',
    captchaCode: ''
})

const passwordVisible = ref(false)
const handlePasswordVisible = () => {
    passwordVisible.value = !passwordVisible.value
}

const getEmailCode = async () => {
    if (!registerForm.email) {
        ElMessage.warning(t('register.errors.enterEmail'))
        return
    }

    if (!/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(registerForm.email)) {
        ElMessage.warning(t('register.errors.invalidEmail'))
        return
    }

    try {
        const res = await api.get('/api/chatGptUser/sendEmailCode?email=' + registerForm.email)
        if (res.data.code === 0) {
            ElMessage.success(t('register.errors.verificationSent'))
            isEmailSent.value = true
            startCountdown()
        } else {
            ElMessage.error(res.data.msg)
        }
    } catch (error) {
        ElMessage.error(error.response?.data?.msg || t('register.errors.sendingFailed'))
    }
}

const startCountdown = () => {
    countdown.value = 60
    const timer = setInterval(() => {
        if (countdown.value > 0) {
            countdown.value--
        } else {
            clearInterval(timer)
        }
    }, 1000)
}

const handleResendCode = () => {
    if (countdown.value === 0) {
        getEmailCode()
    }
}

const validateUsername = () => {
    // 检查是否包含中文字符
    if (/[\u4e00-\u9fa5]/.test(registerForm.username)) {
        ElMessage.error(t('register.errors.noChineseAllowed') || '用户名不能包含中文字符')
        return false
    }
    return true
}

const handleRegister = async () => {
    if (!registerForm.email) {
        ElMessage.error(t('register.errors.enterEmail'))
        return
    }
    if (!registerForm.captchaCode) {
        ElMessage.error(t('register.errors.enterVerificationCode'))
        return
    }
    if (!registerForm.username) {
        ElMessage.error(t('register.errors.enterUsername'))
        return
    }
    if (!validateUsername()) {
        return
    }
    if (!registerForm.password) {
        ElMessage.error(t('register.errors.enterPassword'))
        return
    }

    try {
        const res = await api.post('/api/chatGptUser/register', registerForm)
        if (res.data.code === 0) {
            ElMessage.success(t('register.errors.registrationSuccess'))
            router.push('/login')
        } else {
            ElMessage.error(res.data.msg)
        }
    } catch (error) {
        ElMessage.error(error.response?.data?.msg || t('register.errors.registrationFailed'))
    }
}

const config = ref({})
const getConfig = async () => {
    try {
        const res = await api.post('/api/config/get', [
            "systemName", "systemLogo"
        ])
        if (res.status === 200) {
            config.value = res.data.data
        } else {
            ElMessage.error(t('common.configLoadFailed'))
        }
    } catch (error) {
        console.error('Failed to load config:', error)
    }
}

onMounted(() => {
    getConfig()
})
</script>

<style scoped>
.page-container {
    width: 100%;
    min-height: 100vh;
    position: relative;
    background: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(0deg, white 0%, rgba(255, 255, 255, 1) 1%, rgba(255, 255, 255, 1) 3%, rgba(255, 255, 255, 0.99) 6%, rgba(255, 255, 255, 0.98) 11%, rgba(255, 255, 255, 0.96) 17%, rgba(255, 255, 255, 0.94) 23%, rgba(255, 255, 255, 0.90) 30%, rgba(255, 255, 255, 0.85) 38%, rgba(255, 255, 255, 0.78) 47%, rgba(255, 255, 255, 0.70) 55%, rgba(255, 255, 255, 0.61) 64%, rgba(255, 255, 255, 0.49) 73%, rgba(255, 255, 255, 0.35) 83%, rgba(255, 255, 255, 0.19) 91%, rgba(255, 255, 255, 0) 100%), linear-gradient(90deg, #65F5FF 0%, #A0BBFF 100%);
}

.gradient-header {
    width: 100%;
    height: 383px;
    position: absolute;
    top: 0;
}

.login-content {
    width: 400px;
    margin-top: 5%;
    display: flex;
    flex-direction: column;
    gap: 36px;
    z-index: 1;
}

.login-header {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
}

.login-header .logo-container {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-family: MiSans;
    font-weight: 520;
    line-height: 39.60px;
    text-align: left;
}

.welcome-text {
    color: black;
    font-size: 36px;
    font-family: MiSans;
    font-weight: 520;
    line-height: 39.60px;
    text-align: left;
    margin: 0;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-label {
    color: rgba(0, 0, 0, 0.5);
    font-size: 12px;
    font-family: MiSans;
    font-weight: 450;
    text-align: left;
}

.input-container {
    position: relative;
    width: 100%;
}

.form-input {
    width: 100%;
    height: 40px;
    padding: 11px 12px;
    background: rgba(246.74, 246.74, 246.74, 0.10);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    font-size: 14px;
    font-family: 'Noto Sans SC';
    color: black;
    outline: none;
    box-sizing: border-box;
}

.form-input::placeholder {
    color: rgba(0, 0, 0, 0.3);
}

.eye-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    display: flex;
    align-items: center;
}

.resend-code {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #02A6FF;
    font-size: 14px;
    cursor: pointer;
}

.resend-code.disabled {
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
}

.login-button {
    height: 36px;
    background: #010101;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-family: MiSans;
    font-weight: 520;
    line-height: 22.40px;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.login-button:hover {
    background: #2c2c2c;
}

.register-link {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    color: #02A6FF;
    font-size: 15px;
    font-family: 'Noto Sans SC';
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.register-link:hover {
    opacity: 0.8;
}

.footer {
    position: fixed;
    bottom: 24px;
    color: rgba(0, 0, 0, 0.3);
    font-size: 12px;
    font-family: MiSans;
    font-weight: 380;
}
.logo{
    width: 40px;
    height: 40px;
    border-radius: 50%;
}
@media (max-width: 768px) {
    .login-content {
        width: 90%;
        margin-top: 20%;
    }

    .welcome-text {
        font-size: 28px;
        line-height: 32px;
    }
}
</style> 