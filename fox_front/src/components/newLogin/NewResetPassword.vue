<template>
    <div class="page-container">
        <div class="gradient-header"></div>
        <div class="login-content">
            <div class="login-header">
                <div class="logo-container">
                    <el-image :src="config.systemLogo" class="logo" /> <span class="system-name">{{ config.systemName || 'ChatGPT' }}</span>
                </div>
                <h1 class="welcome-text">{{ $t('resetPassword.subtitle') }}</h1>    
            </div>
            <div class="login-form">
                <!-- Email Input -->
                <div class="form-group">
                    <label class="form-label">{{ $t('resetPassword.email') }}</label>
                    <div class="input-container">
                        <input 
                            v-model="resetForm.email"
                            type="email"
                            :placeholder="$t('resetPassword.emailPlaceholder')"
                            class="form-input"
                            :disabled="isEmailSent"
                        />
                    </div>
                </div>

                <!-- Verification Code (shows after email is sent) -->
                <div class="form-group" v-if="isEmailSent">
                    <label class="form-label">{{ $t('resetPassword.verificationCode') }}</label>
                    <div class="input-container">
                        <input 
                            v-model="resetForm.captchaCode"
                            type="text"
                            :placeholder="$t('resetPassword.verificationCodePlaceholder')"
                            class="form-input"
                            maxlength="6"
                        />
                        <span class="resend-code" @click="handleResendCode" :class="{ disabled: countdown > 0 }">
                            {{ countdown > 0 ? $t('resetPassword.resendCountdown', { seconds: countdown }) : $t('resetPassword.resend') }}
                        </span>
                    </div>
                </div>

                <!-- New Password Input (shows after email is sent) -->
                <div class="form-group" v-if="isEmailSent">
                    <label class="form-label">{{ $t('resetPassword.newPassword') }}</label>
                    <div class="input-container">
                        <input
                            v-model="resetForm.newPassword"
                            :type="passwordVisible ? 'text' : 'password'"
                            :placeholder="$t('resetPassword.newPasswordPlaceholder')"
                            class="form-input"
                        />
                        <span class="eye-icon" @click="handlePasswordVisible">
                            <eye v-show="passwordVisible" />
                            <eyeclose v-show="!passwordVisible" />
                        </span>
                    </div>
                </div>

                <!-- Confirm Password Input (shows after email is sent) -->
                <div class="form-group" v-if="isEmailSent">
                    <label class="form-label">{{ $t('resetPassword.confirmPassword') }}</label>
                    <div class="input-container">
                        <input
                            v-model="resetForm.confirmPassword"
                            :type="confirmPasswordVisible ? 'text' : 'password'"
                            :placeholder="$t('resetPassword.confirmPasswordPlaceholder')"
                            class="form-input"
                        />
                        <span class="eye-icon" @click="handleConfirmPasswordVisible">
                            <eye v-show="confirmPasswordVisible" />
                            <eyeclose v-show="!confirmPasswordVisible" />
                        </span>
                    </div>
                </div>

                <button class="login-button" @click="isEmailSent ? handleResetPassword() : getEmailCode()">
                    {{ isEmailSent ? $t('resetPassword.resetButton') : $t('register.continue') }}
                </button>

                <div class="register-link">
                    <span @click="router.push('/login')">{{ $t('resetPassword.rememberPassword') }}</span>
                    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.5 13.8447L10.5 8.84473L6.5 3.84473" stroke="#02A6FF" stroke-width="1.5"/>
                    </svg>
                </div>
            </div>
        </div>
        <footer class="footer">
            <span>{{ $t('common.termsOfService') }} ｜ {{ $t('common.privacyPolicy') }}</span>
        </footer>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import chatGptLogo from '@/assets/chatgpt.svg'
import { useRouter } from 'vue-router'
import useUserStore from '@/store/user'
import { ElMessage } from 'element-plus'
import api from '@/axios'
import eye from '@/assets/eye.svg'
import eyeclose from '@/assets/eyeclose.svg'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const userStore = useUserStore()
const router = useRouter()
const isEmailSent = ref(false)
const countdown = ref(0)

const resetForm = reactive({
    email: '',
    captchaCode: '',
    newPassword: '',
    confirmPassword: ''
})

const passwordVisible = ref(false)
const confirmPasswordVisible = ref(false)

const handlePasswordVisible = () => {
    passwordVisible.value = !passwordVisible.value
}

const handleConfirmPasswordVisible = () => {
    confirmPasswordVisible.value = !confirmPasswordVisible.value
}

const getEmailCode = async () => {
    if (!resetForm.email) {
        ElMessage.warning(t('resetPassword.errors.enterEmail'))
        return
    }

    if (!/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(resetForm.email)) {
        ElMessage.warning(t('resetPassword.errors.invalidEmail'))
        return
    }

    try {
        const res = await api.get('/api/chatGptUser/sendEmailCode?email=' + resetForm.email)
        if (res.data.code !== 0) {
            ElMessage.error(t('resetPassword.errors.sendingFailed'))
            return
        }
        ElMessage.success(t('resetPassword.errors.verificationSent'))
        isEmailSent.value = true
        startCountdown()
    } catch (error) {
        ElMessage.error(error.response?.data?.msg || t('resetPassword.errors.sendingFailed'))
    }
}

const startCountdown = () => {
    countdown.value = 60
    const timer = setInterval(() => {
        if (countdown.value > 0) {
            countdown.value--
        } else {
            clearInterval(timer)
        }
    }, 1000)
}

const handleResendCode = () => {
    if (countdown.value === 0) {
        getEmailCode()
    }
}

const handleResetPassword = async () => {
    if (!resetForm.email) {
        ElMessage.error(t('resetPassword.errors.enterEmail'))
        return
    }
    if (!resetForm.captchaCode) {
        ElMessage.error(t('resetPassword.errors.enterVerificationCode'))
        return
    }
    if (!resetForm.newPassword) {
        ElMessage.error(t('resetPassword.errors.enterNewPassword'))
        return
    }
    if (!resetForm.confirmPassword) {
        ElMessage.error(t('resetPassword.errors.enterConfirmPassword'))
        return
    }
    if (resetForm.newPassword !== resetForm.confirmPassword) {
        ElMessage.error(t('resetPassword.errors.passwordMismatch'))
        return
    }

    try {
        const res = await api.post('/api/chatGptUser/forgetPassword', {
            email: resetForm.email,
            captchaCode: resetForm.captchaCode,
            password: resetForm.newPassword
        })

        if (res.data.code === 0) {
            ElMessage.success(t('resetPassword.errors.resetSuccess'))
            router.push('/login')
        } else {
            ElMessage.error(res.data.msg)
        }
    } catch (error) {
        ElMessage.error(error.response?.data?.msg || t('resetPassword.errors.resetFailed'))
    }
}

const config = ref({})
const getConfig = async () => {
    try {
        const res = await api.post('/api/config/get', [
            "systemName", "systemLogo"
        ])
        if (res.status === 200) {
            config.value = res.data.data
        } else {
            ElMessage.error(t('common.configLoadFailed'))
        }
    } catch (error) {
        console.error('Failed to load config:', error)
    }
}

onMounted(() => {
    getConfig()
})
</script>

<style scoped>
.page-container {
    width: 100%;
    min-height: 100vh;
    position: relative;
    background: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(0deg, white 0%, rgba(255, 255, 255, 1) 1%, rgba(255, 255, 255, 1) 3%, rgba(255, 255, 255, 0.99) 6%, rgba(255, 255, 255, 0.98) 11%, rgba(255, 255, 255, 0.96) 17%, rgba(255, 255, 255, 0.94) 23%, rgba(255, 255, 255, 0.90) 30%, rgba(255, 255, 255, 0.85) 38%, rgba(255, 255, 255, 0.78) 47%, rgba(255, 255, 255, 0.70) 55%, rgba(255, 255, 255, 0.61) 64%, rgba(255, 255, 255, 0.49) 73%, rgba(255, 255, 255, 0.35) 83%, rgba(255, 255, 255, 0.19) 91%, rgba(255, 255, 255, 0) 100%), linear-gradient(90deg, #65F5FF 0%, #A0BBFF 100%);
}

.gradient-header {
    width: 100%;
    height: 383px;
    position: absolute;
    top: 0;
}

.login-content {
    width: 400px;
    margin-top: 5%;
    display: flex;
    flex-direction: column;
    gap: 36px;
    z-index: 1;
}

.login-header {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
}

.login-header .logo-container {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-family: MiSans;
    font-weight: 520;
    line-height: 39.60px;
    text-align: left;
}

.welcome-text {
    color: black;
    font-size: 36px;
    font-family: MiSans;
    font-weight: 520;
    line-height: 39.60px;
    text-align: left;
    margin: 0;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-label {
    color: rgba(0, 0, 0, 0.5);
    font-size: 12px;
    font-family: MiSans;
    font-weight: 450;
    text-align: left;
}

.input-container {
    position: relative;
    width: 100%;
}

.form-input {
    width: 100%;
    height: 40px;
    padding: 11px 12px;
    background: rgba(246.74, 246.74, 246.74, 0.10);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    font-size: 14px;
    font-family: 'Noto Sans SC';
    color: black;
    outline: none;
    box-sizing: border-box;
}

.form-input::placeholder {
    color: rgba(0, 0, 0, 0.3);
}

.eye-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    display: flex;
    align-items: center;
}

.resend-code {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #02A6FF;
    font-size: 14px;
    cursor: pointer;
}

.resend-code.disabled {
    color: rgba(0, 0, 0, 0.3);
    cursor: not-allowed;
}

.login-button {
    height: 36px;
    background: #010101;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-family: MiSans;
    font-weight: 520;
    line-height: 22.40px;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.login-button:hover {
    background: #2c2c2c;
}

.register-link {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    color: #02A6FF;
    font-size: 15px;
    font-family: 'Noto Sans SC';
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.register-link:hover {
    opacity: 0.8;
}

.footer {
    position: fixed;
    bottom: 24px;
    color: rgba(0, 0, 0, 0.3);
    font-size: 12px;
    font-family: MiSans;
    font-weight: 380;
}
.logo{
    width: 40px;
    height: 40px;
    border-radius: 50%;
}
@media (max-width: 768px) {
    .login-content {
        width: 90%;
        margin-top: 20%;
    }

    .welcome-text {
        font-size: 28px;
        line-height: 32px;
    }
}
</style> 