<template>
    <div class="page-container">
        <div class="gradient-header"></div>
        <div class="login-content">
            <div class="login-header">
                <div class="logo-container">
                    <el-image :src="config.systemLogo" class="logo" /> <span class="system-name">{{ config.systemName || 'ChatGPT' }}</span>
                </div>
                <h1 class="welcome-text">{{ $t('login.welcome') }}<br/>请先登录您的账号</h1>
            </div>
            <div class="login-form">
                <div class="form-group">
                    <label class="form-label">邮箱/用户名</label>
                    <div class="input-container">
                        <input 
                            v-model="loginForm.username"
                            type="text"
                            placeholder="请输入邮箱地址/用户名"
                            class="form-input"
                        />
                    </div>
                </div>
                <div class="form-group">
                    <div class="password-header">
                        <label class="form-label">密码</label>
                        <a @click="router.push('/reset-password')" class="forgot-password">忘记密码</a>
                    </div>
                    <div class="input-container">
                        <input
                            v-model="loginForm.password"
                            :type="passwordVisible ? 'text' : 'password'"
                            placeholder="请输入密码"
                            class="form-input"
                        />
                        <span class="eye-icon" @click="handlePasswordVisible">
                            <eye v-show="passwordVisible" />
                            <eyeclose v-show="!passwordVisible" />
                        </span>
                    </div>
                </div>
                <button class="login-button" @click="handleLogin">
                    登录
                </button>
                
                <!-- OAuth2登录分割线 -->
                <div class="divider">
                    <span class="divider-text">或</span>
                </div>
                
                <!-- OAuth 登录按钮容器 -->
                <div class="oauth-buttons-container" v-if="config.enableThirdLogin==='true'">
                    <!-- GitHub登录按钮 -->
                    <button class="oauth-button github-button" @click="handleGithubLogin">
                        <svg class="oauth-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                        </svg>
                        <span class="button-text">GitHub 登录</span>
                    </button>

                    <!-- Google登录按钮 -->
                    <button class="oauth-button google-button" @click="handleGoogleLogin" v-if="config.enableThirdLoginGoogle==='true'">
                        <svg class="oauth-icon" viewBox="0 0 24 24">
                            <path d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z" fill="#4285F4"/>
                        </svg>
                        <span class="button-text">Google 登录</span>
                    </button>
                </div>
                
                <div class="register-link">
                    <span @click="router.push('/register')">还没有账号，去注册</span>
                    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.5 13.8447L10.5 8.84473L6.5 3.84473" stroke="#02A6FF" stroke-width="1.5"/>
                    </svg>
                </div>
            </div>
        </div>
        <footer class="footer">
            <span>使用条款 ｜ 隐私协议</span>
        </footer>
    </div>

    <el-dialog v-model="showNotification" :title="$t('login.loginAnnouncement')">
        <div v-richText="notification.content"></div>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="handleRead">
                    {{ $t('login.iKnow') }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted,defineProps } from 'vue'
import chatGptLogo from '@/assets/chatgpt.svg'
import { useRouter } from 'vue-router'
import useUserStore from '@/store/user'
import { ElMessage } from 'element-plus'
import api from '@/axios'
import eye from '@/assets/eye.svg'
import eyeclose from '@/assets/eyeclose.svg'
import { useNotificationStore } from '../../store/notificationStore'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const notificationStore = useNotificationStore()
const userStore = useUserStore()
const router = useRouter()
const loginForm = reactive({
    username: '',
    password: '',
})

const { proxy } = getCurrentInstance()

const handleLogin = async () => {
    if (!loginForm.username) {
        ElMessage.error(t('login.errors.enterUsername'))
        return
    }
    if (!loginForm.password) {
        ElMessage.error(t('login.errors.enterPassword'))
        return
    }
    if (loginForm.username.length > 100) {
        ElMessage.error(t('login.errors.usernameTooLong'))
        return
    }
    if (loginForm.password.length > 100) {
        ElMessage.error(t('login.errors.passwordTooLong'))
        return
    }

    const logRes = await userStore.login(loginForm.username, loginForm.password)
    if (logRes) {
        router.push('/')
    }
}

const passwordVisible = ref(false)
const handlePasswordVisible = () => {
    passwordVisible.value = !passwordVisible.value
}

const showNotification = ref(false)
const notification = ref(null)

const getLoginNotification = async () => {
    const res = await api.get('/api/notification/getLatest?typeList=1')
    if (res.status == 200 && res.data.data) {
        let notifications = res.data.data
        if (notifications.length) {
            let theLatest = proxy.$getLatestNotification(notifications)
            if (notificationStore.isVisible(theLatest.updatedAt)) {
                showNotification.value = true
                notification.value = notifications[0]
            }
        }
    }
}
const config = ref({})
const getConfig = async () => {
      const res = await api.post('/api/config/get',
        ["systemName", "systemLogo", "siteNotice", "noteSite", "issuingCardSite", "claudeUrl",
          "nodeFreeName", "node4oName", "nodePlusName", "nodeClaudeName", "nodeGrokName", 'scripts', 'enableVisitor', 'enableDraw',
          "enableClaude", "enableGrok", "lyyClaudeUrl", "grokUrl", "enableUserTokenLogin", "enableFreeNode", "enableUseNote",
          "enable4oPlus", "enableThirdLogin", "enableThirdLoginGoogle"
        ])
      if (res.status !== 200) {
        ElMessage.error('获取配置失败')
        return
      }
      config.value = res.data.data
      console.log(config.value)
      
    }
onMounted(() => {
    notificationStore.initReadStatus('loginNotification')
    getLoginNotification()
    getConfig()
})

const handleRead = () => {
    showNotification.value = false
    notificationStore.markAsRead('loginNotification', notification.value.updatedAt)
}

// GitHub OAuth2登录
const handleGithubLogin = async () => {
    try {
        // 获取GitHub OAuth2配置
        const res = await api.get('/api/oauth/config?type=github')
        if (res.status === 200 && res.data.code === 0) {
            const { clientId,redirectUri, scope } = res.data.data
            
            
            // 构建GitHub授权URL
            const githubAuthUrl = `https://github.com/login/oauth/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${encodeURIComponent(scope || 'user:email')}&state=${generateRandomState()}`
            
            // 跳转到GitHub授权页面
            window.location.href = githubAuthUrl
        } else {
            ElMessage.error('获取GitHub登录配置失败')
        }
    } catch (error) {
        console.error('GitHub login error:', error)
        ElMessage.error('GitHub登录失败: ' + error.message)
    }
}

// 生成随机state参数用于安全验证
const generateRandomState = () => {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
}

// Google OAuth2登录
const handleGoogleLogin = async () => {
    try {
        // 获取Google OAuth2配置
        const res = await api.get('/api/oauth/config?type=google')
        if (res.status === 200 && res.data.code === 0) {
            const { clientId, redirectUri, scope } = res.data.data
            
            // 构建Google授权URL
            const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
                `client_id=${clientId}` +
                `&redirect_uri=${encodeURIComponent(redirectUri)}` +
                `&response_type=code` +
                `&scope=${encodeURIComponent(scope || 'openid email profile')}` +
                `&state=${generateRandomState()}` +
                `&access_type=offline`
            
            // 跳转到Google授权页面
            window.location.href = googleAuthUrl
        } else {
            ElMessage.error('获取Google登录配置失败')
        }
    } catch (error) {
        console.error('Google login error:', error)
        ElMessage.error('Google登录失败: ' + error.message)
    }
}
</script>

<style scoped>
.page-container {
    width: 100%;
    min-height: 100vh;
    position: relative;
    background: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(0deg, white 0%, rgba(255, 255, 255, 1) 1%, rgba(255, 255, 255, 1) 3%, rgba(255, 255, 255, 0.99) 6%, rgba(255, 255, 255, 0.98) 11%, rgba(255, 255, 255, 0.96) 17%, rgba(255, 255, 255, 0.94) 23%, rgba(255, 255, 255, 0.90) 30%, rgba(255, 255, 255, 0.85) 38%, rgba(255, 255, 255, 0.78) 47%, rgba(255, 255, 255, 0.70) 55%, rgba(255, 255, 255, 0.61) 64%, rgba(255, 255, 255, 0.49) 73%, rgba(255, 255, 255, 0.35) 83%, rgba(255, 255, 255, 0.19) 91%, rgba(255, 255, 255, 0) 100%), linear-gradient(90deg, #65F5FF 0%, #A0BBFF 100%);
}

.gradient-header {
    width: 100%;
    height: 383px;
    position: absolute;
    top: 0;

}

.login-content {
    width: 400px;
    margin-top: 5%;
    display: flex;
    flex-direction: column;
    gap: 36px;
    z-index: 1;
}

.login-header {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
}
.login-header .logo-container {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-family: MiSans;
    font-weight: 520;
    line-height: 39.60px;
    text-align: left;
}

.logo {
    width: 120px;
    height: 17px;
}

.welcome-text {
    color: black;
    font-size: 36px;
    font-family: MiSans;
    font-weight: 520;
    line-height: 39.60px;
    text-align: left;
    margin: 0;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
    width: 100%;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-label {
    color: rgba(0, 0, 0, 0.5);
    font-size: 12px;
    font-family: MiSans;
    font-weight: 450;
    text-align: left;
}

.password-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.forgot-password {
    color: #44BDFF;
    font-size: 12px;
    font-family: MiSans;
    font-weight: 450;
    cursor: pointer;
}

.input-container {
    position: relative;
    width: 100%;
}

.form-input {
    width: 100%;
    height: 40px;
    padding: 11px 12px;
    background: rgba(246.74, 246.74, 246.74, 0.10);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    font-size: 14px;
    font-family: 'Noto Sans SC';
    color: black;
    outline: none;
    box-sizing: border-box;  /* 添加这一行 */
}

.form-input::placeholder {
    color: rgba(0, 0, 0, 0.3);
}

.eye-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    display: flex;
    align-items: center;
}

.login-button {
    height: 36px;
    background: #010101;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-family: MiSans;
    font-weight: 520;
    line-height: 22.40px;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.login-button:hover {
    background: #2c2c2c;
}

.register-link {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    color: #02A6FF;
    font-size: 15px;
    font-family: 'Noto Sans SC';
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.register-link:hover {
    opacity: 0.8;
}

/* OAuth登录相关样式 */
.divider {
    display: flex;
    align-items: center;
    margin: 20px 0;
}

.divider::before,
.divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
}

.divider-text {
    margin: 0 16px;
    color: rgba(0, 0, 0, 0.5);
    font-size: 14px;
    font-family: MiSans;
    font-weight: 450;
}

.oauth-buttons-container {
    display: flex;
    gap: 12px;
    width: 100%;
    margin-bottom: 16px;
}

.oauth-button {
    flex: 1;
    height: 40px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    background: white;
    color: #333;
    font-size: 14px;
    font-family: MiSans;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 0 16px;
    min-width: 160px;
}

.oauth-button:only-child {
    max-width: 100%;
}

.oauth-button .button-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.oauth-button:hover {
    background: rgba(0, 0, 0, 0.02);
    border-color: rgba(0, 0, 0, 0.25);
}

.github-button:hover {
    background: #f6f8fa;
    border-color: #d0d7de;
}

.google-button {
    background: white;
    border-color: #dadce0;
}

.google-button:hover {
    background: #f8f9fa;
    border-color: #dadce0;
}

.oauth-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

@media (max-width: 768px) {
    .oauth-buttons-container {
        flex-direction: column;
        gap: 8px;
    }
    
    .oauth-button {
        width: 100%;
    }
}

.footer {
    position: fixed;
    bottom: 24px;
    color: rgba(0, 0, 0, 0.3);
    font-size: 12px;
    font-family: MiSans;
    font-weight: 380;
}
.logo{
    width: 40px;
    height: 40px;
    border-radius: 50%;
}
@media (max-width: 768px) {
    .login-content {
        width: 90%;
        margin-top: 20%;
    }

    .welcome-text {
        font-size: 28px;
        line-height: 32px;
    }
}
</style> 