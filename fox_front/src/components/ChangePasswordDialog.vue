<template>
  <el-dialog v-model="visible" :show-close="false" width="400px" class="modern-password-dialog" :modal="true" :lock-scroll="true" :close-on-click-modal="false">
    <div class="dialog-container">
      <div class="dialog-gradient-header">
        <div class="dialog-title">修改密码</div>
      </div>
      <div class="dialog-content">
        <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="0">
          <div class="form-group">
            <div class="form-label">输入旧密码</div>
            <el-form-item prop="currentPassword">
              <el-input v-model="passwordForm.currentPassword" type="password" placeholder="请输入当前密码" class="custom-input"></el-input>
            </el-form-item>
          </div>
          <div class="form-group">
            <div class="form-label">设定新密码</div>
            <el-form-item prop="newPassword">
              <el-input v-model="passwordForm.newPassword" type="password" placeholder="请输入新密码" class="custom-input"></el-input>
            </el-form-item>
          </div>
          <div class="form-group">
            <div class="form-label">确认新密码</div>
            <el-form-item prop="confirmPassword">
              <el-input v-model="passwordForm.confirmPassword" type="password" placeholder="请再次输入密码" class="custom-input"></el-input>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <div class="dialog-footer">
        <button class="cancel-btn" @click="closeDialog">取消</button>
        <button class="confirm-btn" @click="submitPasswordChange">确认</button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/axios'
import { useRouter } from 'vue-router'
import useUserStore from '@/store/user'

const props = defineProps({
  modelValue: Boolean, // for v-model:visible
  user: { type: Object, required: true }
})
const emit = defineEmits(['update:modelValue', 'success'])
const router = useRouter()
const userStore = useUserStore()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
})
const passwordFormRef = ref(null)

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else if (value === passwordForm.value.currentPassword) {
          callback(new Error('新密码不能与当前密码相同'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    },
  ],
}

function closeDialog() {
  visible.value = false
  resetForm()
}

function resetForm() {
  passwordForm.value.currentPassword = ''
  passwordForm.value.newPassword = ''
  passwordForm.value.confirmPassword = ''
  if (passwordFormRef.value) passwordFormRef.value.clearValidate()
}

async function submitPasswordChange() {
  passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      const res = await api.post(`/api/chatGptUser/updatePassword`,
        { username: props.user.userToken, oldPassword: passwordForm.value.currentPassword, newPassword: passwordForm.value.newPassword })
      if (res.status !== 200 || res.data.code != 0) {
        ElMessage.error(res.data.msg)
        return
      }
      ElMessage.success('密码修改成功,请重新登录')
      userStore.logout()
      emit('success')
      router.push('/login')
      closeDialog()
    } else {
      ElMessage.error('请检查输入')
    }
  })
}

watch(() => props.modelValue, (val) => {
  if (!val) resetForm()
})
</script>

<style scoped>
:global(.el-dialog.modern-password-dialog) {
  border-radius: 12px;
  overflow: hidden;
  padding: 0;
  margin-top: 15vh !important;
  width: 400px !important;
}

/* Mobile styles */
@media screen and (max-width: 768px) {
  :global(.el-dialog.modern-password-dialog) {
    width: 90% !important;
    margin: 15vh auto !important;
  }
}

:global(.el-dialog.modern-password-dialog .el-dialog__header) {
  display: none;
}

:global(.el-dialog.modern-password-dialog .el-dialog__body) {
  padding: 0;
}

.modern-password-dialog {
  border-radius: 12px;
  overflow: hidden;
  padding: 0;
}

.dialog-container {
  width: 100%;
  position: relative;
  background: white;
  overflow: hidden;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
}

.dialog-gradient-header {
  position: relative;
  height: 177px;
  opacity: 0.7;
  background: linear-gradient(0deg, #FFF 0%, rgba(255, 255, 255, 1.00) 0.75%, rgba(255, 255, 255, 1.00) 2.92%, rgba(255, 255, 255, 0.99) 6.37%, rgba(255, 255, 255, 0.98) 10.97%, rgba(255, 255, 255, 0.96) 16.59%, rgba(255, 255, 255, 0.94) 23.1%, rgba(255, 255, 255, 0.90) 30.37%, rgba(255, 255, 255, 0.85) 38.27%, rgba(255, 255, 255, 0.78) 46.66%, rgba(255, 255, 255, 0.70) 55.41%, rgba(255, 255, 255, 0.61) 64.39%, rgba(255, 255, 255, 0.49) 73.47%, rgba(255, 255, 255, 0.35) 82.52%, rgba(255, 255, 255, 0.19) 91.41%, rgba(255, 255, 255, 0.00) 100%), linear-gradient(90deg, #65F5FF 0%, #A0BBFF 100%);
}
.dialog-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: -130px;
}
@media screen and (max-width: 768px) {
  .dialog-gradient-header {
    height: 140px;
  }

  .dialog-title {
    font-size: 20px;
    padding: 16px;
  }

  .dialog-content {
    padding: 16px;
    margin-top: -100px;
    gap: 16px;
  }

  .form-group {
    margin-bottom: 16px;
  }

  .custom-input :deep(.el-input__wrapper) {
    padding: 6px 10px;
  }

  .custom-input :deep(.el-input__inner) {
    height: 28px;
    font-size: 13px;
  }

  .dialog-footer {
    padding: 16px;
  }

  .cancel-btn, .confirm-btn {
    height: 32px;
    padding: 8px 16px;
    font-size: 14px;
  }
}

/* Add touch-friendly styles for mobile */
@media (hover: none) and (pointer: coarse) {
  .cancel-btn, .confirm-btn {
    min-height: 44px; /* Minimum touch target size */
  }

  .custom-input :deep(.el-input__wrapper) {
    min-height: 44px;
  }
}

.dialog-title {
  color: black;
  font-size: 24px;
  font-family: MiSans;
  font-weight: 520;
  line-height: 26.4px;
  padding: 20px;
  position: relative;
  z-index: 1;
  text-align: left;
}



.form-group {
  margin-bottom: 24px;
}

.form-label {
  opacity: 0.3;
  color: black;
  font-size: 12px;
  font-family: MiSans;
  font-weight: 450;
  margin-bottom: 8px;
  text-align: left;
}

.form-input {
  height: 40px;
  padding: 11px 12px;
  background: #F7F7F7;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: black;
  font-size: 14px;
  font-family: 'Noto Sans SC';
  font-weight: 500;
  line-height: 16.8px;
  display: flex;
  align-items: center;
}

.custom-input :deep(.el-input__wrapper) {
  background: #F7F7F7;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: none !important;
  padding: 8px 12px;
}

.custom-input :deep(.el-input__inner) {
  height: 32px;
  color: black;
  font-size: 14px;
  font-family: 'Noto Sans SC';
  font-weight: 400;
  line-height: 16.8px;
}

.custom-input :deep(.el-input__inner::placeholder) {
  opacity: 0.3;
  color: black;
}

.dialog-footer {
  padding: 20px;
  background: white;
  display: flex;
  gap: 8px;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 36px;
  padding: 12px 32px;
  border-radius: 8px;
  border: none;
  font-size: 16px;
  font-family: MiSans;
  font-weight: 520;
  line-height: 22.4px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: rgba(0, 0, 0, 0.07);
  color: #010101;
}

.confirm-btn {
  background: #010101;
  color: white;
}

.cancel-btn:hover {
  background: rgba(0, 0, 0, 0.12);
}

.confirm-btn:hover {
  background: #2c2c2c;
}
</style> 