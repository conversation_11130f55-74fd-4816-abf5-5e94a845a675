<template>
  <div class="callback-container">
    <div class="callback-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-section">
        <div class="loading-spinner">
          <svg class="spinner" viewBox="0 0 50 50">
            <circle class="path" cx="25" cy="25" r="20" fill="none" stroke="#409EFF" stroke-width="2" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
              <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
              <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
            </circle>
          </svg>
        </div>
        <h2 class="loading-title">正在处理 Google 登录...</h2>
        <p class="loading-text">请稍候，我们正在验证您的身份</p>
      </div>

      <!-- 成功状态 -->
      <div v-else-if="success" class="success-section">
        <div class="success-icon">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
            <polyline points="22,4 12,14.01 9,11.01"/>
          </svg>
        </div>
        <h2 class="success-title">登录成功！</h2>
        <p class="success-text">欢迎回来，正在跳转到主页...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-section">
        <div class="error-icon">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
        </div>
        <h2 class="error-title">登录失败</h2>
        <p class="error-text">{{ error }}</p>
        <div class="error-actions">
          <button class="retry-button" @click="retryLogin">重试</button>
          <button class="back-button" @click="goToLogin">返回登录</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import  useUserStore from '@/store/user';
import { ElMessage } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import api from '@/axios';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

const loading = ref(true);
const success = ref(false);
const error = ref('');

const handleCallback = async () => {
  try {
    const code = route.query.code;
    if (!code) {
      throw new Error('未收到 Google 授权码，请重试');
    }

    const response = await api.get(`/api/oauth/google/login?code=${code}`);
    if (response.data.code === 0 && response.data.data) {
      // 登录成功，保存token
      const token = response.data.data;
      userStore.setToken(token);
      // 获取用户信息并更新store
      await userStore.fetchUserInfo();
      
      // 显示成功状态
      loading.value = false;
      success.value = true;
      
      // 延迟跳转到主页
      setTimeout(() => {
        router.push('/');
      }, 2000);
    } else {
      throw new Error(response.data.msg || '登录失败');
    }
  } catch (err) {
    console.error('Google callback error:', err);
    loading.value = false;
    
    if (err.response?.status === 400) {
      error.value = '授权码无效或已过期，请重新登录';
    } else if (err.response?.status === 500) {
      error.value = '服务器错误，请稍后重试';
    } else {
      error.value = err.message || '登录过程中发生未知错误';
    }
    ElMessage.error(error.value);
  }
};

// 重试登录
const retryLogin = () => {
  router.push('/login');
};

// 返回登录页面
const goToLogin = () => {
  router.push('/login');
};

onMounted(() => {
  handleCallback();
});
</script>

<style scoped>
.callback-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.00) 43.99%, rgba(255, 255, 255, 0.14) 55%, #FFF 100%), radial-gradient(232.08% 120.13% at 68.41% 100%, #E9EEF1 0%, #44BDFF 100%);
  font-family: inherit;
  box-sizing: border-box;
}

.callback-content {
  background: #FFF;
  border-radius: 16px;
  padding: 48px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 480px;
  width: 90%;
  border-top: 1px solid var(--el-border-color);
}

/* 加载状态样式 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  margin-bottom: 24px;
}

.spinner {
  width: 64px;
  height: 64px;
  animation: rotate 2s linear infinite;
}

.path {
  stroke: #44BDFF;
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

.loading-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.loading-text {
  font-size: 16px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* 成功状态样式 */
.success-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon {
  width: 64px;
  height: 64px;
  background: #67C23A;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  animation: successBounce 0.6s ease-out;
}

.success-icon svg {
  width: 32px;
  height: 32px;
  color: white;
}

@keyframes successBounce {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.success-title {
  font-size: 24px;
  font-weight: 600;
  color: #67C23A;
  margin: 0 0 12px 0;
}

.success-text {
  font-size: 16px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* 错误状态样式 */
.error-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.error-icon {
  width: 64px;
  height: 64px;
  background: #F56C6C;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  animation: errorShake 0.6s ease-out;
}

.error-icon svg {
  width: 32px;
  height: 32px;
  color: white;
}

@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-8px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(8px);
  }
}

.error-title {
  font-size: 24px;
  font-weight: 600;
  color: #F56C6C;
  margin: 0 0 12px 0;
}

.error-text {
  font-size: 16px;
  color: #666;
  margin: 0 0 32px 0;
  line-height: 1.5;
  max-width: 400px;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.retry-button,
.back-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.retry-button {
  background: #44BDFF;
  color: white;
}

.retry-button:hover {
  background: #66B1FF;
  transform: translateY(-1px);
}

.back-button {
  background: #F5F7FA;
  color: #606266;
}

.back-button:hover {
  background: #E4E7ED;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .callback-container {
    padding: 16px;
    box-sizing: border-box;
  }
  
  .callback-content {
    padding: 32px 24px;
    margin: 0;
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }
  
  .loading-title,
  .success-title,
  .error-title {
    font-size: 20px;
  }
  
  .loading-text,
  .success-text,
  .error-text {
    font-size: 14px;
  }
  
  .error-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .retry-button,
  .back-button {
    width: 100%;
    padding: 14px 24px;
  }
}
</style>
