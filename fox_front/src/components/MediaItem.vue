<template>
  <div :class="className" class="relative overflow-hidden">
    <!-- 视频播放 -->
    <video
      v-if="item.type === 'video'"
      ref="videoRef"
      class="w-full h-full object-cover video-element"
      :class="{ 'video-buffering': isBuffering }"
      @click="handleClick"
      playsinline
      muted
      loop
      preload="auto"
    >
      <source :src="item.url" type="video/mp4" />
    </video>
    
    <!-- 图片展示 -->
    <img
      v-else
      :src="item.url"
      :alt="item.title"
      :class="`${className} object-cover cursor-pointer`"
      @click="handleClick"
      loading="lazy"
      decoding="async"
    />
    
    <!-- 视频缓冲指示器 -->
    <div 
      v-if="item.type === 'video' && isBuffering"
      class="absolute inset-0 flex items-center justify-center bg-black/10"
    >
      <div class="w-6 h-6 border-2 border-white/30 border-t-white rounded-full animate-spin loading-spinner" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

// Props
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  className: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['click'])

// 响应式数据
const videoRef = ref(null)
const isInView = ref(false)
const isBuffering = ref(true)
const observer = ref(null)
const mounted = ref(false)

// 处理点击事件
const handleClick = (event) => {
  emit('click', event)
}

// 设置 Intersection Observer
const setupIntersectionObserver = () => {
  if (props.item.type !== 'video' || !videoRef.value) return

  const options = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1
  }

  observer.value = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      isInView.value = entry.isIntersecting
    })
  }, options)

  observer.value.observe(videoRef.value)
}

// 处理视频播放/暂停
const handleVideoPlayback = async () => {
  if (!videoRef.value || !mounted.value || props.item.type !== 'video') return

  try {
    if (isInView.value) {
      // 视频进入视口，开始播放
      if (videoRef.value.readyState >= 3) {
        // 视频已准备好播放
        isBuffering.value = false
        await videoRef.value.play()
      } else {
        // 等待视频准备就绪
        isBuffering.value = true
        await new Promise((resolve) => {
          const handleCanPlay = () => {
            if (mounted.value) {
              isBuffering.value = false
              resolve()
            }
            videoRef.value.removeEventListener('canplay', handleCanPlay)
          }
          videoRef.value.addEventListener('canplay', handleCanPlay)
        })
        
        if (mounted.value && isInView.value) {
          await videoRef.value.play()
        }
      }
    } else {
      // 视频离开视口，暂停播放
      videoRef.value.pause()
    }
  } catch (error) {
    console.warn("Video playback failed:", error)
    isBuffering.value = false
  }
}

// 清理视频资源
const cleanupVideo = () => {
  if (videoRef.value) {
    videoRef.value.pause()
    videoRef.value.removeAttribute('src')
    videoRef.value.load()
  }
}

// 监听 isInView 变化
watch(isInView, () => {
  if (props.item.type === 'video') {
    handleVideoPlayback()
  }
})

// 组件挂载
onMounted(async () => {
  mounted.value = true
  
  if (props.item.type === 'video') {
    await nextTick()
    setupIntersectionObserver()
  }
})

// 组件卸载
onUnmounted(() => {
  mounted.value = false
  
  if (observer.value) {
    observer.value.disconnect()
  }
  
  if (props.item.type === 'video') {
    cleanupVideo()
  }
})
</script>

<!-- 引入画廊组件样式 -->
<style src="@/styles/gallery.css"></style>

<style scoped>
.video-element {
  transition: opacity 0.2s ease;
  transform: translateZ(0);
  will-change: transform;
}

.video-buffering {
  opacity: 0.8;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 确保视频和图片正确填充容器 */
video,
img {
  display: block;
  width: 100%;
  height: 100%;
}

/* 优化视频性能 */
video {
  object-fit: cover;
  background-color: #000;
}

/* 图片懒加载优化 */
img {
  object-fit: cover;
  background-color: #f3f4f6;
}

img[loading="lazy"] {
  opacity: 0;
  transition: opacity 0.3s ease;
}

img[loading="lazy"]:not([src=""]) {
  opacity: 1;
}
</style> 