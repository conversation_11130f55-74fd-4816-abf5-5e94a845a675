<template>
  <!-- 移动端头部 -->
  <div class="mobile-header-container" v-if="isMobile">
    <!-- 顶部栏 -->
    <div class="mobile-header">
      <div class="mobile-header-left">
        <!-- 菜单按钮 -->
        <div class="mobile-menu-button" @click="toggleMobileMenu">
          <el-icon class="menu-icon">
            <Menu />
          </el-icon>
        </div>
        <div class="mobile-logo-section">
          <el-image :src="config.systemLogo" v-if="config.systemLogo" style="width: 32px; height: 32px;"
            class="website-logo" />
          <chatgptIcon v-else style="width: 32px; height: 32px;" />
          <span class="mobile-website-name">{{ config.systemName }}</span>
        </div>
      </div>
      <div class="mobile-header-right">
        <div class="mobile-user-section" :class="{ 'dropdown-open': showUserDropdown }" v-if="isLogined" @click="toggleUserDropdown">
          <el-avatar :size="32" :src="user.avatar || avatar" />
        </div>
        <div class="mobile-user-section" v-else @click="gotoLogin">
          <el-avatar :size="32" :src="avatar" />
          <!-- <span class="mobile-login-text">登录</span> -->
        </div>
      </div>
    </div>
    
    <!-- 移动端用户下拉菜单 -->
    <div class="mobile-user-dropdown" v-if="isLogined && showUserDropdown" @click="closeUserDropdown">
      <div class="mobile-dropdown-content" @click.stop>
        <div class="mobile-dropdown-item user-info">
          <userIcon />
          <div class="user-info-content">
            <div class="user-name">{{ user.userToken }}</div>
            <div class="user-expiry">{{ formatExpiryDate }}</div>
          </div>
        </div>
        <div class="mobile-dropdown-item" @click="passwordDialogVisible = true">
          <Lock />
          <span>{{ $t('menu.changePassword') }}</span>
        </div>
        <div class="mobile-dropdown-item logout" @click="logout">
          <logoutIcon />
          <span>{{ $t('menu.logout') }}</span>
        </div>
        <div class="mobile-dropdown-item" v-if="user.userToken == 'admin'" @click="gotoBackend">
          <panelIcon />
          <span>{{ $t('menu.backend') }}</span>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 修改密码对话框 -->
  <ChangePasswordDialog v-model="passwordDialogVisible" :user="user" @success="onPasswordChangeSuccess" />
  <UserCenter ref="userCenterRef" />
</template>

<script setup>
import { ref, defineProps, computed, reactive, getCurrentInstance, onMounted, onBeforeUnmount, defineEmits } from 'vue'
import { useDark, useToggle } from '@vueuse/core'
import chatgptIcon from '@/assets/chatgpt-white.svg'
import claudeIcon from '@/assets/claude.svg'
import grokIcon from '@/assets/grok.svg'
import drawSvg from '@/assets/draw.svg'
import purchaseIcon from '@/assets/purchase.svg'
import exchangeIcon from '@/assets/exchange.svg'
import noteIcon from '@/assets/note.svg'
import noticeIcon from '@/assets/notice.svg'
import userIcon from '@/assets/dropdown/user.svg'
import logoutIcon from '@/assets/dropdown/logout.svg'
import panelIcon from '@/assets/dropdown/panel.svg'
import avatar from '@/assets/kid.png'
import {
  Sunrise,
  Sunny,
  Moon,
  MoonNight,
  Lock,
  SwitchButton,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  Menu
} from '@element-plus/icons-vue'
import useUserStore from '@/store/user'
import { useRouter } from 'vue-router'
import moment from 'moment'
import { ElMessage, ElMessageBox } from 'element-plus'
import UserCenter from '@/components/draw/UserCenter.vue'
import ChangePasswordDialog from '@/components/ChangePasswordDialog.vue'
import { useNotificationStore } from '../store/notificationStore'
import api from '@/axios'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()
const user = ref(userStore.user)
const isLogined = ref(userStore.isLoggedIn)
const notificationStore = useNotificationStore()
let passwordFormRef = ref(null)

// 添加 emit 定义
const emit = defineEmits(['toggle-mobile-menu'])

const props = defineProps({
  config: {
    type: Object,
    required: true,
  },
})

// 添加移动端相关状态
const isMobile = ref(false)
const showUserDropdown = ref(false)

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 移动端用户下拉菜单控制
const toggleUserDropdown = () => {
  showUserDropdown.value = !showUserDropdown.value
}

const closeUserDropdown = () => {
  showUserDropdown.value = false
}

// 切换移动端菜单
const toggleMobileMenu = () => {
  emit('toggle-mobile-menu')
}

const formatExpiryDate = ref('')
const userTypeTip = ref('')
if (isLogined.value) {
  const now = moment()
  const plusExpireTime = moment(user.value.plusExpireTime)
  const expireTime = moment(user.value.expireTime)
  if (plusExpireTime.isAfter(now)) {
    formatExpiryDate.value = plusExpireTime.format('YY年MM月DD日')
  } else if (expireTime.isAfter(now)) {
    formatExpiryDate.value = expireTime.format('YY年MM月DD日')
  } else {
    formatExpiryDate.value = '免费用户'
  }
  const userType = user.value.userType
  if (userType == 2) {
    userTypeTip.value = '尊贵的会员用户'
  } else if (userType == 3) {
    userTypeTip.value = '尊贵的高级会员用户'
  }
}

// Logout function
const logout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    userStore.logout()
    ElMessage.info('已退出登录')
    isLogined.value = false
    showUserDropdown.value = false
  })
}

const passwordDialogVisible = ref(false)

onMounted(async () => {
  // 初始化移动端检测
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

// 清理事件监听器
onBeforeUnmount(() => {
  window.removeEventListener('resize', checkMobile)
})

const gotoLogin = () => {
  ElMessageBox.confirm('确定要前往登录页面吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    router.push('/login')
  })
}

const gotoBackend = () => {
  showUserDropdown.value = false
  router.push('/dashboard/today')
}

const userCenterRef = ref(null)
const gotoUserCenter = () => {
  showUserDropdown.value = false
  userCenterRef.value.openDialog()
}

// 修改密码成功回调
function onPasswordChangeSuccess() {
  showUserDropdown.value = false
}
</script>

<style scoped>
/* 移动端头部样式 */
.mobile-header-container {
  position: sticky;
  top: 0;
  z-index: 1000;
  width: 100%;
  max-width: 100vw;
  background: linear-gradient(90deg, #44BDFF 0%, #E9EEF1 100%);
  box-sizing: border-box;
}

.mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  height: 60px;
  width: 100%;
  box-sizing: border-box;
}

.mobile-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0; /* 防止 flex 子元素溢出 */
}

.mobile-logo-section {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0; /* 防止 flex 子元素溢出 */
}

.mobile-website-name {
  font-size: 16px;
  font-weight: 600;
  color: #FFF;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mobile-header-right {
  position: relative;
  flex-shrink: 0; /* 防止右侧被压缩 */
}

.mobile-user-section {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s;
}


.mobile-user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.mobile-user-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
}

.mobile-expiry {
  font-size: 12px;
  color: #666;
  line-height: 1.2;
}

.mobile-login-text {
  font-size: 14px;
  color: #000;
}

.mobile-dropdown-arrow {
  transition: transform 0.3s;
}

.mobile-dropdown-arrow.rotated {
  transform: rotate(180deg);
}

/* 移动端菜单按钮 */
.mobile-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  margin-right: 12px;
}

.mobile-menu-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.mobile-menu-button:active {
  transform: scale(0.95);
}

.mobile-menu-button .menu-icon {
  font-size: 20px;
  color: #FFF;
}

/* 移动端用户下拉菜单 */
.mobile-user-dropdown {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1200;
  background: rgba(0, 0, 0, 0.15);
}

.mobile-user-dropdown .mobile-dropdown-content {
  position: absolute;
  top: 0;
  right: 16px;
  width: 200px;
  padding: 0;
}

.mobile-dropdown-content {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 8px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  animation: slideDown 0.3s ease;
  position: absolute;
  top: 100%;
  right: 16px;
  width: 200px;
  z-index: 1;
}

.mobile-dropdown-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  color: #000;
  font-family: MiSans;
  font-size: 14px;
  font-style: normal;
  font-weight: 380;
  line-height: normal;
  align-self: stretch;
}

.mobile-dropdown-item:hover {
  background-color: #f5f7fa;
  color: #409EFF;
}

.mobile-dropdown-item.logout {
  color: #E73535;
  stroke: #E73535;
  font-family: MiSans;
  font-size: 14px;
  font-style: normal;
  font-weight: 380;
  line-height: normal;
}

.mobile-dropdown-item.logout:hover {
  background-color: #f5f7fa;
  color: #E73535;
}

.mobile-dropdown-item.logout svg {
  color: #E73535;
}

/* 移动端下拉菜单图标样式 */
.mobile-dropdown-item .el-icon,
.mobile-dropdown-item svg {
  margin-right: 0;
  font-size: 16px;
  vertical-align: middle;
  width: 16px;
  height: 16px;
}

/* 分割线样式 */
.mobile-dropdown-item:has(+ .mobile-dropdown-item.logout) {
  margin-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 12px;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.website-logo {
  border-radius: 50%;
}

@media (min-width: 769px) {
  .mobile-header-container {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .password-dialog .el-dialog__body {
    padding: 0;
  }

  .password-dialog {
    width: 100%;
    --el-dialog-width: 90%;
  }

  :deep(.el-dialog) {
    --el-dialog-width: 90%;
  }
  
  /* 移动端下拉菜单调整 */
  .mobile-dropdown-content {
    min-width: 200px;
  }
  
  .mobile-dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/* 桌面端头部样式 */
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-radius: 10px;
  gap: 20px;
}

.left-section {
  display: flex;
  gap: 20px;
  align-items: center;
}

.right-section {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
}

:deep(.el-icon) {
  font-size: 25px;
}



.avatar-section {
  cursor: pointer;
}

.el-dropdown-item [class^="el-icon"] {
  margin-right: 8px;
  vertical-align: middle;
}


.password-dialog .dialog-content {
  padding: 10px 0;
}

.password-dialog .el-dialog__body {
  padding: 20px 20px 10px 20px;
}

.website-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.website-left {
  flex: 1;
  text-align: center;
  padding: 20px;
  border-radius: 15px;
  background-color: var(--el-bg-color-container);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.website-logo {
  border-radius: 50%;
}

.website-name {
  font-size: 30px;
  font-weight: bold;
}

@media (min-width: 769px) {
  .website-info {
    display: none;
  }
  
  .mobile-header {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .header-actions {
    display: none !important;
  }

  .left-section {
    flex-direction: column;
    align-items: end;
    width: 100%;
  }



  .password-dialog .el-dialog__body {
    padding: 0;
  }

  .password-dialog {
    width: 100%;
    --el-dialog-width: 90%;
  }

  :deep(.el-dialog) {
    --el-dialog-width: 90%;
  }
}

.mobile-dropdown-item.user-info {
  cursor: default;
}

.mobile-dropdown-item.user-info:hover {
  background-color: transparent;
  color: inherit;
}

.user-info-content {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
}

.user-name {
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.user-expiry {
  font-size: 12px;
  color: #666;
}
</style>