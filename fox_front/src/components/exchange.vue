<template>
    <div class="redeem-container">
      <el-card class="redeem-card">
        <div class="card-header">
          <h2>激活码兑换</h2>
          <p class="subtitle">请输入您的激活码进行兑换</p>
        </div>
        
        <div class="input-section">
          <el-input
            v-model="activationCode"
            placeholder="请输入激活码"
            :maxlength="64"
            show-word-limit
            @keyup.enter="handleRedeem"
          >
            <template #prefix>
              <el-icon><Key /></el-icon>
            </template>
          </el-input>
          
          <el-button 
            type="primary" 
            :loading="loading"
            @click="handleRedeem"
            class="redeem-button"
          >
            立即兑换
          </el-button>
        </div>
  
        <div class="tips-section">
          <h3>温馨提示：</h3>
          <ul>
            <li>激活码可能有时间限制，请尽快使用</li>
            <li>每个激活码仅能使用一次</li>
            <li>如遇到问题，请联系客服人员</li>
          </ul>
        </div>
      </el-card>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Key } from '@element-plus/icons-vue'
  import api from '@/axios'
  const activationCode = ref('')
  const loading = ref(false)
  
  const handleRedeem = async () => {
    if (!activationCode.value) {
      ElMessage.warning('请输入激活码')
      return
    }
  
    try {
      loading.value = true
      // 这里添加您的兑换逻辑
      const res = await api.post('/api/activationCode/exchange?activationCode=' + activationCode.value)
      if (res.data.code === 0) {
        ElMessage.success('兑换成功！')
        activationCode.value = ''
      } else {
        console.log(res)
        ElMessage.error(res.data.msg)
      }
    } catch (error) {
      ElMessage.error('兑换失败，请检查激活码是否正确')
    } finally {
      loading.value = false
    }
  }
  </script>
  
  <style scoped>
  .redeem-container {
    display: flex;
    justify-content: center;
    align-items: center;
    /* min-height: 100vh; */
    /* background-color: #f5f7fa; */
    padding: 20px;
  }
  
  .redeem-card {
    width: 100%;
    max-width: 500px;
    border-radius: 8px;
  }
  
  .card-header {
    text-align: center;
    margin-bottom: 30px;
  }
  
  .card-header h2 {
    color: #303133;
    margin: 0 0 10px 0;
  }
  
  .subtitle {
    color: #909399;
    font-size: 14px;
    margin: 0;
  }
  
  .input-section {
    margin-bottom: 30px;
  }
  
  .redeem-button {
    width: 100%;
    margin-top: 15px;
    height: 40px;
  }
  
  .tips-section {
    background-color: var(--el-bg-color-container);
    padding: 15px;
    border-radius: 4px;
  }
  
  .tips-section h3 {
    color: #303133;
    font-size: 16px;
    margin: 0 0 10px 0;
  }
  
  .tips-section ul {
    margin: 0;
    padding-left: 20px;
    color: #606266;
    font-size: 14px;
  }
  
  .tips-section li {
    margin-bottom: 5px;
  }
  
  .tips-section li:last-child {
    margin-bottom: 0;
  }
  </style>