<template>
  <div class="notice-container" v-richText="content" v-if="content && !isMobile"></div>

  <div class="tab-container">
    <div class="tab-header">
      <div class="tab-group">
        <div class="slider" :style="sliderStyle"></div>
        <input name="nav" type="radio" class="nav-radio" id="home" checked="checked" hidden />
        <div class="tab-button" ref="freeTab" :class="{ active: activeTab === 'free' }" @click="handleClick('free')"
          v-if="enableFreeNode === 'true'">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M11.3333 2C9.86057 2 8.66667 3.19391 8.66667 4.66667C8.66667 6.13943 9.86057 7.33333 11.3333 7.33333C12.8061 7.33333 14 6.13943 14 4.66667C14 3.19391 12.8061 2 11.3333 2Z"
              fill="currentColor" />
            <path d="M7.33333 8.66667H2V14H7.33333V8.66667Z" fill="currentColor" />
            <path d="M5.33333 2V4H7.33333V5.33333H5.33333V7.33333H4V5.33333H2V4H4V2H5.33333Z" fill="currentColor" />
            <path
              d="M12.2761 11.3331L13.6904 9.91887L12.7475 8.97606L11.3333 10.3903L9.91912 8.97606L8.97631 9.91887L10.3905 11.3331L8.97631 12.7473L9.91912 13.6901L11.3333 12.2759L12.7475 13.6901L13.6904 12.7473L12.2761 11.3331Z"
              fill="currentColor" />
          </svg>
          <span>{{ config.nodeFreeName || $t('nodes.free') }}</span>
        </div>
        <div class="divider" v-if="enableFreeNode === 'true'"></div>

        <input name="nav" type="radio" class="nav-radio" id="about" hidden />
        <div class="tab-button" ref="fourOTab" :class="{ active: activeTab === '4o' }" @click="handleClick('4o')"
          v-if="!(enable4oPlus === 'true')">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M14.6663 2.66663H1.33301V8.58258C1.4323 8.53053 1.5405 8.47345 1.65639 8.4123C2.31887 8.06276 3.23255 7.58068 4.16944 7.14997C4.81858 6.85155 5.49909 6.56784 6.14517 6.35772C6.78176 6.15067 7.42953 5.99996 7.99967 5.99996C8.56982 5.99996 9.21759 6.15067 9.85418 6.35772C10.5003 6.56784 11.1808 6.85155 11.8299 7.14997C12.7668 7.58068 13.6805 8.06276 14.343 8.4123C14.4589 8.47345 14.567 8.53054 14.6663 8.58259V2.66663Z"
              fill="currentColor" />
            <path
              d="M14.6663 10.0809L14.3646 9.92776C14.1726 9.8303 13.9415 9.70834 13.68 9.57038C13.0229 9.22358 12.1739 8.7756 11.273 8.36142C10.6491 8.0746 10.0201 7.81377 9.4418 7.62567C9.00878 7.48484 8.62798 7.39283 8.31245 7.35414L8.29122 7.38356C8.15619 7.57163 7.98634 7.82704 7.83791 8.10535C7.68654 8.38918 7.57433 8.66571 7.52957 8.90074C7.48502 9.13462 7.52125 9.24503 7.55254 9.29402C7.56133 9.30195 7.63123 9.35656 7.9037 9.37649C8.16568 9.39564 8.46735 9.37499 8.8286 9.35025C8.90672 9.3449 8.98763 9.33936 9.07151 9.33399C9.496 9.30683 10.0213 9.28212 10.4748 9.39343C10.7123 9.45173 10.9665 9.55508 11.1862 9.74411C11.4138 9.94002 11.5698 10.1984 11.6464 10.5049C11.8154 11.181 11.5049 11.744 11.1095 12.1415C10.722 12.531 10.1765 12.8492 9.6147 13.1111C9.4332 13.1957 9.30154 13.2705 9.20641 13.3333H14.6663V10.0809Z"
              fill="currentColor" />
            <path
              d="M7.65128 13.3333C7.69372 13.0943 7.79909 12.8331 8.04221 12.5745C8.2707 12.3314 8.59952 12.1132 9.05132 11.9026C9.56284 11.6642 9.94136 11.4252 10.1643 11.2011C10.3792 10.985 10.3654 10.8782 10.3529 10.8283C10.3394 10.7745 10.3238 10.7611 10.3169 10.7552C10.3023 10.7426 10.2593 10.7134 10.157 10.6883C9.9308 10.6328 9.60366 10.636 9.15667 10.6646C9.09161 10.6688 9.02353 10.6735 8.95326 10.6784C8.59198 10.7037 8.17257 10.733 7.80646 10.7063C7.38501 10.6755 6.79268 10.558 6.44497 10.0364C6.14332 9.58394 6.1402 9.06905 6.21978 8.65126C6.28954 8.28506 6.43429 7.9266 6.59048 7.61502L6.55755 7.62567C5.97922 7.81377 5.35027 8.0746 4.72636 8.36142C3.82542 8.7756 2.9766 9.22353 2.31941 9.57033C2.05793 9.70831 1.82678 9.83029 1.63476 9.92776L1.33301 10.0809V13.3333H7.65128Z"
              fill="currentColor" />
          </svg>
          <span>{{ config.node4oName || $t('nodes.fouro') }}</span>
        </div>

        <div class="divider" v-if="!(enable4oPlus === 'true')"></div>

        <input name="nav" type="radio" class="nav-radio" id="contact" hidden />
        <div class="tab-button" ref="plusTab" :class="{ active: activeTab === 'plus' }" @click="handleClick('plus')">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd"
              d="M0.0576172 6.66667L4.72428 2H11.2766L15.9432 6.66667L8.00043 14.6095L0.0576172 6.66667ZM7.27655 5.33333L6.33374 4.39052L4.0576 6.66667L6.33374 8.94281L7.27655 8L5.94322 6.66667L7.27655 5.33333Z"
              fill="currentColor" />
          </svg>
          <span>{{ config.nodePlusName || $t('nodes.plus') }}</span>
        </div>
      </div>
      <div class="notice-button" v-if="isMobile && content" @click="toggleNotice">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M12 16V12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M12 8H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </div>
    </div>

    <div class="mobile-notice" v-if="content && showNotice && isMobile" v-richText="content"></div>

    <div class="tab-content">
      <div v-show="activeTab === 'free'" class="page-contents">
        <NodeGrid :nodes="freeNodes" :config="config" />
      </div>
      <div v-show="activeTab === '4o'" class="page-contents">
        <NodeGrid :nodes="fourONodes" :config="config" />
      </div>
      <div v-show="activeTab === 'plus'" class="page-contents">
        <NodeGrid :nodes="plusNodes" :config="config" />
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, defineProps, getCurrentInstance, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import NodeGrid from './NodeGrid.vue'
import api from '@/axios'
import useUserStore from '@/store/user'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const userStore = useUserStore()
const user = ref(userStore.user)


const props = defineProps({
  config: {
    type: Object,
    required: true,
  },

})
const { proxy } = getCurrentInstance()
const content = ref('')
const getCarNotification = async () => {
  const res = await api.get('/api/notification/getLatest?typeList=4')
  if (res.data.code === 0) {
    let arr = res.data.data
    const notification = proxy.$getLatestNotification(arr)
    if (notification) {
      content.value = notification.content
    }
  }
}
getCarNotification()

const activeTab = ref('free')
const freeNodes = ref([])
const fourONodes = ref([])
const plusNodes = ref([])

const freeTab = ref(null)
const fourOTab = ref(null)
const plusTab = ref(null)

const sliderStyle = ref({
  left: '0px',
  width: '0px'
})

const updateSliderPosition = () => {
  console.log('updateSliderPosition', activeTab.value)
  const tabs = {
    'free': freeTab.value,
    '4o': fourOTab.value,
    'plus': plusTab.value
  }
  const activeElement = tabs[activeTab.value]
  if (activeElement) {
    sliderStyle.value = {
      left: `${activeElement.offsetLeft}px`,
      width: `${activeElement.offsetWidth}px`
    }
  }
}

const mockData = {
  free: [
    {
      type: 'free',
      carID: 'ChatGPT-Free-01',
      clearsIn: -1,
      count: 5,
      isTeam: false,
      desc: '推荐'
    },
    {
      type: 'free',
      carID: 'ChatGPT-Free-02',
      clearsIn: 120,
      count: 25,
      isTeam: true,
      desc: '推荐'
    },
    {
      type: 'free',
      carID: 'Claude-Free-01',
      clearsIn: -1,
      count: 15,
      isTeam: false,
      desc: '推荐'
    },
    {
      type: 'free',
      carID: 'Grok-Free-01',
      clearsIn: 300,
      count: 35,
      isTeam: true,
      desc: '推荐'
    }
  ],
  '4o': [
    {
      type: 'fouro',
      carID: 'ChatGPT-4o-01',
      clearsIn: -1,
      count: 10,
      isTeam: true,
      desc: '推荐'
    },
    {
      type: 'fouro',
      carID: 'ChatGPT-4o-02',
      clearsIn: 180,
      count: 30,
      isTeam: false,
      desc: '推荐'
    },
    {
      type: 'fouro',
      carID: 'Claude-4o-01',
      clearsIn: -1,
      count: 20,
      isTeam: true,
      desc: '推荐'
    }
  ],
  plus: [
    {
      type: 'plus',
      label: 'PLUS',
      carID: 'ChatGPT-Plus-01',
      clearsIn: -1,
      count: 8,
      isTeam: false,
      desc: '推荐'
    },
    {
      type: 'plus',
      label: 'TEAM',
      carID: 'ChatGPT-Plus-02',
      clearsIn: 240,
      count: 28,
      isTeam: true,
      desc: '推荐'
    },
    {
      type: 'plus',
      label: 'PRO',
      carID: 'Claude-Plus-01',
      clearsIn: -1,
      count: 18,
      isTeam: false,
      desc: '推荐'
    },
    {
      type: 'plus',
      carID: 'Grok-Plus-01',
      clearsIn: 150,
      count: 38,
      isTeam: true,
      desc: '推荐'
    }
  ]
}
const enableFreeNode = computed(() => props.config.enableFreeNode)
const enable4oPlus = computed(() => props.config.enable4oPlus)

// 监听两个变量的变化
watch([enableFreeNode, enable4oPlus], async ([newFreeNode, new4oPlus]) => {
  console.log('enableFreeNode:', newFreeNode, 'enable4oPlus:', new4oPlus)

  if (newFreeNode === 'true') {
    activeTab.value = 'free'
    await reqNode('free')
  } else if (new4oPlus === 'true') {
    activeTab.value = 'plus'
    await reqNode('plus')
  } else {
    activeTab.value = '4o'
    await reqNode('4o')
  }

  // 使用 nextTick 确保 DOM 更新后再更新滑动条位置
  await nextTick(() => {
    updateSliderPosition()
  })
})
const reqNode = async (name) => {
  try {
    const res = await api.get('/api/chatGpt/car/list?type=' + name)
    if (res.data.code === 0) {
      if (name == 'free') {
        res.data.data.forEach(element => {
          element.type = 'free'
        });
        freeNodes.value = res.data.data;
        // freeNodes.value = mockData.free;
      } else if (name == 'plus') {
        res.data.data.forEach(element => {
          if (enable4oPlus.value === 'true' && !['PLUS', 'TEAM', 'PRO','EDU'].includes(element.label)) {
            element.type = 'fouro'
          } else {
            element.type = 'plus'
          }
        });
        plusNodes.value = res.data.data;
        // plusNodes.value = mockData.plus;
      } else if (name == '4o') {
        res.data.data.forEach(element => {
          element.type = 'fouro'
        });
        fourONodes.value = res.data.data;
        // fourONodes.value = mockData['4o'];
      }
    } else {
      // 当API返回错误时，使用模拟数据
      if (name === 'free') {
        freeNodes.value = mockData.free;
      } else if (name === 'plus') {
        plusNodes.value = mockData.plus;
      } else if (name === '4o') {
        fourONodes.value = mockData['4o'];
      }
      console.log(`Using mock data for ${name} nodes`);
    }
  } catch (error) {
    console.error('Error fetching nodes:', error);
    // 当API请求失败时，也使用模拟数据
    if (name === 'free') {
      freeNodes.value = mockData.free;
    } else if (name === 'plus') {
      plusNodes.value = mockData.plus;
    } else if (name === '4o') {
      fourONodes.value = mockData['4o'];
    }
    console.log(`Using mock data for ${name} nodes due to API error`);
  }
}
const handleClick = async (tab) => {
  activeTab.value = tab
  await reqNode(tab)
  setTimeout(updateSliderPosition, 0)
}

const showNotice = ref(false)
const isMobile = ref(false)

const toggleNotice = () => {
  showNotice.value = !showNotice.value
}

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

onMounted(async () => {
  if (enableFreeNode.value === 'true') {
    activeTab.value = 'free'
    await reqNode('free')
  } else if (enable4oPlus.value === 'true') {
    activeTab.value = 'plus'
    await reqNode('plus')
  } else {
    activeTab.value = '4o'
    await reqNode('4o')
  }

  window.addEventListener('resize', updateSliderPosition)
  window.addEventListener('resize', checkMobile)
  checkMobile()

  // 确保 DOM 更新后再更新滑动条位置
  await nextTick(() => {
    updateSliderPosition()
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', updateSliderPosition)
  window.removeEventListener('resize', checkMobile)
})

</script>
<style scoped>
@import url(@/style/nodetabs.css);

.tab-group {
  width: fit-content;
  font-size: 14px;
}


.tab-button {
  padding: 8px 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tab-button :deep(svg) {
  width: 20px;
  height: 20px;
}

.notice-container {
  border: 3px dashed #5AC4FD;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  color: #495057;
  font-size: 14px;
  line-height: 1.5;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 16px;
}

.notice-button {
  cursor: pointer;
  padding: 8px;
  color: #5AC4FD;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.notice-button:hover {
  opacity: 0.8;
}

.mobile-notice {
  border: 3px dashed #5AC4FD;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  color: #495057;
  font-size: 14px;
  line-height: 1.5;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .notice-container {
    display: none;
  }

}
</style>