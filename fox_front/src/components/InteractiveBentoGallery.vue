<template>
  <div class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- 标题和描述 -->
    <div class="mb-8 text-center">
      <h1 
        class="text-2xl sm:text-3xl md:text-4xl font-bold bg-clip-text text-transparent 
               bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900
               dark:from-white dark:via-gray-200 dark:to-white title-enter"
      >
        {{ title }}
      </h1>
      <p 
        class="mt-2 text-sm sm:text-base text-gray-600 dark:text-gray-400 desc-enter"
      >
        {{ description }}
      </p>
    </div>

    <!-- 瀑布流网格 -->
    <div 
      v-if="!selectedItem"
      class="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-4 gap-3 auto-rows-[60px] gallery-grid"
    >
      <div
        v-for="(item, index) in items"
        :key="item.id"
        :class="[
          'relative overflow-hidden rounded-xl cursor-move transition-transform duration-200',
          'hover:scale-[1.02] gallery-item',
          item.span
        ]"
        :style="{ animationDelay: `${index * 50}ms` }"
        @click="handleItemClick(item, $event)"
        @dragstart="handleDragStart($event, index)"
        @dragend="handleDragEnd"
        @dragover="handleDragOver"
        @drop="handleDrop($event, index)"
        draggable="true"
      >
        <!-- 媒体项内容 -->
        <MediaItem 
          :item="item" 
          class="absolute inset-0 w-full h-full"
        />
        
        <!-- 悬停信息遮罩 -->
        <div class="media-overlay">
          <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
          <h3 class="relative text-white text-xs sm:text-sm md:text-base font-medium line-clamp-1">
            {{ item.title }}
          </h3>
          <p class="relative text-white/70 text-[10px] sm:text-xs md:text-sm mt-0.5 line-clamp-2">
            {{ item.desc }}
          </p>
        </div>
      </div>
    </div>

    <!-- 全屏模态窗口 -->
    <transition name="modal" appear>
      <div 
        v-if="selectedItem"
        class="fixed inset-0 w-full min-h-screen sm:h-[90vh] md:h-[600px] backdrop-blur-lg 
               rounded-none sm:rounded-lg md:rounded-xl overflow-hidden z-50 modal-backdrop"
        @click="closeModal"
      >
        <!-- 主要内容区 -->
        <div class="h-full flex flex-col">
          <div class="flex-1 p-2 sm:p-3 md:p-4 flex items-center justify-center bg-gray-50/50">
            <transition name="slide" mode="out-in">
              <div
                :key="selectedItem.id"
                class="relative w-full aspect-[16/9] max-w-[95%] sm:max-w-[85%] md:max-w-3xl 
                       h-auto max-h-[70vh] rounded-lg overflow-hidden shadow-md modal-content"
                @click.stop
              >
                <MediaItem 
                  :item="selectedItem" 
                  class="w-full h-full object-contain bg-gray-900/20"
                />
                <div class="absolute bottom-0 left-0 right-0 p-2 sm:p-3 md:p-4 
                           bg-gradient-to-t from-black/50 to-transparent">
                  <h3 class="text-white text-base sm:text-lg md:text-xl font-semibold">
                    {{ selectedItem.title }}
                  </h3>
                  <p class="text-white/80 text-xs sm:text-sm mt-1">
                    {{ selectedItem.desc }}
                  </p>
                </div>
              </div>
            </transition>
          </div>
        </div>

        <!-- 关闭按钮 -->
        <button
          class="absolute top-2 sm:top-2.5 md:top-3 right-2 sm:right-2.5 md:right-3 
                 p-2 rounded-full bg-gray-200/80 text-gray-700 hover:bg-gray-300/80 
                 text-xs sm:text-sm backdrop-blur-sm transition-all duration-200
                 hover:scale-110 active:scale-90 close-btn"
          @click="closeModal"
        >
          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        <!-- 可拖拽的控制面板 -->
        <div 
          class="fixed z-50 left-1/2 bottom-4 -translate-x-1/2 touch-none draggable-dock"
          :style="{ transform: `translate(calc(-50% + ${dockPosition.x}px), ${dockPosition.y}px)` }"
          @mousedown="startDrag"
          @touchstart="startDrag"
        >
          <div class="relative rounded-xl bg-sky-400/20 backdrop-blur-xl 
                     border border-blue-400/30 shadow-lg cursor-grab active:cursor-grabbing dock-container">
            <div class="flex items-center -space-x-2 px-3 py-2">
              <div
                v-for="(item, index) in items"
                :key="item.id"
                :class="[
                  'relative group w-8 h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 flex-shrink-0',
                  'rounded-lg overflow-hidden cursor-pointer hover:z-20 dock-item',
                  selectedItem.id === item.id ? 'ring-2 ring-white/70 shadow-lg selected-dock-item' : 'hover:ring-2 hover:ring-white/30'
                ]"
                :style="{ 
                  zIndex: selectedItem.id === item.id ? 30 : items.length - index,
                  transform: `rotate(${selectedItem.id === item.id ? 0 : index % 2 === 0 ? -15 : 15}deg) 
                            scale(${selectedItem.id === item.id ? 1.2 : 1}) 
                            translateY(${selectedItem.id === item.id ? -8 : 0}px)`
                }"
                @click.stop="setSelectedItem(item)"
              >
                <MediaItem :item="item" class="w-full h-full" />
                <div class="absolute inset-0 bg-gradient-to-b from-transparent via-white/5 to-white/20" />
                <div 
                  v-if="selectedItem.id === item.id"
                  class="absolute -inset-2 bg-white/20 blur-xl active-glow"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import MediaItem from './MediaItem.vue'

// Props
const props = defineProps({
  mediaItems: {
    type: Array,
    required: true
  },
  title: {
    type: String,
    default: 'Interactive Gallery'
  },
  description: {
    type: String,
    default: 'Drag and explore our curated collection'
  }
})

// 响应式数据
const selectedItem = ref(null)
const items = ref([...props.mediaItems])
const isDragging = ref(false)
const draggedIndex = ref(-1)
const dockPosition = reactive({ x: 0, y: 0 })
const isDraggingDock = ref(false)

// 处理媒体项点击
const handleItemClick = (item, event) => {
  if (!isDragging.value) {
    selectedItem.value = item
  }
}

// 关闭模态窗口
const closeModal = () => {
  selectedItem.value = null
}

// 设置选中项
const setSelectedItem = (item) => {
  selectedItem.value = item
}

// 拖拽处理
const handleDragStart = (event, index) => {
  isDragging.value = true
  draggedIndex.value = index
  event.dataTransfer.effectAllowed = 'move'
  event.dataTransfer.setData('text/html', index.toString())
  
  // 添加拖拽样式
  setTimeout(() => {
    if (event.target) {
      event.target.style.opacity = '0.5'
    }
  }, 0)
}

const handleDragEnd = (event) => {
  isDragging.value = false
  draggedIndex.value = -1
  if (event.target) {
    event.target.style.opacity = '1'
  }
}

const handleDragOver = (event) => {
  event.preventDefault()
  event.dataTransfer.dropEffect = 'move'
}

const handleDrop = (event, targetIndex) => {
  event.preventDefault()
  const sourceIndex = draggedIndex.value
  
  if (sourceIndex !== -1 && sourceIndex !== targetIndex) {
    const newItems = [...items.value]
    const draggedItem = newItems[sourceIndex]
    newItems.splice(sourceIndex, 1)
    newItems.splice(targetIndex, 0, draggedItem)
    items.value = newItems
  }
}

// Dock 拖拽处理
let dragOffset = { x: 0, y: 0 }
let isDraggingDockFlag = false

const startDrag = (event) => {
  isDraggingDockFlag = true
  const clientX = event.clientX || event.touches[0].clientX
  const clientY = event.clientY || event.touches[0].clientY
  
  dragOffset.x = clientX - dockPosition.x
  dragOffset.y = clientY - dockPosition.y
  
  document.addEventListener('mousemove', handleDockDrag)
  document.addEventListener('mouseup', endDrag)
  document.addEventListener('touchmove', handleDockDrag)
  document.addEventListener('touchend', endDrag)
  
  event.preventDefault()
}

const handleDockDrag = (event) => {
  if (!isDraggingDockFlag) return
  
  const clientX = event.clientX || event.touches[0].clientX
  const clientY = event.clientY || event.touches[0].clientY
  
  dockPosition.x = clientX - dragOffset.x
  dockPosition.y = clientY - dragOffset.y
  
  event.preventDefault()
}

const endDrag = () => {
  isDraggingDockFlag = false
  document.removeEventListener('mousemove', handleDockDrag)
  document.removeEventListener('mouseup', endDrag)
  document.removeEventListener('touchmove', handleDockDrag)
  document.removeEventListener('touchend', endDrag)
}

// 组件挂载后的初始化
onMounted(() => {
  // 添加渐进式动画类
  nextTick(() => {
    const titleEl = document.querySelector('.title-enter')
    const descEl = document.querySelector('.desc-enter')
    const galleryItems = document.querySelectorAll('.gallery-item')
    
    if (titleEl) titleEl.classList.add('animate-fade-in')
    if (descEl) descEl.classList.add('animate-fade-in-delay')
    
    galleryItems.forEach((item, index) => {
      setTimeout(() => {
        item.classList.add('animate-slide-up')
      }, index * 50)
    })
  })
})
</script>

<!-- 引入画廊组件样式 -->
<style src="@/styles/gallery.css"></style>

<style scoped>
/* 基础样式 */
.container {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 网格布局 */
.gallery-grid {
  grid-auto-rows: 60px;
}

/* 媒体项样式 */
.gallery-item {
  position: relative;
  opacity: 0;
  transform: translateY(50px) scale(0.9);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.gallery-item.animate-slide-up {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.gallery-item:hover {
  transform: scale(1.02) !important;
  z-index: 10;
}

/* 媒体遮罩 */
.media-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

@media (min-width: 640px) {
  .media-overlay {
    padding: 0.75rem;
  }
}

@media (min-width: 768px) {
  .media-overlay {
    padding: 1rem;
  }
}

.gallery-item:hover .media-overlay {
  opacity: 1;
}

/* 文本截断 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 模态窗口动画 */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.98);
}

.modal-backdrop {
  animation: modalFadeIn 0.3s ease-out;
}

/* 内容切换动画 */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.2s ease;
}

.slide-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.97);
}

.slide-leave-to {
  opacity: 0;
  transform: translateY(20px) scale(0.97);
}

/* 标题动画 */
.animate-fade-in {
  animation: fadeInUp 0.5s ease-out;
}

.animate-fade-in-delay {
  animation: fadeInUp 0.5s ease-out 0.1s both;
}

/* Dock 样式 */
.draggable-dock {
  user-select: none;
  -webkit-user-select: none;
}

.dock-container {
  transition: transform 0.1s ease;
}

.dock-item {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.dock-item:hover {
  transform: scale(1.3) rotate(0deg) translateY(-10px) !important;
}

.selected-dock-item {
  transform: scale(1.2) rotate(0deg) translateY(-8px);
}

.active-glow {
  animation: glow 0.2s ease-in-out;
}

/* 关闭按钮 */
.close-btn:hover {
  transform: scale(1.1);
}

.close-btn:active {
  transform: scale(0.9);
}

/* 动画关键帧 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glow {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式样式 */
@media (max-width: 640px) {
  .gallery-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-backdrop {
    border-radius: 0;
  }
  
  .draggable-dock {
    left: 50% !important;
    bottom: 1rem !important;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 769px) {
  .gallery-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Tailwind 兼容类 */
.md\:col-span-1 { grid-column: span 1 / span 1; }
.md\:col-span-2 { grid-column: span 2 / span 2; }
.md\:row-span-2 { grid-row: span 2 / span 2; }
.md\:row-span-3 { grid-row: span 3 / span 3; }
.sm\:col-span-1 { grid-column: span 1 / span 1; }
.sm\:col-span-2 { grid-column: span 2 / span 2; }
.sm\:row-span-2 { grid-row: span 2 / span 2; }

@media (min-width: 640px) {
  .sm\:col-span-1 { grid-column: span 1 / span 1; }
  .sm\:col-span-2 { grid-column: span 2 / span 2; }
  .sm\:row-span-2 { grid-row: span 2 / span 2; }
}

@media (min-width: 768px) {
  .md\:col-span-1 { grid-column: span 1 / span 1; }
  .md\:col-span-2 { grid-column: span 2 / span 2; }
  .md\:row-span-2 { grid-row: span 2 / span 2; }
  .md\:row-span-3 { grid-row: span 3 / span 3; }
}
</style> 