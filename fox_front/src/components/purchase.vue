<template>
    <div class="pricing-wrapper">
        <div class="pricing-container">
            <div class="pricing-grid">
                <div v-for="item in salesPlan" :key="item.id" class="pricing-card">
                    <!-- Hot badge for hot plans -->
                    <div v-if="item.isHot" class="hot-badge">HOT</div>
                    
                    <div class="card-header">
                        <h2 class="plan-title">{{ item.name }}</h2>
                        <div class="price-section">
                            <div class="price-row">
                                <span class="price">¥{{ item.amount }}</span>
                                <span class="price-period">{{ item.validDays }}天</span>
                            </div>
                            <div class="plan-subtitle">{{ item.groupTitle }}</div>
                        </div>
                    </div>

                    <div class="card-content">

                        <!-- Other features from tagList -->
                        <div v-for="tag in item.tagList" :key="tag" class="feature-item">
                            <div class="feature-icon">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M8.3326 12.6424L15.9929 4.98206L17.1714 6.16056L8.3326 14.9994L3.0293 9.69612L4.20781 8.51762L8.3326 12.6424Z" fill="black"/>
                                </svg>
                            </div>
                            <span class="feature-text">{{ tag }}</span>
                        </div>

                        <div class="spacer"></div>
                        
                        <button class="purchase-button" @click="openOrderDialog(item)">
                            立即订购
                        </button>
                    </div>
                </div>
                <el-empty :image="empty" v-if="!salesPlan.length"></el-empty>
            </div>
        </div>

        <!-- Modern Dialog -->
        <el-dialog v-model="orderDialogVisible" :title="selectedPlan?.name" class="modern-dialog">
            <div class="dialog-content">
                <!-- Order Summary Section -->
                <div v-if="!showQRCode && !showOrderSuccess" class="order-summary">
                    <div class="summary-content">
                        <div class="plan-header">
                            <div class="price-info">
                                <div class="price-row" v-if="selectedPlan?.discountAmount">
                                    <span class="original-price">¥{{ selectedPlan?.amount }}</span>
                                    <span class="discounted-price">¥{{ selectedPlan?.discountAmount }}</span>
                                </div>
                                <div class="price-row" v-else>
                                    <span class="final-price">¥{{ selectedPlan?.amount }}</span>
                                </div>
                            </div>
                                <span class="plan-duration">{{ selectedPlan?.validDays }}天</span>
                
                    
                 
                        </div>
                        
                        <div class="plan-features">
                            <el-tag :type="getTagType(selectedPlan?.membershipType)" class="feature-tag">{{ selectedPlan?.groupTitle }}</el-tag>
                            <el-tag v-if="selectedPlan?.gpt4oLimit" type="success" class="feature-tag">{{ useLimitMap[selectedPlan?.gpt4oLimit.per] }}可用{{ selectedPlan?.gpt4oLimit.limit }}次</el-tag>
                        </div>
                    </div>

                    <!-- Coupon Section -->
                    <div v-if="useConpon" class="coupon-section">
                        <el-form :model="form" :rules="rules">
                            <el-form-item prop="couponCode">
                                <el-input v-model="form.couponCode" placeholder="请输入优惠券代码" class="coupon-input">
                                    <template #prefix>
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M2 3h12v2l-1 1v6l1 1v2H2v-2l1-1V6L2 5V3z" fill="#409EFF"/>
                                        </svg>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-form>
                    </div>

                    <!-- Payment Methods -->
                    <div class="payment-section">
                        <h4 class="payment-title">选择支付方式</h4>
                        <el-radio-group v-model="form.paymentMethodId" class="payment-methods">
                            <el-radio v-for="item in paymentMethods" :label="item.id" class="payment-method">
                                {{ item.name }}
                            </el-radio>
                        </el-radio-group>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <el-button type="primary" @click="handleOrder" class="primary-btn" size="large">
                            立即购买
                        </el-button>
                        <el-button v-if="!useConpon" @click="useConpon = true" class="secondary-btn" size="large">
                            使用优惠券
                        </el-button>
                    </div>
                </div>

                <!-- QR Code Section -->
                <div v-if="showQRCode" class="qr-section">
                    <div class="qr-header">
                        <h3 class="qr-title">{{ getPayTip() }}支付</h3>
                        <p class="qr-subtitle" v-if="qrCodeSrc">请使用{{ getPayTip() }}扫描下方二维码完成付款</p>
                        <p class="qr-subtitle" v-else>正在跳转到{{ getPayTip() }}支付页面...</p>
                    </div>
                    
                    <div v-if="qrCodeSrc" class="qr-code-container">
                        <div class="qr-code-wrapper">
                            <img :src="qrCodeSrc" alt="支付二维码" class="qr-image">
                        </div>
                    </div>
                    
                    <div class="qr-footer">
                        <div class="loading-indicator">
                            <div class="loading-spinner"></div>
                            <span>等待支付中...</span>
                        </div>
                    </div>
                </div>

                <!-- Success Section -->
                <div v-if="showOrderSuccess" class="success-section">
                    <div class="success-icon">
                        <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="40" cy="40" r="40" fill="#67C23A"/>
                            <path d="M33.5 40L37.5 44L46.5 35" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    
                    <h2 class="success-title">支付成功！</h2>
                    <p class="success-subtitle">您的订单已完成，现在可以开始使用服务了</p>
                    
                    <el-button type="primary" @click="goToHome" class="success-btn" size="large">
                        立即使用
                    </el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import { Check } from '@element-plus/icons-vue'
import { ref, reactive } from 'vue'
import api from '@/axios'
import { ElMessage, ElMessageBox } from 'element-plus';
import useUserStore from '../store/user';
import { useRouter } from 'vue-router'
import empty from '@/assets/node/empty.png'
const router = useRouter()
const userStore = useUserStore()
const isLoggedIn = userStore.isLoggedIn
const showOrderSuccess = ref(false)
const salesPlan = ref([])
const getGroupInfo = async (groupName) => {
    try {
        const res = await api.get('/api/userGroup/detailByName', {
            params: {
                groupName
            }
        })
        if (res.data.code === 0 && res.data.data.group) {
            const group = res.data.data.group
            // 获取gpt-4o模型的速率限制
            const gpt4oLimit = res.data.data.rateLimits?.find(limit => limit.model === 'gpt-4o')
            return {
                ...group,
                gpt4oLimit: gpt4oLimit
            }
        }
        return null
    } catch (error) {
        console.error('Failed to get group info:', error)
        return null
    }
}

const getSalesPlan = async () => {
    const requestPayload = {
        pageNum: 1,
        pageSize: 100,
        sortOrder: 'asc',
        sortField: '`order`'
    }
    const res = await api.post('/api/salesPlan/page', requestPayload)
    if (res.status == 200) {
        // 获取所有用户组的速率限制信息
        const groupsRes = await api.get('/api/userGroup/details')
        
        if (groupsRes.data.code === 0) {
            const groupLimitsMap = groupsRes.data.data
            
            salesPlan.value = res.data.data.map(item => {
                const membershipType = item.membershipType || item.memberShipType
                const groupLimits = groupLimitsMap[membershipType] || []
                // 优先使用 gpt-4o 模型的限制，如果没有则使用第一个限制
                const gpt4oLimit = groupLimits.find(limit => limit.model === 'gpt-4o') || groupLimits[0]
                
                return {
                    ...item,
                    membershipType,
                    groupTitle: gpt4oLimit?.groupTitle || membershipType, // 使用 groupTitle 作为组标题
                    gpt4oLimit: gpt4oLimit ? {
                        limit: gpt4oLimit.limit,
                        per: gpt4oLimit.per
                    } : null,
                    tagList: item.tags && item.tags.split(',')
                }
            })
        }
        
        salesPlan.value = salesPlan.value.filter(item => item.isDisplayOnFront)
    }
}
getSalesPlan()
const paymentMethods = ref([])
const getPaymentMethods = async () => {
    const res = await api.get('/api/paymentMethod/queryAll')
    if (res.status == 200) {
        paymentMethods.value = res.data.data
    }
}
getPaymentMethods()

const useLimitMap = reactive({
    '1h': '1小时',
    '3h': '3小时',
    '1d': '每天',
    '1w': '每周'
})

// 控制弹窗的显示与隐藏
const orderDialogVisible = ref(false)
const selectedPlan = ref(null)
const showQRCode = ref(false)
const qrCodeSrc = ref('')

const openOrderDialog = (plan) => {
    if (!isLoggedIn) {
        ElMessageBox.confirm('请先登录', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            router.push('/login')
        }).catch(() => {
            ElMessage({
                type: 'info',
                message: '取消登录'
            })
        })
        return
    }
    selectedPlan.value = plan
    orderDialogVisible.value = true
    showQRCode.value = false // 重置二维码状态
    form.couponCode = ''
    useConpon.value = false
    form.paymentMethodId = ''
    form.salesPlanId = plan.id
    selectedPlan.value.discountAmount = undefined
}
const tradeNo = ref('')
let timer = null
const handleOrder = async () => {
    if (!form.paymentMethodId) {
        ElMessage({
            message: '请选择支付方式',
            type: 'error'
        })
        return;
    }

    const res = await api.post('/api/pay/qrCode', {
        salesPlanId: selectedPlan.value.id,
        couponCode: form.couponCode,
        paymentMethodId: form.paymentMethodId
    });
    if (res.data.code === 0) {
        showQRCode.value = true
        tradeNo.value = res.data.data.outTradeNo

        if (res.data.data.payurl) {
            // 如果有支付链接，在新窗口打开
            window.open(res.data.data.payurl, '_blank')
            // 同时开始轮询检查支付状态
            timer = setInterval(queryOrderStatus, 2000)
        } else if (res.data.data.qrCodeBase64) {
            // 如果有二维码，显示二维码
            qrCodeSrc.value = 'data:image/png;base64,' + res.data.data.qrCodeBase64
            timer = setInterval(queryOrderStatus, 2000)
        }
    } else {
        ElMessage.error(res.data.msg)
    }

}
const queryOrderStatus = async () => {
    const res = await api.get('/api/pay/queryOrderStatus?outTradeNo=' + tradeNo.value)
    console.log(res)
    if (res.data.code == 0) {
        //订单支付成功
        if (res.data.data == 1) {
            // orderDialogVisible.value = false
            showOrderSuccess.value = true
            useConpon.value = false
            showQRCode.value = false
            clearInterval(timer)
        }
    }
}
const useConpon = ref(false)
const form = reactive({
    couponCode: '',
    paymentMethodId: '',
    salesPlanId: ''
})
const couponCodeValidator = async (rule, value, callback) => {
    // console.log(value)
    if (!value) {
        return callback()
    }
    // 模拟优惠券验证逻辑
    const res = await api.get('/api/coupon/checkCoupon?couponCode=' + value + '&salesPlanId=' + selectedPlan.value.id)
    console.log(res)
    if (res.data.code == 0) {
        selectedPlan.value.discountAmount = selectedPlan.value.amount - res.data.data
    } else {
        return callback(new Error(res.data.msg))
    }
    return callback()
}
const rules = reactive({
    couponCode: [
        { validator: couponCodeValidator, trigger: 'blur' }
    ]
})
const getPayTip = () => {
    let tip = ''
    paymentMethods.value.forEach(element => {
        if (element.id === form.paymentMethodId) {
            console.log(element)
            if (element.paymentType.indexOf('wx') != -1) {
                tip = "使用微信"
                return tip
            } else if (element.paymentType.indexOf('ali') != -1) {
                tip = "使用支付宝"
                return tip
            }
        }
    });
    return tip
}
const goToHome = () => {
    router.push('/')
}
const getTagType = (membershipType) => {
    if (!membershipType) return 'info'
    // 根据membershipType判断标签类型
    if (membershipType.includes('plus') || membershipType.includes('high')) {
        return 'danger'  // 高级会员用红色
    }
    return 'success'     // 普通会员用绿色
}
</script>

<style scoped>
.pricing-wrapper {
    align-self: stretch;
    padding: 32px 24px;
    padding-top: 0;
    position: relative;
    background: white;
    overflow: hidden;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 32px;
}

.pricing-container {
    width: 100%;
    max-width: 1440px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
}

.pricing-grid {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    gap: 0;
    justify-content: flex-start;
}

.pricing-card {
    width: calc(25% - 1px);
    height: 480px;
    min-width: 300px;
    background: white;
    overflow: hidden;
    border-right: 1px rgba(0, 0, 0, 0.20) solid;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    position: relative;
    margin-bottom: 16px;
}

.pricing-card:last-child {
    border-right: none;
}

.hot-badge {
    position: absolute;
    left: 150px;
    top: 71px;
    padding: 4px 12px;
    background: #FFDD00;
    border-radius: 12px;
    outline: 1px white solid;
    outline-offset: -1px;
    color: #877500;
    font-size: 13px;
    font-family: MiSans;
    font-weight: 520;
    text-transform: capitalize;
    line-height: 18.20px;
    z-index: 1;
}

.card-header {
    align-self: stretch;
    padding: 0 32px 16px 32px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 16px;
}

.plan-title {
    color: black;
    font-size: 16px;
    font-family: MiSans;
    font-weight: 630;
    text-transform: capitalize;
    line-height: 22.40px;
    margin: 0;
}

.price-section {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 8px;
}

.price-row {
    display: flex;
    justify-content: flex-start;
    align-items: baseline;
    gap: 4px;
}

.price {
    color: #47BEFF;
    font-size: 40px;
    font-family: MiSans;
    font-weight: 520;
    line-height: 48px;
}

.price-period {
    color: rgba(0, 0, 0, 0.60);
    font-size: 13px;
    font-family: MiSans;
    font-weight: 380;
    text-transform: capitalize;
    line-height: 18.20px;
}

.plan-subtitle {
    color: rgba(0, 0, 0, 0.60);
    font-size: 14px;
    font-family: MiSans;
    font-weight: 380;
    line-height: 19.60px;
}

.card-content {
    align-self: stretch;
    flex: 1 1 0;
    padding: 0 32px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
}

.feature-item {
    align-self: stretch;
    padding: 8px 0;
    border-bottom: 1px rgba(255, 255, 255, 0.10) solid;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
    text-align: left;
}

.feature-item:first-child {
    border-top: 1px rgba(255, 255, 255, 0.10) solid;
}

.feature-icon {
    position: relative;
    width: 20px;
    height: 20px;
}

.feature-text {
    flex: 1 1 0;
    color: black;
    font-size: 16px;
    font-family: MiSans;
    font-weight: 380;
    line-height: 22.40px;
}

.spacer {
    align-self: stretch;
    flex: 1 1 0;
}

.purchase-button {
    align-self: stretch;
    height: 46px;
    min-width: 120px;
    padding: 8px 16px;
    background: rgba(0, 0, 0, 0.06);
    border-radius: 12px;
    border: none;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    color: black;
    font-size: 16px;
    font-family: MiSans;
    font-weight: 520;
    line-height: 22.40px;
    transition: all 0.2s ease;
}

.purchase-button:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}



/* Modern Dialog Styles */
:deep(.modern-dialog) {
    border-radius: 20px;
    --el-dialog-width: 480px;
    --el-dialog-margin-top: 8vh;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

:deep(.modern-dialog .el-dialog__header) {
    padding: 24px 24px 0 24px;
    border-bottom: none;
}

:deep(.modern-dialog .el-dialog__title) {
    font-size: 20px;
    font-weight: 600;
    color: #1a1a1a;
    font-family: MiSans;
}

:deep(.modern-dialog .el-dialog__body) {
    padding: 0 24px 24px 24px;
}

.dialog-content {
    font-family: MiSans;
}

/* Order Summary Styles */
.order-summary {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.summary-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 24px;
    background: #fafafa;
    border-radius: 16px;
}

.plan-header {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
}

.plan-info {
    display: flex;
    flex-direction: column;
    gap: 6px;
    flex: 1;
}

.plan-name {
    font-size: 20px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
    line-height: 1.3;
}

.plan-duration {
    font-size: 14px;
    color: #666;
    font-weight: 400;
}

.price-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
    text-align: right;
}

.price-row {
    display: flex;
    gap: 8px;
}

.original-price {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
}

.discounted-price {
    font-size: 28px;
    font-weight: 600;
    color: #47BEFF;
    line-height: 1;
}

.final-price {
    font-size: 28px;
    font-weight: 600;
    color: #47BEFF;
    line-height: 1;
}

.plan-features {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

.feature-tag {
    font-size: 13px;
    padding: 4px 10px;
}

/* Coupon Section */
.coupon-section {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #e0e0e0;
}

:deep(.coupon-input .el-input__wrapper) {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Payment Section */
.payment-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.payment-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
}

.payment-methods {
    display: flex;
    flex-direction: row;
    gap: 12px;
    justify-content: center;
    align-items: center;
}

:deep(.payment-method) {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    transition: all 0.2s ease;
    text-align: center;
    font-weight: 500;
    font-size: 14px;
    max-width: 40%;
}

:deep(.payment-method:hover) {
    border-color: #47BEFF;
    background: #f0f9ff;
}

:deep(.payment-method.is-checked) {
    border-color: #47BEFF;
    background: #f0f9ff;
    color: #47BEFF;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 12px;
    margin-top: 8px;
}

:deep(.primary-btn) {
    flex: 2;
    border-radius: 12px;
    font-weight: 600;
    background: #47BEFF;
    border-color: #47BEFF;
    height: 48px;
    font-size: 16px;
}

:deep(.secondary-btn) {
    flex: 1;
    border-radius: 12px;
    font-weight: 600;
    color: #47BEFF;
    border-color: #47BEFF;
    background: white;
    height: 48px;
    font-size: 14px;
}

/* QR Code Section */
.qr-section {
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 20px 0;
}

.qr-header {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.qr-title {
    font-size: 20px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
}

.qr-subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.qr-code-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.qr-code-wrapper {
    padding: 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.qr-image {
    width: 200px;
    height: 200px;
    border-radius: 8px;
}

.qr-footer {
    display: flex;
    justify-content: center;
}

.loading-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #666;
    font-size: 14px;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e0e0e0;
    border-top-color: #47BEFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Success Section */
.success-section {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    padding: 40px 20px;
}

.success-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 8px;
}

.success-title {
    font-size: 24px;
    font-weight: 600;
    color: #67C23A;
    margin: 0;
}

.success-subtitle {
    font-size: 16px;
    color: #666;
    margin: 0;
}

:deep(.success-btn) {
    border-radius: 12px;
    font-weight: 600;
    padding: 12px 32px;
    background: #67C23A;
    border-color: #67C23A;
}

/* Responsive design */
@media (max-width: 1400px) {
    .pricing-card {
        width: calc(33.333% - 1px);
    }
}

@media (max-width: 1200px) {
    .pricing-card {
        width: calc(50% - 1px);
    }
}

@media (max-width: 900px) {
    .pricing-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1px;
    }
    
    .pricing-card {
        width: 100%;
        border-right: none;
        border-bottom: 1px rgba(0, 0, 0, 0.20) solid;
    }
    
    .pricing-card:last-child {
        border-bottom: none;
    }
    
    .hot-badge {
        left: auto;
        right: 32px;
        top: 32px;
    }
}

@media (max-width: 768px) {
    .pricing-wrapper {
        padding: 32px 16px;
    }
    
    .pricing-grid {
        grid-template-columns: 1fr;
    }
    
    .pricing-card {
        min-width: unset;
        height: auto;
    }
    
    .card-header {
        padding: 0 24px 16px 24px;
    }
    
    .card-content {
        padding: 0 24px;
    }
    
    :deep(.modern-dialog) {
        --el-dialog-width: 95%;
        --el-dialog-margin-top: 5vh;
    }
    
    .summary-content {
        padding: 20px;
    }
    
    .plan-header {
        gap: 12px;
    }
    
    .price-info {
        align-items: flex-start;
    }
    
    .payment-methods {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    :deep(.payment-method) {
        padding: 8px;
        font-size: 13px;
        min-height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .payment-section {
        gap: 12px;
    }
    
    .payment-title {
        font-size: 14px;
        margin-bottom: 8px;
    }
    
    .action-buttons {
        gap: 8px;
    }
    
    :deep(.primary-btn) {
        flex: 3;
        font-size: 14px;
        height: 44px;
    }
    
    :deep(.secondary-btn) {
        flex: 2;
        font-size: 13px;
        height: 44px;
    }
}

@media (max-width: 480px) {
    .pricing-wrapper {
        padding: 24px 12px;
    }
    
    .card-header {
        padding: 0 16px 16px 16px;
    }
    
    .card-content {
        padding: 0 16px;
    }
    
    .price {
        font-size: 32px;
        line-height: 40px;
    }
    
    .feature-text {
        font-size: 14px;
        line-height: 20px;
    }
}
</style>
