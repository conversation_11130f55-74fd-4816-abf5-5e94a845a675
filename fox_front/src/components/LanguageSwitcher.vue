<template>
  <el-dropdown @command="handleLanguageChange" trigger="click">
    <el-button type="text" class="language-switcher">
      <el-icon><Globe /></el-icon>
      <span class="language-text">{{ currentLanguageDisplay }}</span>
      <el-icon class="el-icon--right"><arrow-down /></el-icon>
    </el-button>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item 
          v-for="lang in supportedLanguages" 
          :key="lang.code"
          :command="lang.code"
          :class="{ 'is-active': currentLocale === lang.code }"
        >
          <span class="language-option">
            <span class="language-name">{{ lang.name }}</span>
            <span class="language-native">{{ lang.nativeName }}</span>
          </span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { Globe, ArrowDown } from '@element-plus/icons-vue'
import { setLocale, getCurrentLocale, getSupportedLocales } from '@/i18n'
import { ElMessage } from 'element-plus'
import api from '@/axios'

const { locale } = useI18n()

const currentLocale = ref(getCurrentLocale())
const supportedLanguages = ref(getSupportedLocales())

// 当前语言显示名称
const currentLanguageDisplay = computed(() => {
  const current = supportedLanguages.value.find(lang => lang.code === currentLocale.value)
  return current ? current.nativeName : '中文'
})

// 处理语言切换
const handleLanguageChange = async (langCode) => {
  try {
    // 前端切换语言
    setLocale(langCode)
    currentLocale.value = langCode
    
    // 通知后端切换语言
    try {
      await api.post('/api/i18n/changeLanguage', null, {
        params: { lang: langCode }
      })
    } catch (error) {
      console.warn('后端语言切换失败，但前端已切换:', error)
    }
    
    ElMessage.success(getSuccessMessage(langCode))
  } catch (error) {
    console.error('语言切换失败:', error)
    ElMessage.error('语言切换失败')
  }
}

// 根据语言获取成功消息
const getSuccessMessage = (langCode) => {
  const messages = {
    zh: '语言切换成功',
    en: 'Language switched successfully',
    ja: '言語切り替え成功'
  }
  return messages[langCode] || messages.zh
}

onMounted(() => {
  // 初始化时同步当前语言到后端
  const currentLang = getCurrentLocale()
  if (currentLang) {
    api.post('/api/i18n/changeLanguage', null, {
      params: { lang: currentLang }
    }).catch(error => {
      console.warn('初始化语言同步失败:', error)
    })
  }
})
</script>

<style scoped>
.language-switcher {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: var(--el-text-color-primary);
  cursor: pointer;
  transition: all 0.3s;
}

.language-switcher:hover {
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.language-text {
  font-size: 14px;
  margin: 0 4px;
}

.language-option {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.language-name {
  font-size: 14px;
  font-weight: 500;
}

.language-native {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.el-dropdown-menu__item.is-active {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.el-dropdown-menu__item.is-active .language-name {
  color: var(--el-color-primary);
  font-weight: 600;
}
</style>
