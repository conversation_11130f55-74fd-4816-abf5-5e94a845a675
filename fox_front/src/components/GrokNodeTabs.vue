<template>
  <div class="notice-container" v-richText="content" v-if="content && !isMobile"></div>

  <div class="tab-container">
    <div class="tab-header">
      <div class="tab-wrapper">
        <div class="slider" :style="sliderStyle"></div>
        <div class="tab-group">
          <input name="nav" type="radio" class="nav-radio" id="home" checked="checked" hidden />
          <div class="tab-button" ref="grokTab" :class="{ active: activeTab === 'grok' }" @click="handleClick('grok')">
            <free/>
            <span>{{ config.nodeGrokName || $t('nodes.grok') }}</span>
          </div>
        </div>
      </div>
      <div class="notice-button" v-if="isMobile && content" @click="toggleNotice">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M12 16V12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M12 8H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </div>

    <div class="mobile-notice" v-if="content && showNotice && isMobile" v-richText="content"></div>

    <div class="tab-content">
      <div class="page-contents">
        <NodeGrid :nodes="grokNodes" :config="config" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, getCurrentInstance, onMounted, onUnmounted } from 'vue'
import NodeGrid from './NodeGrid.vue'
import api from '@/axios'
import free from '@/assets/node/free.svg'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'

const { t } = useI18n()
const props = defineProps({
  config: {
    type: Object,
    required: true,
  },
})

const { proxy } = getCurrentInstance()
const content = ref('')
const activeTab = ref('grok')
const grokNodes = ref([])
const grokTab = ref(null)
const getCarNotification = async () => {
  const res = await api.get('/api/notification/getLatest?typeList=6')
  if (res.data.code === 0) {
    let arr = res.data.data
    const notification = proxy.$getLatestNotification(arr)
    if (notification) {
      content.value = notification.content
    }
  }
}
getCarNotification()
const sliderStyle = ref({
  left: '0px',
  width: '0px'
})

const updateSliderPosition = () => {
  if (grokTab.value) {
    sliderStyle.value = {
      left: `${grokTab.value.offsetLeft}px`,
      width: `${grokTab.value.offsetWidth}px`
    }
  }
}

const reqNode = async (name) => {
  const res = await api.get('/api/chatGpt/car/list?type=' + name)
  if (res.data.code === 0) {
    if (name == 'grok') {
      res.data.data.forEach(element => {
        element.type = 'grok'
      });
      grokNodes.value = res.data.data;
    }
  } else {
    ElMessage.error(t('nodes.fetchError'))
  }
}

const handleClick = async (tab) => {
  activeTab.value = tab
  await reqNode(tab)
  setTimeout(updateSliderPosition, 0)
}

const showNotice = ref(false)
const isMobile = ref(false)

const toggleNotice = () => {
  showNotice.value = !showNotice.value
}

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

onMounted(() => {
  updateSliderPosition()
  window.addEventListener('resize', updateSliderPosition)
  window.addEventListener('resize', checkMobile)
  checkMobile()
})

onUnmounted(() => {
  window.removeEventListener('resize', updateSliderPosition)
  window.removeEventListener('resize', checkMobile)
})

reqNode('grok')
</script>

<style scoped>
@import url(@/style/nodetabs.css);

.tab-group {
  width: fit-content;
  font-size: 14px;
}

.tab-button {
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tab-button.active {
  background: transparent;
}

@media screen and (max-width: 768px) {
  .tab-group {
    width: 100%;
  }

  .tab-button {
    flex: 1;
    justify-content: center;
  }
}
.notice-container {
  border: 3px dashed #5AC4FD;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  color: #495057;
  font-size: 14px;
  line-height: 1.5;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 16px;
}

.tab-wrapper {
  position: relative;
}

.slider {
  z-index: 1;
}

.notice-button {
  cursor: pointer;
  padding: 8px;
  color: #5AC4FD;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.notice-button:hover {
  opacity: 0.8;
}

.mobile-notice {
  border: 3px dashed #5AC4FD;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  color: #495057;
  font-size: 14px;
  line-height: 1.5;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media screen and (max-width: 768px) {
  .notice-container {
    display: none;
  }
  
  .tab-group {
    flex: 1;
  }
}
</style>