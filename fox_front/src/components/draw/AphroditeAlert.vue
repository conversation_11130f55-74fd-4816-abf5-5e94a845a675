<template>
  <Teleport to="body">
    <transition name="aphrodite-fade">
      <div v-if="visible" :class="['aphrodite-alert', alertTypeClass]">
        <span class="icon">
          <svg v-if="type==='success'" viewBox="0 0 24 24" fill="none" width="24" height="24">
            <circle cx="12" cy="12" r="12" fill="#e6f9f0"/>
            <path d="M7 13l3 3 7-7" stroke="#34c759" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <svg v-else-if="type==='error'" viewBox="0 0 24 24" fill="none" width="24" height="24">
            <circle cx="12" cy="12" r="12" fill="#ffeaea"/>
            <path d="M15 9l-6 6M9 9l6 6" stroke="#ff3b30" stroke-width="2.2" stroke-linecap="round"/>
          </svg>
        </span>
        <div class="content">
          <div v-if="title" class="title">{{ title }}</div>
          <div v-if="description" class="description">{{ description }}</div>
          <slot />
        </div>
        <button v-if="closable" class="close-btn" @click="close">
          <svg viewBox="0 0 24 24" width="18" height="18">
            <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2.2" stroke-linecap="round"/>
          </svg>
        </button>
      </div>
    </transition>
  </Teleport>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, computed } from 'vue'

const props = defineProps({
  type: { type: String, default: 'success' },
  title: String,
  description: String,
  closable: { type: Boolean, default: true },
  modelValue: { type: Boolean, default: true },
  duration: { type: Number, default: 3000 },
  position: { type: String, default: 'top' }
})

const emit = defineEmits(['update:modelValue', 'close'])
const visible = ref(props.modelValue)

watch(() => props.modelValue, v => {
  visible.value = v
  if (v && props.duration > 0) {
    setTimeout(() => close(), props.duration)
  }
})

function close() {
  visible.value = false
  emit('update:modelValue', false)
  emit('close')
}

const alertTypeClass = computed(() => ({
  'aphrodite-alert--error': props.type === 'error',
  'aphrodite-alert--success': props.type === 'success',
  [`aphrodite-alert--${props.position}`]: true
}))
</script>

<style scoped>
.aphrodite-alert {
  display: flex;
  align-items: flex-start;
  gap: 14px;
  padding: 18px 22px;
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.1), 0 1.5px 6px 0 rgba(0,0,0,0.04);
  min-width: 260px;
  max-width: 420px;
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  animation: aphrodite-pop 0.4s cubic-bezier(.4,0,.2,1);
  transition: all 0.3s cubic-bezier(.4,0,.2,1);
  backdrop-filter: blur(8px);
}

.aphrodite-alert--top {
  top: 20px;
}

.aphrodite-alert--bottom {
  bottom: 20px;
}

.aphrodite-alert--success {
  border: 1.5px solid rgba(52, 199, 89, 0.3);
  background: linear-gradient(90deg, rgba(230, 249, 240, 0.95) 0%, rgba(245, 255, 248, 0.95) 100%);
}

.aphrodite-alert--error {
  border: 1.5px solid rgba(255, 59, 48, 0.3);
  background: linear-gradient(90deg, rgba(255, 234, 234, 0.95) 0%, rgba(255, 245, 245, 0.95) 100%);
}

.icon {
  margin-top: 2px;
  flex-shrink: 0;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.content {
  flex: 1;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.description {
  font-size: 14px;
  color: #606266;
  opacity: 0.92;
  line-height: 1.4;
}

.close-btn {
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  position: absolute;
  top: 12px;
  right: 14px;
  color: #bbb;
  transition: all 0.2s;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.aphrodite-alert--success .close-btn:hover {
  color: #34c759;
  background: rgba(52, 199, 89, 0.1);
}

.aphrodite-alert--error .close-btn:hover {
  color: #ff3b30;
  background: rgba(255, 59, 48, 0.1);
}

.aphrodite-fade-enter-active {
  animation: aphrodite-slide-down 0.3s ease-out;
}

.aphrodite-fade-leave-active {
  animation: aphrodite-slide-up 0.3s ease-in;
}

@keyframes aphrodite-pop {
  0% { 
    transform: translateX(-50%) translateY(-20px);
    opacity: 0;
  }
  100% { 
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

@keyframes aphrodite-slide-down {
  0% {
    transform: translateX(-50%) translateY(-20px);
    opacity: 0;
  }
  100% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

@keyframes aphrodite-slide-up {
  0% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(-20px);
    opacity: 0;
  }
}

@media (max-width: 768px) {
  .aphrodite-alert {
    width: 90%;
    margin: 0 auto;
  }
}
</style> 