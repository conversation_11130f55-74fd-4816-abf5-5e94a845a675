<template>
  <div class="mask-editor-dialog">
    <div class="dialog-header">
      <h3>创建遮罩</h3>
      <div class="tools">
        <button 
          :class="{ active: !isEraser }" 
          @click="isEraser = false"
          title="画笔"
        >
          🖌️
        </button>
        <button 
          :class="{ active: isEraser }" 
          @click="isEraser = true"
          title="橡皮擦"
        >
          ⌫
        </button>
        <input 
          type="range" 
          v-model="brushSize" 
          min="1" 
          max="50" 
          class="brush-size-slider"
        >
        <span class="brush-size">{{ brushSize }}px</span>
      </div>
    </div>
    
    <div class="editor-content">
      <div class="canvas-container" ref="containerRef">
        <img 
          :src="imageUrl" 
          ref="imageRef" 
          @load="initializeCanvas" 
          class="source-image"
        >
        <canvas
          ref="canvasRef"
          @pointerdown="handlePointerDown"
          @pointermove="handlePointerMove"
          @pointerup="handlePointerUp"
          @pointerleave="handlePointerUp"
          class="mask-canvas"
        ></canvas>
      </div>

      <div class="mask-description">
        <p>使用紫色画笔在图像上绘制要被替换的区域。绘制时紫色区域代表将被AI替换的部分。</p>
        <p>保存后，系统会自动将紫色区域转换为白色，未绘制区域转换为黑色，生成符合OpenAI API要求的遮罩。</p>
      </div>
    </div>

    <div class="dialog-footer">
      <button @click="$emit('cancel')" class="cancel-btn">取消</button>
      <button @click="handleSave" class="save-btn">保存</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const props = defineProps({
  imageUrl: {
    type: String,
    required: true
  },
  existingMask: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['save', 'cancel'])

const canvasRef = ref(null)
const imageRef = ref(null)
const containerRef = ref(null)
const isEraser = ref(false)
const brushSize = ref(20)
const isDrawing = ref(false)
const currentPath = ref(null)
const paths = ref([])

const initializeCanvas = () => {
  const canvas = canvasRef.value
  const image = imageRef.value
  
  if (!canvas || !image) return
  
  canvas.width = image.naturalWidth
  canvas.height = image.naturalHeight
  
  // 初始化画布为透明
  const ctx = canvas.getContext('2d')
  ctx.fillStyle = 'rgba(0,0,0,0)'
  ctx.fillRect(0, 0, canvas.width, canvas.height)

  // 如果有现有的遮罩，加载并转换它
  if (props.existingMask) {
    const maskImage = new Image()
    maskImage.onload = () => {
      // 创建临时画布来处理遮罩
      const tempCanvas = document.createElement('canvas')
      tempCanvas.width = canvas.width
      tempCanvas.height = canvas.height
      const tempCtx = tempCanvas.getContext('2d')
      
      // 1. 绘制原始遮罩
      tempCtx.drawImage(maskImage, 0, 0)
      
      // 2. 将透明区域转换为紫色半透明（因为透明区域是用户之前绘制的区域）
      const imageData = tempCtx.getImageData(0, 0, canvas.width, canvas.height)
      const data = imageData.data
      for (let i = 0; i < data.length; i += 4) {
        // 检查像素是否透明
        if (data[i + 3] === 0) {
          // 设置为紫色半透明
          data[i] = 147  // R
          data[i + 1] = 112  // G
          data[i + 2] = 219  // B
          data[i + 3] = 128  // A (0.5 * 255)
        } else {
          // 不透明区域（黑色）保持透明
          data[i + 3] = 0
        }
      }
      tempCtx.putImageData(imageData, 0, 0)
      
      // 3. 将转换后的遮罩绘制到主画布
      ctx.drawImage(tempCanvas, 0, 0)
      
      // 将现有遮罩转换为路径信息
      // 注意：这里简化处理，实际应用中可能需要更复杂的路径检测算法
      paths.value = [{
        points: [
          { x: 0, y: 0 },
          { x: canvas.width, y: 0 },
          { x: canvas.width, y: canvas.height },
          { x: 0, y: canvas.height }
        ],
        brushSize: canvas.width / 2,
        isEraser: false
      }]
    }
    maskImage.src = props.existingMask
  }
}

const getCanvasPoint = (e) => {
  const canvas = canvasRef.value
  const rect = canvas.getBoundingClientRect()
  const scaleX = canvas.width / rect.width
  const scaleY = canvas.height / rect.height
  
  return {
    x: (e.clientX - rect.left) * scaleX,
    y: (e.clientY - rect.top) * scaleY
  }
}

const drawPaths = () => {
  const canvas = canvasRef.value
  const ctx = canvas.getContext('2d')
  
  // 清除画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  
  // 绘制所有路径
  const allPaths = [...paths.value]
  if (currentPath.value) allPaths.push(currentPath.value)
  
  allPaths.forEach(path => {
    if (!path.points.length) return
    
    ctx.beginPath()
    ctx.moveTo(path.points[0].x, path.points[0].y)
    
    path.points.forEach(point => {
      ctx.lineTo(point.x, point.y)
    })
    
    if (path.isEraser) {
      // 橡皮擦模式
      ctx.globalCompositeOperation = 'destination-out'
      ctx.strokeStyle = 'rgba(0, 0, 0, 1)'
    } else {
      // 画笔模式
      ctx.globalCompositeOperation = 'source-over'
      ctx.strokeStyle = 'rgba(147, 112, 219, 0.5)'
    }
    
    ctx.lineWidth = path.brushSize
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'
    ctx.stroke()
    
    // 重置混合模式
    ctx.globalCompositeOperation = 'source-over'
  })
}

const handlePointerDown = (e) => {
  isDrawing.value = true
  const point = getCanvasPoint(e)
  currentPath.value = {
    points: [point],
    brushSize: brushSize.value,
    isEraser: isEraser.value
  }
}

const handlePointerMove = (e) => {
  if (!isDrawing.value || !currentPath.value) return
  
  const point = getCanvasPoint(e)
  currentPath.value.points.push(point)
  drawPaths()
}

const handlePointerUp = () => {
  if (currentPath.value) {
    paths.value.push(currentPath.value)
  }
  isDrawing.value = false
  currentPath.value = null
}

const handleSave = () => {
  const canvas = canvasRef.value
  const ctx = canvas.getContext('2d')

  // 创建临时画布来生成最终的遮罩
  const tempCanvas = document.createElement('canvas')
  tempCanvas.width = canvas.width
  tempCanvas.height = canvas.height
  const tempCtx = tempCanvas.getContext('2d')

  // 初始化画布为黑色
  tempCtx.globalAlpha = 1;
  tempCtx.globalCompositeOperation = 'source-over';
  tempCtx.fillStyle = 'black';
  tempCtx.fillRect(0, 0, canvas.width, canvas.height);

  // 用 destination-out 擦除用户绘制的区域（变透明）
  paths.value.forEach(path => {
    if (!path.points.length) return;
    tempCtx.save();
    tempCtx.globalCompositeOperation = 'destination-out';
    tempCtx.beginPath();
    tempCtx.moveTo(path.points[0].x, path.points[0].y);
    path.points.forEach(point => {
      tempCtx.lineTo(point.x, point.y);
    });
    tempCtx.strokeStyle = 'rgba(0,0,0,1)';
    tempCtx.lineWidth = path.brushSize;
    tempCtx.lineCap = 'round';
    tempCtx.lineJoin = 'round';
    tempCtx.stroke();
    tempCtx.restore();
  });

  // 获取最终的PNG数据URL (仍包含alpha通道)
  const maskDataUrl = tempCanvas.toDataURL('image/png')

  // 验证文件大小
  const byteString = atob(maskDataUrl.split(',')[1])
  const fileSize = byteString.length

  if (fileSize > 4 * 1024 * 1024) {
    alert('遮罩文件大小超过4MB限制')
    return
  }

  emit('save', maskDataUrl)
}

onMounted(() => {
  if (imageRef.value.complete) {
    initializeCanvas()
  }
})
</script>

<style scoped>
.mask-editor-dialog {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 90vh;
  width: 100%;
  max-width: 90vw;
  overflow: auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(147, 112, 219, 0.2);
}

.dialog-header h3 {
  font-size: 1.5rem;
  margin: 0;
}

.tools {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.tools button {
  padding: 0.75rem;
  font-size: 1.2rem;
  border: 1px solid rgba(147, 112, 219, 0.2);
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
}

.tools button.active {
  background: rgba(147, 112, 219, 0.1);
  border-color: rgba(147, 112, 219, 0.4);
}

.brush-size-slider {
  width: 150px;
  height: 6px;
}

.brush-size {
  min-width: 50px;
}

.editor-content {
  display: flex;
  gap: 1.5rem;
  flex: 1;
  min-height: 0;
  height: calc(100% - 120px);
}

.canvas-container {
  position: relative;
  width: 100%;
  overflow: auto;
  border: 1px solid rgba(147, 112, 219, 0.2);
  border-radius: 4px;
  max-height: calc(80vh - 200px);
}

.source-image {
  max-width: 100%;
  height: auto;
  display: block;
  object-fit: contain;
}

.mask-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  touch-action: none;
  pointer-events: all;
}

.mask-description {
  width: 300px;
  flex-shrink: 0;
  background-color: rgba(147, 112, 219, 0.1);
  border-radius: 4px;
  padding: 1rem;
  font-size: 1rem;
  line-height: 1.6;
  align-self: flex-start;
  position: sticky;
  top: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(147, 112, 219, 0.2);
}

.cancel-btn, .save-btn {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background: white;
  border: 1px solid rgba(147, 112, 219, 0.2);
}

.cancel-btn:hover {
  border-color: rgba(147, 112, 219, 0.4);
  background: rgba(147, 112, 219, 0.1);
}

.save-btn {
  background: #9370db;
  border: 1px solid #9370db;
  color: white;
}

.save-btn:hover {
  opacity: 0.9;
}
</style> 