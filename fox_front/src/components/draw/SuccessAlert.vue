<template>
  <AphroditeAlert
    v-bind="$attrs"
    :type="'success'"
    :title="title"
    :description="description"
    :closable="closable"
    :duration="duration"
    :position="position"
    v-bind:modelValue="modelValue"
    @close="$emit('close')"
  >
    <slot />
  </AphroditeAlert>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import AphroditeAlert from './AphroditeAlert.vue'

const props = defineProps({
  title: String,
  description: String,
  closable: { type: Boolean, default: true },
  modelValue: { type: Boolean, default: true },
  duration: { type: Number, default: 3000 },
  position: { type: String, default: 'top' }
})

defineEmits(['update:modelValue', 'close'])
</script> 