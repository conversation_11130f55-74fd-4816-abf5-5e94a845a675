<template>
  <header class="app-header">
    <div class="left-actions">
      <button 
        class="action-button divine-presence-toggle"
        @click="toggleDivinePresence"
        :class="{ active: showDivinePresence }"
        data-tooltip="唤醒阿佛洛狄忒的神圣光辉"
      >
        <span class="toggle-icon"></span>
        <span class="toggle-text">神颜显现</span>
      </button>
      <!-- <button 
        class="action-button pricing-toggle"
        @click="scrollToPricing"
        data-tooltip="查看会员套餐"
      >
        <span class="pricing-icon"></span>
        <span class="toggle-text">价格套餐</span>
      </button> -->
    </div>

    <div class="right-actions">
      <div v-if="isLoggedIn" class="user-info">
        <div class="points-display">
          <span class="points-icon">✨</span>
          <span class="points-value">{{ points || 0 }}</span>
          <span class="points-label">积分</span>
          <span class="points-divider">|</span>
          <span class="drawings-info">
            <span class="drawings-value">{{ Math.floor((points || 0) / draw_consume_points) }}</span>
            <span class="drawings-label">次可用</span>
          </span>
        </div>
        <!-- <UserAvatarDropdown
          :username="username"
          :email="email"
          :avatarUrl="avatarUrl"
          @myWorks="goToMyWorks"
          @userCenter="openUserCenter"
          @logout="logout"
        /> -->
      </div>
      <!-- <button 
        v-else
        class="login-btn" 
        @click="showAuth"
      >
        <i class="fas fa-user"></i>
        登录
      </button> -->
    </div>

    <!-- <UserCenter ref="userCenterRef" /> -->
  </header>
</template>

<script setup>
import { ref, computed } from 'vue'
// import UserAvatarDropdown from './UserAvatarDropdown.vue'
import { useRouter } from 'vue-router'
import useUserStore from '@/store/user'
import { User } from '@element-plus/icons-vue'
// import UserCenter from '@/views/UserCenter.vue'

const router = useRouter()
const userStore = useUserStore()

const isLoggedIn = computed(() => userStore.isLoggedIn)

const points = computed(() => isLoggedIn.value ? userStore.user.limit : 0)
const showDivinePresence = ref(false)

const userCenterRef = ref(null)

const toggleDivinePresence = () => {
  showDivinePresence.value = !showDivinePresence.value
  emit('toggle-divine-presence', showDivinePresence.value)
}


const emit = defineEmits(['toggle-divine-presence', 'show-auth'])

const props = defineProps({
  draw_consume_points: {
    type: Number,
    default: 0
  }
})
</script>

<style scoped>
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
}

.left-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.points-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(147, 112, 219, 0.2);
  border-radius: 2rem;
  color: #9370db;
  font-family: 'Cormorant Garamond', serif;
}

.points-icon {
  font-size: 1.2rem;
  animation: sparkle 1.5s ease-in-out infinite alternate;
}

.points-value {
  font-weight: 500;
  font-size: 1.1rem;
}

.points-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

.points-divider {
  margin: 0 0.5rem;
  color: rgba(147, 112, 219, 0.4);
}

.drawings-info {
  display: flex;
  align-items: center;
  gap: 0.2rem;
}

.drawings-value {
  font-weight: 500;
  color: #9370db;
}

.drawings-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

@keyframes sparkle {
  from {
    opacity: 0.5;
    transform: scale(0.9) rotate(-10deg);
  }
  to {
    opacity: 1;
    transform: scale(1.1) rotate(10deg);
  }
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.2rem;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(147, 112, 219, 0.2);
  border-radius: 2rem;
  color: #9370db;
  font-family: 'Cormorant Garamond', serif;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  outline: none;
}

.toggle-icon {
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%239370db"><path d="M12 1L9 9l-8 3 8 3 3 8 3-8 8-3-8-3z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.pricing-icon {
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%239370db"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* .action-button::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ff69b4, #9370db);
  border-radius: 2rem;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
} */

.action-button:hover::before {
  opacity: 0.2;
}

.action-button.active::before {
  opacity: 0.3;
}

.action-button::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(147, 112, 219, 0.2);
  border-radius: 8px;
  font-size: 0.9rem;
  color: #9370db;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(147, 112, 219, 0.1);
  z-index: 1001;
  pointer-events: none;
}

.action-button:hover::after {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-5px);
}

.login-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.2rem;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(147, 112, 219, 0.2);
  border-radius: 2rem;
  color: #9370db;
  font-family: 'Cormorant Garamond', serif;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.login-btn:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 12px rgba(147, 112, 219, 0.1);
  transform: translateY(-2px);
}

.user-center-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #9370db;
  font-weight: 500;
  
  &:hover {
    color: #8a2be2;
  }
  
  :deep(.el-icon) {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 0.8rem 1rem;
    flex-wrap: wrap;  /* 允许内容换行 */
    justify-content: center;  /* 居中对齐 */
    gap: 0.8rem;  /* 添加垂直间距 */
  }

  .left-actions {
    gap: 0.5rem;
    order: 2;  /* 调整显示顺序 */
    width: 100%;  /* 占满宽度 */
    justify-content: center;  /* 居中对齐 */
  }

  .right-actions {
    gap: 0.5rem;
    order: 1;  /* 调整显示顺序，让用户信息显示在最上方 */
    width: 100%;  /* 占满宽度 */
    justify-content: center;  /* 居中对齐 */
  }

  .user-info {
    gap: 0.8rem;
    width: 100%;  /* 占满宽度 */
    justify-content: center;  /* 居中对齐 */
  }

  .action-button {
    padding: 0.6rem 0.8rem;
    flex: 0 1 auto;  /* 允许按钮收缩 */
  }

  .points-display {
    padding: 0.4rem 0.8rem;
    flex-wrap: nowrap;  /* 防止积分信息换行 */
    justify-content: center;
    gap: 0.3rem;
    max-width: 100%;  /* 确保不会溢出 */
    overflow: hidden;  /* 隐藏溢出内容 */
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 0.6rem 0.8rem;
    gap: 0.6rem;
  }

  .user-info {
    flex-direction: column;  /* 垂直排列 */
    align-items: center;  /* 居中对齐 */
    gap: 0.5rem;
  }

  .points-display {
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
    width: 100%;  /* 占满宽度 */
    justify-content: space-around;  /* 均匀分布 */
  }

  .points-divider {
    display: none;  /* 隐藏分隔符 */
  }

  .drawings-info {
    margin-left: auto;  /* 推到右边 */
  }

  .action-button,
  .login-btn {
    padding: 0.5rem 0.6rem;
    font-size: 0.8rem;
    min-width: 0;  /* 移除最小宽度 */
  }
}

@media (max-width: 360px) {
  .app-header {
    padding: 0.5rem;
    gap: 0.4rem;
  }

  .points-display {
    flex-direction: column;  /* 垂直排列积分信息 */
    align-items: center;
    padding: 0.3rem 0.5rem;
    gap: 0.2rem;
  }

  .points-value,
  .drawings-value {
    font-size: 0.9rem;
  }

  .action-button {
    padding: 0.4rem 0.5rem;
  }

  /* 保持图标显示，隐藏文字 */
  .toggle-text,
  .points-label,
  .drawings-label {
    display: none;
  }

  .points-icon {
    font-size: 0.9rem;
  }

  .drawings-info {
    margin: 0;  /* 重置边距 */
  }
}
</style> 