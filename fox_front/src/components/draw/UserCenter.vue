<template>
  <div class="modal-overlay" v-if="isVisible" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="user-center">
        <div class="user-info-header">
          <div class="user-header">
            <default-avatar class="avatar" :alt="userInfo.username || 'User'" />
            <div class="user-details">
              <h2 class="username">{{ userInfo.username }}</h2>
              <p class="email">{{ userInfo.email }}</p>
            </div>
            <button class="close-button" @click="closeModal">
              <span class="close-icon">×</span>
            </button>
          </div>
          
          <div class="stats">
            <div class="stat-card">
              <div class="stat-icon">✨</div>
              <div class="stat-content">
                <span class="stat-label">可用积分</span>
                <span class="stat-value">{{ userInfo.limit || 0 }}</span>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">📅</div>
              <div class="stat-content">
                <span class="stat-label">注册时间</span>
                <span class="stat-value">{{ formatDate(userInfo.createTime).split(' ')[0] }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="points-history">
          <div class="history-header">
            <span class="history-icon">📊</span>
            <h3>积分历史</h3>
          </div>
          
          <div class="table-container" :class="{ loading }">
            <div v-if="loading" class="loading-spinner"></div>
            <table class="points-table">
              <thead>
                <tr>
                  <th>时间</th>
                  <th>积分变动</th>
                  <th>来源</th>
                  <th>变动后余额</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="record in pointsHistory" :key="record.id">
                  <td>{{ formatDate(record.createdAt) }}</td>
                  <td>
                    <span :class="{ 'positive': record.pointsAmount > 0, 'negative': record.pointsAmount < 0 }">
                      {{ Math.abs(record.pointsAmount) }}
                    </span>
                  </td>
                  <td>{{ getSourceTypeText(record.sourceType) }}</td>
                  <td>{{ record.balanceAfter }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div class="pagination">
            <div class="page-size">
              <select v-model="pageSize" @change="handleSizeChange">
                <option :value="5">5 条/页</option>
                <option :value="10">10 条/页</option>
                <option :value="20">20 条/页</option>
                <option :value="50">50 条/页</option>
              </select>
            </div>
            <div class="page-numbers">
              <button 
                class="page-btn" 
                :disabled="currentPage === 1"
                @click="handleCurrentChange(currentPage - 1)"
              >上一页</button>
              <button 
                v-for="page in displayPages" 
                :key="page"
                class="page-btn" 
                :class="{ active: currentPage === page }"
                @click="handleCurrentChange(page)"
              >{{ page }}</button>
              <button 
                class="page-btn" 
                :disabled="currentPage >= totalPages"
                @click="handleCurrentChange(currentPage + 1)"
              >下一页</button>
            </div>
            <div class="total-info">
              共 {{ total }} 条
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import defaultAvatar from '@/assets/doraemon.svg';
import api from '@/axios';

const isVisible = ref(false);
const userInfo = ref({});
const pointsHistory = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(5);
const total = ref(0);

const totalPages = computed(() => Math.ceil(total.value / pageSize.value));

const displayPages = computed(() => {
  const pages = [];
  const maxPages = 5;
  const halfMaxPages = Math.floor(maxPages / 2);
  
  let startPage = Math.max(1, currentPage.value - halfMaxPages);
  let endPage = Math.min(totalPages.value, startPage + maxPages - 1);
  
  if (endPage - startPage + 1 < maxPages) {
    startPage = Math.max(1, endPage - maxPages + 1);
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }
  
  return pages;
});

const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getMonth() + 1}月${d.getDate()}日 ${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}`;
};

const getRecordTypeText = (type) => {
  const types = {
    1: '获得积分',
    2: '消耗积分'
  };
  return types[type] || '未知类型';
};

const getSourceTypeText = (type) => {
  const types = {
    1: '注册奖励',
    2: '管理员新增',
    3: '邀请奖励',
    4: '图片生成消耗',
    5: '图片生成失败退还',
    6: '积分购买'
  };
  return types[type] || '未知来源';
};

const fetchUserInfo = async () => {
  try {
    const response = await api.get('/api/chatGptUser/getChatGptUser');
    if (response.data.code === 0) {
      userInfo.value = response.data.data;
    } else {
      console.error(response.data.msg || '获取用户信息失败');
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
};

const fetchPointsHistory = async () => {
  loading.value = true;
  try {
    const response = await api.post('/api/user-points/page', {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    });
    if (response.data.code === 0) {
      pointsHistory.value = response.data.data;
      total.value = response.data.total;
    } else {
      console.error(response.data.msg || '获取积分历史失败');
    }
  } catch (error) {
    console.error('获取积分历史失败:', error);
  } finally {
    loading.value = false;
  }
};

const handleSizeChange = () => {
  currentPage.value = 1;
  fetchPointsHistory();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchPointsHistory();
};

const openDialog = () => {
  isVisible.value = true;
  fetchUserInfo();
  fetchPointsHistory();
  document.body.style.overflow = 'hidden';
};

const closeModal = () => {
  isVisible.value = false;
  document.body.style.overflow = '';
};

defineExpose({
  openDialog
});
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  background: white;
  border-radius: 16px;
  overflow: auto;
  animation: slideIn 0.3s ease;
  display: flex;
  flex-direction: column;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.user-info-header {
  background: linear-gradient(135deg, #9370db, #8a2be2);
  color: white;
  padding: 24px;
}

.user-header {
  display: flex;
  align-items: center;
  position: relative;
  margin-bottom: 24px;
}

.close-button {
  position: absolute;
  right: 0;
  top: 0;
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

.close-icon {
  font-size: 24px;
  line-height: 1;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-right: 20px;
  border: 3px solid rgba(255, 255, 255, 0.2);
}

.user-details {
  flex: 1;
}

.username {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.email {
  margin: 4px 0 0;
  opacity: 0.9;
  font-size: 14px;
}

.stats {
  display: flex;
  gap: 16px;
}

.stat-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  font-size: 24px;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
}

.points-history {
  padding: 24px;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
}

.history-icon {
  font-size: 24px;
}

.history-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.table-container {
  position: relative;
  overflow: auto;
  border: 1px solid #eee;
  border-radius: 8px;
  flex: 1;
  min-height: 0;
}

.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #9370db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.points-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 600px;
  text-align: center;
}

.points-table th {
  position: sticky;
  top: 0;
  background: #f8f9fa;
  z-index: 1;
  padding: 12px 16px;
  text-align: center;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #eee;
}

.points-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  color: #666;
  text-align: center;
}

.points-table tr:hover {
  background: #f8f9fa;
}

.positive {
  color: #67c23a;
  font-weight: 600;
  position: relative;
  padding-left: 16px;
}

.positive::before {
  content: '+';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  color: #67c23a;
  font-weight: bold;
}

.negative {
  color: #f56c6c;
  font-weight: 600;
  position: relative;
  padding-left: 16px;
}

.negative::before {
  content: '-';
  position: absolute;
  left: 4px;
  top: 50%;
  transform: translateY(-50%);
  color: #f56c6c;
  font-weight: bold;
}

.pagination {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.page-size select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  color: #333;
  cursor: pointer;
}

.page-numbers {
  display: flex;
  gap: 8px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 6px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  border-color: #9370db;
  color: #9370db;
}

.page-btn.active {
  background: #9370db;
  border-color: #9370db;
  color: white;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.total-info {
  color: #666;
  font-size: 14px;
}

@media (max-width: 768px) {
  .modal-content {
    width: 100%;
    height: 100%;
    max-height: 100vh;
    border-radius: 0;
  }
  
  .stats {
    flex-direction: column;
  }
  
  .pagination {
    flex-direction: column;
    align-items: stretch;
  }
  
  .table-container {
    margin: 0 -24px;
    border-left: none;
    border-right: none;
    border-radius: 0;
  }
}
</style> 