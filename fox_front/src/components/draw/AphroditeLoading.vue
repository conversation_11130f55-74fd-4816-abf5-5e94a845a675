<template>
  <div class="aphrodite-loading-overlay">
    <div class="aphrodite-loading-container">
      <div class="aphrodite-loading">
        <div class="divine-circles">
          <div class="circle pink"></div>
          <div class="circle purple"></div>
          <div class="circle gold"></div>
        </div>
        <div class="spinner-glow">
          <svg class="spinner" viewBox="0 0 50 50">
            <defs>
              <linearGradient id="aphrodite-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stop-color="#ffb6d5" />
                <stop offset="100%" stop-color="#b39ddb" />
              </linearGradient>
            </defs>
            <circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="5"/>
          </svg>
        </div>
        <div class="loading-tip">
          <slot name="tip">{{ tip }}</slot>
        </div>
        
        <div class="divine-icons">
          <svg class="divine-icon" viewBox="0 0 24 24" width="24" height="24"><path d="M12 2L13.09 8.26L19 8.27L14 12.14L15.18 18.02L12 14.77L8.82 18.02L10 12.14L5 8.27L10.91 8.26L12 2Z" fill="#ffb6d5"/></svg>
          <svg class="divine-icon" viewBox="0 0 24 24" width="24" height="24"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41 0.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" fill="#b39ddb"/></svg>
          <svg class="divine-icon" viewBox="0 0 24 24" width="24" height="24"><path d="M2 17.25L9.5 21l7.5-3.75V6.75L9.5 3 2 6.75v10.5z" fill="#ffe082"/></svg>
        </div>
        
        <div class="wait-message">
          生成图片大概需要2分钟，请耐心等待，你可以稍后去"我的作品"查看
        </div>
        
        <div class="loading-desc">
          <slot></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'
const props = defineProps({
  tip: { type: String, default: '' }
})
</script>

<style scoped>
.aphrodite-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.aphrodite-loading-container {
  width: 100%;
  max-width: 480px;
  margin: 0 20px;
  animation: fade-in 0.3s ease-out;
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.aphrodite-loading {
  width: 100%;
  min-height: 240px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(255,248,251,0.95), rgba(251,244,255,0.92));
  border-radius: 1.5rem;
  box-shadow: 0 8px 32px rgba(147,112,219,0.15);
  position: relative;
  overflow: hidden;
  z-index: 10;
  padding: 2rem 1rem;
}
.divine-circles {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  pointer-events: none;
  z-index: 0;
}
.circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.18;
  filter: blur(16px);
  animation: float 8s infinite ease-in-out;
}
.circle.pink {
  width: 180px; height: 180px;
  left: -60px; top: -60px;
  background: radial-gradient(circle, #ffb6d5 0%, transparent 80%);
  animation-delay: 0s;
}
.circle.purple {
  width: 140px; height: 140px;
  right: -40px; top: 30px;
  background: radial-gradient(circle, #b39ddb 0%, transparent 80%);
  animation-delay: 2s;
}
.circle.gold {
  width: 120px; height: 120px;
  left: 40%; bottom: -40px;
  background: radial-gradient(circle, #ffe082 0%, transparent 80%);
  animation-delay: 4s;
}
@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); opacity: 0.18; }
  50% { transform: translate(20px, -20px) scale(1.1); opacity: 0.28; }
}
.spinner-glow {
  margin-top: 2.5rem;
  margin-bottom: 1.2rem;
  position: relative;
  z-index: 1;
}
.spinner {
  width: 64px;
  height: 64px;
  display: block;
}
.spinner .path {
  stroke: url(#aphrodite-gradient);
  stroke-linecap: round;
  animation: dash 1.4s ease-in-out infinite;
}
.spinner {
  filter: drop-shadow(0 0 16px #ffb6d5) drop-shadow(0 0 8px #b39ddb);
}
@keyframes dash {
  0% { stroke-dasharray: 1, 150; stroke-dashoffset: 0; }
  50% { stroke-dasharray: 90, 150; stroke-dashoffset: -35; }
  100% { stroke-dasharray: 90, 150; stroke-dashoffset: -124; }
}
.loading-tip {
  font-size: 1.3rem;
  color: #b39ddb;
  text-align: center;
  margin-top: 0.5rem;
  font-family: 'Cormorant Garamond', serif;
  letter-spacing: 1px;
  min-height: 2.5em;
  z-index: 2;
  text-shadow: 0 2px 8px #fff6, 0 1px 0 #ffe08244;
}
.loading-desc {
  font-size: 1rem;
  color: #9370db;
  margin-top: 0.5rem;
  text-align: center;
  z-index: 2;
  opacity: 0.7;
}

/* 新增图标和等待提示样式 */
.divine-icons {
  display: flex;
  justify-content: center;
  gap: 1.2rem;
  margin: 1rem 0 0.6rem;
  z-index: 2;
}
.divine-icon {
  filter: drop-shadow(0 2px 8px rgba(255,255,255,0.4));
  animation: pulse 3s infinite ease-in-out;
}
.divine-icon:nth-child(2) {
  animation-delay: 1s;
}
.divine-icon:nth-child(3) {
  animation-delay: 2s;
}
@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.15); opacity: 1; }
}
.wait-message {
  font-size: 1.05rem;
  color: #9370db;
  text-align: center;
  margin: 0.5rem 0 1rem;
  max-width: 85%;
  font-family: 'Cormorant Garamond', serif;
  line-height: 1.6;
  background: rgba(255,255,255,0.4);
  padding: 0.6rem 1.2rem;
  border-radius: 1rem;
  box-shadow: 0 2px 12px rgba(147,112,219,0.05);
  z-index: 2;
  position: relative;
}
</style> 