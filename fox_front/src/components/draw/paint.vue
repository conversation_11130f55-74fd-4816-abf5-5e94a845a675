<template>
  <div class="page-wrapper">
    <div class="background-elements"></div>
    <div class="image-generator-container" :class="{ 'divine-presence': showDivinePresence }">
      <AppHeader @toggle-divine-presence="handleDivinePresence" 
        :draw_consume_points="config.draw_consume_points" />
      <div class="divine-header">
        <div class="sparkle left"></div>
        <h1 class="title">阿佛洛狄忒的画廊</h1>
        <div class="sparkle right"></div>
        <p class="subtitle">
          <span v-for="(char, index) in '将你的想象化为神圣艺术'" :key="index" class="animated-char">{{ char }}</span>
        </p>
      </div>

      <div class="form-container">
        <!-- Mode Selection -->
        <div class="mode-selector">
          <button :class="['mode-button', { active: mode === 'generate' }]" @click="mode = 'generate'">
            <i class="mode-icon generate-icon"></i>
            文生图
          </button>
          <button :class="['mode-button', { active: mode === 'edit' }]" @click="changeMode('edit')">
            <i class="mode-icon edit-icon"></i>
            图生图
          </button>
        </div>

        <!-- Generate Mode -->
        <div v-if="mode === 'generate'" class="mode-content">
          <!-- Prompt Input -->
          <div class="input-group">
            <label for="prompt" class="label">描绘你的愿景</label>
            <div class="textarea-wrapper">
              <textarea id="prompt" v-model="prompt" :maxlength="1000" class="prompt-input"
                placeholder="描述你想要创作的画面，例如：'沐浴在金色阳光中的阿佛洛狄忒神庙，周围绽放着盛开的玫瑰...'" @input="validatePrompt"></textarea>
              <span class="char-count">{{ prompt.length }}/1000</span>
            </div>
          </div>

          <!-- Size and Style Selection -->
          <div class="input-group">
            <label class="label">{{ mode === 'generate' ? '画布设置' : '编辑设置' }}</label>
            <div class="settings-buttons">
              <!-- Size Selection Button -->
              <button class="settings-button" @click="handleSizeClick" :class="{ 'has-value': currentSize }">
                <i class="setting-icon size-icon"></i>
                <div class="setting-text">
                  <span class="setting-label">画布尺寸</span>
                  <span class="setting-value">{{sizeOptions.find(opt => opt.value === currentSize)?.title || '选择尺寸'
                    }}</span>
                </div>
                <i class="arrow-icon"></i>
              </button>

              <!-- Style Selection Button -->
              <button class="settings-button" @click="handleStyleClick" :class="{ 'has-value': currentStyle }">
                <i class="setting-icon style-icon"></i>
                <div class="setting-text">
                  <span class="setting-label">艺术风格</span>
                  <span class="setting-value">{{styleOptions.find(opt => opt.value === currentStyle)?.title || '选择风格'
                    }}</span>
                </div>
                <i class="arrow-icon"></i>
              </button>
            </div>
          </div>

        </div>

        <!-- Edit Mode -->
        <div v-else class="mode-content">
          <div class="edit-layout">
            <!-- Image Upload -->
            <div class="edit-layout-left">
              <ImageUploader label="上传原始图片" @update:images="handleImagesUpdate" @mask-created="handleMaskCreated"
                @error="showPermanentError" :existing-mask="selectedMask" ref="imageUploader" />
            </div>

            <div class="edit-layout-right">
              <!-- Edit Settings -->
              <div class="input-group">
                <label class="label">编辑设置</label>
                <div class="settings-buttons">
                  <!-- Size Selection Button -->
                  <button class="settings-button" @click="handleSizeClick" :class="{ 'has-value': editSize }">
                    <i class="setting-icon size-icon"></i>
                    <div class="setting-text">
                      <span class="setting-label">画布尺寸</span>
                      <span class="setting-value">{{sizeOptions.find(opt => opt.value === editSize)?.title || '选择尺寸'
                        }}</span>
                    </div>
                    <i class="arrow-icon"></i>
                  </button>

                  <!-- Style Selection Button -->
                  <button class="settings-button" @click="handleStyleClick" :class="{ 'has-value': editStyle }">
                    <i class="setting-icon style-icon"></i>
                    <div class="setting-text">
                      <span class="setting-label">艺术风格</span>
                      <span class="setting-value">{{styleOptions.find(opt => opt.value === editStyle)?.title || '选择风格'
                        }}</span>
                    </div>
                    <i class="arrow-icon"></i>
                  </button>
                </div>
              </div>

              <!-- Edit Prompt -->
              <div class="input-group">
                <label for="editPrompt" class="label">编辑描述</label>
                <div class="textarea-wrapper">
                  <textarea id="editPrompt" v-model="editPrompt" :maxlength="32000" class="prompt-input"
                    placeholder="描述你想要对图片进行的编辑，例如：'将天空改为粉色，添加飘落的花瓣...'" @input="validateEditPrompt"></textarea>
                  <span class="char-count">{{ editPrompt.length }}/32000</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Size Popover -->
        <div v-if="showSizeDrawer" class="size-popover-overlay" @click="showSizeDrawer = false">
          <div class="size-popover" @click.stop>
            <div class="size-options-grid">
              <button v-for="option in sizeOptions" :key="option.value" class="size-option"
                :class="{ active: currentSize === option.value }" @click="selectSize(option.value)">
                <i :class="['size-option-icon', option.iconClass]"></i>
                <span class="size-option-title">{{ option.title }}</span>
                <span class="size-option-value">{{ option.value }}</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Style Popover -->
        <div v-if="showStylePopover" class="popover-overlay" @click="closeStylePopover">
          <div class="popover-content" @click.stop>
            <div class="popover-header">
              <h3>选择艺术风格</h3>
              <button class="close-button" @click="closeStylePopover">×</button>
            </div>
            <div class="popover-body">
              <div class="options-grid">
                <button v-for="style in styleOptions" :key="style.value"
                  :class="['option-button', { active: currentStyle === style.value }]"
                  @click="selectStyle(style.value)">
                  <div class="option-content">
                    <img :src="style.preview" :alt="style.title" class="style-preview-img">
                    <div class="option-text">
                      <span class="option-title">{{ style.title }}</span>
                    </div>
                    <span v-if="['anime', 'realistic', 'ghibli'].includes(style.value)" class="hot-tag">热门</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Button -->
        <button @click="handleAction" :disabled="!isValid || loading" class="generate-button">
          <span v-if="!loading" class="button-content">
            <i class="wand-icon"></i>
            {{ mode === 'generate' ? '开始创作' : '开始编辑' }}
          </span>
          <span v-else class="button-content loading-content">
            <i class="wand-icon loading-wand"></i>
            {{ currentTip }}
          </span>
        </button>
      </div>

      <!-- Results Display -->
      <transition-group name="fade" tag="div" class="results-container" v-if="images.length > 0 || permanentError">
        <h3 class="results-title">{{ permanentError ? '创作过程中遇到了问题' : '你的艺术作品' }}</h3>
        <div v-if="permanentError" class="error-display">
          <div class="error-display-content">
            <div class="error-display-text">
              <p class="error-message">{{ permanentError }}</p>
            </div>
            <div class="error-actions">
              <button v-if="errorCode === 1000" class="recharge-button" @click="handleRecharge">
                立即充值
              </button>
              <button v-else class="retry-button" @click="handleRetry">
                重新尝试
              </button>
            </div>
          </div>
        </div>
        <div v-else class="image-grid">
          <div v-for="(image, index) in images" :key="index" class="image-card">
            <div class="image-wrapper">
              <img :src="getImageSrc(image)" :alt="`生成的图像 ${index + 1}`" class="generated-image">
              <div class="image-overlay">
                <div class="image-badge">作品 {{ index + 1 }}</div>
              </div>
            </div>
            <div class="image-actions">
              <button class="art-action-btn view-btn" @click="openImageModal(getImageSrc(image), index)">
                <svg class="action-icon" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor"
                  stroke-width="2">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
                查看
              </button>
              <button class="art-action-btn download-btn" @click="downloadImage(getImageSrc(image))">
                <svg class="action-icon" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor"
                  stroke-width="2">
                  <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                下载
              </button>
              <button class="art-action-btn edit-btn" @click="editExistingImage(getImageSrc(image))">
                <svg class="action-icon" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor"
                  stroke-width="2">
                  <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"></path>
                  <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
                编辑
              </button>
            </div>
          </div>
        </div>
      </transition-group>

      <!-- 图片查看弹窗 -->
      <div v-if="imageModal.visible" class="divine-image-modal" @click="closeImageModal">
        <div class="modal-backdrop"></div>
        <div class="modal-content" @click.stop>
          <button class="modal-close" @click="closeImageModal">×</button>
          <img :src="imageModal.src" :alt="`高清图像 ${imageModal.index + 1}`" class="modal-image">
          <div class="modal-info">
            <div class="modal-title">艺术作品 {{ imageModal.index + 1 }}</div>
            <div class="modal-actions">
              <button class="modal-action-btn" @click="downloadImage(imageModal.src)">
                <svg class="action-icon" viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor"
                  stroke-width="2">
                  <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                下载此作品
              </button>
              <button class="modal-action-btn" @click="editFromModal">
                <svg class="action-icon" viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor"
                  stroke-width="2">
                  <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"></path>
                  <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
                编辑此作品
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- <AuthModal ref="authModal" /> -->

      <!-- Keep AphroditeLoading inside the container -->
      <AphroditeLoading v-if="showEditLoading" id="edit-loading-page" :tip="currentTip" />
      <ErrorAlert
        v-model="showError"
        title="提示"
        :description="errorMessage"
        :duration="3000"
      />
    </div>
  </div>

  <!-- Purchase Component Section - Outside the image-generator-container -->
  <!-- <div class="purchase-section">
    <h2 class="purchase-heading">升级您的会员计划</h2>
    <p class="purchase-subheading">解锁更多创作功能，提升您的创作体验</p>
    <Purchase />
  </div> -->
  <!-- <NotificationDialog /> -->

  <!-- Copyright Section -->
  <!-- <footer class="copyright-section">
    <p class="copyright-text">© {{ new Date().getFullYear() }} {{ config.systemName || '阿佛洛狄忒的画廊' }}. All rights
      reserved.</p>
  </footer> -->
</template>

<script setup>
import { ref, computed, onUnmounted, nextTick, onMounted } from 'vue'
import api from '@/axios'
import AphroditeLoading from './AphroditeLoading.vue'
// import AuthModal from './AuthModal.vue'
import useUserStore from '@/store/user';
// import Purchase from './purchase.vue'
import AppHeader from './AppHeader.vue'
// import NotificationDialog from './NotificationDialog.vue'
import ImageUploader from './ImageUploader.vue'
// import { useScriptLoader } from '@/useScriptLoader'
import ErrorAlert from './ErrorAlert.vue'
import { useRouter } from 'vue-router'
const router = useRouter()
// Add new state
const showDivinePresence = ref(false)

// Add new method
const handleDivinePresence = (value) => {
  showDivinePresence.value = value
}

// State
const mode = ref('generate')
// Generate mode data
const prompt = ref('')
const size = ref('1024x1024')
// Edit mode data
const selectedImages = ref([])
const selectedImagePreviews = ref([])
const editPrompt = ref('')
const editSize = ref('1024x1024')
const editStyle = ref('none')
const quality = ref('high')
const selectedMask = ref(null) // 添加遮罩状态
// Common data
const images = ref([])
const loading = ref(false)
const errorMessage = ref(null)

const imageUploader = ref(null)  // Add this line

// Add these to the script setup section
const sizeOptions = [
  { value: '1024x1024', title: '方形', iconClass: 'size-large' },
  { value: '1024x1536', title: '竖图', iconClass: 'size-vertical' },
  { value: '1536x1024', title: '横图', iconClass: 'size-horizontal' },
  { value: 'auto', title: '自动', iconClass: 'size-auto' }
]



const selectedStyle = ref('none')
import nonePreview from '@/assets/draw/styles/none.png'
import ghibliPreview from '@/assets/draw/styles/ghibli.webp'
import animePreview from '@/assets/draw/styles/anime.webp'
import realisticPreview from '@/assets/draw/styles/realistic.webp'
import disneyPreview from '@/assets/draw/styles/disney.webp'
import pixarPreview from '@/assets/draw/styles/pixel.webp'
import snoopyPreview from '@/assets/draw/styles/snoopy.webp'
import comicPreview from '@/assets/draw/styles/comics.webp'
// Add style options
const styleOptions = [
  { value: 'none', title: '无风格', description: '保持原始风格', preview: nonePreview },
  { value: 'ghibli', title: '吉卜力', description: '吉卜力风格', preview: ghibliPreview },
  { value: 'anime', title: '二次元风格', description: '日系动漫风格', preview: animePreview },
  { value: 'realistic', title: '写实风格', description: '真实细腻质感', preview: realisticPreview },
  { value: 'disney', title: '迪士尼风格', description: '迪士尼动画风格', preview: disneyPreview },
  { value: 'pixar', title: '皮克斯风格', description: '皮克斯3D风格', preview: pixarPreview },
  { value: 'snoopy', title: '史努比风格', description: '史努比风格', preview: snoopyPreview },
  { value: 'comic', title: '四格漫画', description: '4-grid comics, need to have a sense of story, irasutoya style, Vibrant Colors, Low Angle', preview: comicPreview }
]

// Computed
const isValid = computed(() => {
  if (mode.value === 'generate') {
    return prompt.value.length > 0 &&
      prompt.value.length <= 1000
  } else {
    return selectedImages.value.length > 0 &&
      editPrompt.value.length > 0 &&
      editPrompt.value.length <= 32000
  }
})

// Methods
const validatePrompt = () => {
  if (prompt.value.length > 1000) {
    prompt.value = prompt.value.slice(0, 1000)
  }
}
const changeMode = (value) => {
  mode.value = value
  console.log(mode.value)
}
const validateEditPrompt = () => {
  if (editPrompt.value.length > 32000) {
    editPrompt.value = editPrompt.value.slice(0, 32000)
  }
}

// 处理从ImageUploader组件接收到的图片更新
const handleImagesUpdate = (images) => {
  selectedImages.value = images
  // 清除现有预览
  selectedImagePreviews.value = []
  // 清除遮罩数据
  selectedMask.value = null

  // 为每个图片创建预览
  if (images && images.length > 0) {
    images.forEach(file => {
      const reader = new FileReader()
      reader.onload = e => {
        selectedImagePreviews.value.push(e.target.result)
      }
      reader.readAsDataURL(file)
    })
  }
}

// 从URL加载图片并添加到上传组件
const handleImageFiles = (files) => {
  if (!files || files.length === 0) return

  // 清除遮罩数据，因为加载了新图片
  selectedMask.value = null

  // 追加新文件而不是覆盖
  selectedImages.value = [...selectedImages.value, ...files]

  // 为新添加的文件创建预览
  files.forEach(file => {
    const reader = new FileReader()
    reader.onload = e => {
      selectedImagePreviews.value.push(e.target.result)
    }
    reader.readAsDataURL(file)
  })
}

// Loading tips for edit mode
const loadingTips = [
  '阿佛洛狄忒正在拿起她的画笔',
  '神圣的灵感正在降临',
  '画布上浮现出神秘的光辉',
  '艺术女神在细细描绘每一笔',
  '美的奇迹即将诞生',
  '神圣之手正在润色你的创想',
  '色彩与灵感在画布上交融',
  '阿佛洛狄忒正为你点亮艺术之光'
]
const showEditLoading = ref(false)
const currentTip = ref(loadingTips[0])
let tipTimer = null

const scrollToLoading = () => {
  nextTick(() => {
    const el = document.getElementById('edit-loading-page')
    if (el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  })
}

function startEditLoading() {
  showEditLoading.value = true
  let idx = 0
  currentTip.value = loadingTips[idx]
  tipTimer = setInterval(() => {
    idx = (idx + 1) % loadingTips.length
    currentTip.value = loadingTips[idx]
  }, 5000)
  scrollToLoading()
}
// startEditLoading()
function stopEditLoading() {
  showEditLoading.value = false
  if (tipTimer) {
    clearInterval(tipTimer)
    tipTimer = null
  }
}
// startEditLoading()
onUnmounted(() => {
  if (tipTimer) clearInterval(tipTimer)
})
const showError = ref(false)

const handleAction = () => {
  const userStore = useUserStore()
  if (!userStore.isLoggedIn) {
    showError.value = true
    errorMessage.value = '请先登录'
    return
  }

  if (mode.value === 'generate') {
    startEditLoading()
    generateImages().finally(() => {
      stopEditLoading()
    })
  } else {
    startEditLoading()
    editImages().finally(() => {
      stopEditLoading()
    })
  }
}

// Add scroll to form function
const scrollToForm = () => {
  const formElement = document.querySelector('.form-container')
  if (formElement) {
    formElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}

// Modify handleRecharge function
const handleRecharge = () => {
  router.push('/purchase')
}

// Modify retry handler to only scroll
const handleRetry = () => {
  scrollToForm()
  // Remove handleAction call
}

// Add new state for error code
const errorCode = ref(null)

// Modify generateImages function
const generateImages = async () => {
  if (!isValid.value) return

  loading.value = true
  permanentError.value = null
  errorCode.value = null
  images.value = []

  try {
    let finalPrompt = prompt.value
    if (selectedStyle.value && selectedStyle.value !== 'none') {
      const style = styleOptions.find(s => s.value === selectedStyle.value)
      if (style) {
        finalPrompt += `, ${style.description}`
      }
    }

    const res = await api.post('/api/generate-image', {
      prompt: finalPrompt,
      n: 1,
      size: size.value,
      style: selectedStyle.value,
      model: 'gpt-image-1'
    }, {
      timeout: 120000 // 2分钟超时
    })
    if (res.data.code !== 0) {
      errorCode.value = res.data.code
      throw new Error(res.data.msg || '生成图像失败')
    }
    images.value = res.data.data.data || []
  } catch (err) {
    permanentError.value = err.message === 'timeout of 120000ms exceeded'
      ? '生成图像超时，你可以稍后去我的作品查看'
      : (err.message || '生成图像时发生错误，请稍后重试。')
    console.error('错误:', err)
  } finally {
    loading.value = false
  }
}

// Modify editImages function similarly
const editImages = async () => {
  if (!isValid.value) return

  loading.value = true
  permanentError.value = null
  errorCode.value = null
  images.value = []

  try {
    let finalPrompt = editPrompt.value
    if (editStyle.value && editStyle.value !== 'none') {
      const style = styleOptions.find(s => s.value === editStyle.value)
      if (style) {
        finalPrompt += `, ${style.description}`
      }
    }

    const formData = new FormData()
    selectedImages.value.forEach((image, index) => {
      formData.append('images', image)
    })
    formData.append('prompt', finalPrompt)
    formData.append('size', editSize.value)
    formData.append('style', editStyle.value)
    formData.append('model', 'gpt-image-1')
    formData.append('quality', quality.value)

    if (selectedMask.value) {
      const base64Data = selectedMask.value.split(',')[1]
      const byteCharacters = atob(base64Data)
      const byteNumbers = new Array(byteCharacters.length)
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i)
      }
      const byteArray = new Uint8Array(byteNumbers)
      const blob = new Blob([byteArray], { type: 'image/png' })

      formData.append('mask', blob, 'mask.png')
    }

    const res = await api.post('/api/edit-image', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
      timeout: selectedImages.value.length * 60000 * 2 // 每张图片2分钟超时
    })

    if (res.data.code !== 0) {
      errorCode.value = res.data.code
      throw new Error(res.data.msg || '编辑图像失败')
    }
    images.value = res.data.data.data || []
  } catch (err) {
    permanentError.value = err.message === 'timeout of 120000ms exceeded'
      ? '编辑图像超时，你可以稍后去我的作品查看'
      : (err.message || '编辑图像时发生错误，请稍后重试。')
    console.error('错误:', err)
  } finally {
    loading.value = false
  }
}

const downloadImage = (url) => {
  const link = document.createElement('a')
  link.href = url
  link.download = `divine-creation-${Date.now()}.png`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 获取图片src，支持url和base64
const getImageSrc = (image) => {
  if (image.url) return image.url
  if (image.b64_json) return `data:image/png;base64,${image.b64_json}`
  return ''
}

// 图片查看弹窗状态
const imageModal = ref({
  visible: false,
  src: '',
  index: 0
})

// 打开图片弹窗
const openImageModal = (url, index) => {
  imageModal.value = {
    visible: true,
    src: url,
    index: index
  }
  // 防止滚动
  document.body.style.overflow = 'hidden'
}

// 关闭图片弹窗
const closeImageModal = () => {
  imageModal.value.visible = false
  document.body.style.overflow = 'auto'
}

// 从弹窗编辑图片
const editFromModal = () => {
  editExistingImage(imageModal.value.src)
  closeImageModal()
}
// 编辑已有图片
const editExistingImage = (url) => {
  mode.value = 'edit';
  selectedMask.value = null;

  const loadImage = async (url) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const file = new File([blob], "artwork.png", { type: "image/png" });

      if (imageUploader.value) {
        imageUploader.value.clearAllImages();
      }

      if (imageUploader.value) {
        imageUploader.value.handleImageFiles([file]);
      } else {
        handleImageFiles([file]);
      }
    } catch (err) {
      permanentError.value = "无法加载图片用于编辑，请稍后重试。"
      console.error(err)
    }
  }

  loadImage(url).catch(err => {
    permanentError.value = "无法加载图片用于编辑，请稍后重试。"
    console.error(err)
  })
}

// Add new state for popovers
const showSizePopover = ref(false)
const showStylePopover = ref(false)

// Add selection methods
const handleSizeClick = () => {
  showSizeDrawer.value = true
  showStylePopover.value = false
}

const handleStyleClick = () => {
  showStylePopover.value = true
  showSizePopover.value = false
}

const selectSize = (value) => {
  if (mode.value === 'generate') {
    size.value = value
  } else {
    editSize.value = value
  }
  showSizeDrawer.value = false
}

const selectStyle = (value) => {
  if (mode.value === 'generate') {
    selectedStyle.value = value
  } else {
    editStyle.value = value
  }
  showStylePopover.value = false
}

const closeStylePopover = () => {
  showStylePopover.value = false
}

// Computed properties for current values
const currentSize = computed(() => mode.value === 'generate' ? size.value : editSize.value)
const currentStyle = computed(() => mode.value === 'generate' ? selectedStyle.value : editStyle.value)


// const authModal = ref(null)

    // const showAuth = () => {
    //     authModal.value.showDialog('login')
    //   }

// Add new state for size drawer
const showSizeDrawer = ref(false)


// 修改错误处理函数
const showPermanentError = (message) => {
  permanentError.value = message
}

// 处理从ImageUploader组件接收到的遮罩
const handleMaskCreated = (maskDataUrl) => {
  selectedMask.value = maskDataUrl
}
const config = ref({})
const getConfig = async () => {
  const res = await api.post('/api/config/get',
    ["systemName", "draw_consume_points", "scripts"
    ])
  if (res.status !== 200) {
    ElMessage.error('获取配置失败')
    return
  }
  config.value = res.data.data
  console.log(config.value)
}
onMounted(async () => {
  await getConfig()
  // Add animation delay for each character
  const chars = document.querySelectorAll('.animated-char')
  chars.forEach((char, index) => {
    char.style.setProperty('--char-index', index)
  })
})


// Add new state
const permanentError = ref(null)


</script>

<style scoped>
.page-wrapper {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
}

.background-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  pointer-events: none;
  min-height: 100vh;
  overflow: hidden;
}

.background-elements::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('@/assets/draw/aphrodite-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.18;
  z-index: 2;
  transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.background-elements::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
      rgba(255, 248, 251, 0.9),
      rgba(251, 244, 255, 0.85));
  backdrop-filter: blur(2px);
  z-index: 1;
}

.image-generator-container {
  position: relative;
  z-index: 2;
  max-width: 900px;
  margin: 0 auto;
  padding: 1rem;
  font-family: 'Cormorant Garamond', 'Times New Roman', serif;
  min-height: 80vh;
}

.image-generator-container::before,
.image-generator-container::after {
  display: none;
  /* Hide the old background elements */
}

.image-generator-container.divine-presence~.background-elements::before {
  opacity: 0.85;
}

/* Divine circles animation */
.divine-header {
  text-align: center;
  position: relative;
  margin-bottom: 1.5rem;
  padding: 1rem 0;
  overflow: hidden;
}

.divine-header::before,
.divine-header::after {
  content: '';
  position: absolute;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 192, 203, 0.2) 0%, transparent 70%);
  animation: float 8s infinite ease-in-out;
}

.divine-header::before {
  top: -90px;
  left: -90px;
  animation-delay: 0s;
}

.divine-header::after {
  bottom: -90px;
  right: -90px;
  animation-delay: -4s;
}

@keyframes float {

  0%,
  100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.5;
  }

  50% {
    transform: translate(20px, -20px) scale(1.1);
    opacity: 0.8;
  }
}

.title {
  font-size: 2.7rem;
  font-weight: 300;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #ff69b4, #9370db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 1rem;
  color: #666;
  font-style: italic;
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  gap: 2px;
  font-family: 'Cormorant Garamond', 'Times New Roman', serif;
}

.animated-char {
  opacity: 0;
  animation: popIn 0.5s ease forwards;
  animation-delay: calc(var(--char-index, 0) * 0.1s);
  font-family: 'Cormorant Garamond', 'Times New Roman', serif;
  font-size: 1.1rem;
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.8);
  }

  70% {
    opacity: 1;
    transform: translateY(-2px) scale(1.1);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.form-container {
  background: rgba(255, 255, 255, 0.8);
  padding: 1rem;
  border-radius: 1rem;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.1),
    inset 0 0 20px rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 1.5rem;
  position: relative;
  overflow: hidden;
}

.form-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 70%);
  animation: shimmer 10s infinite linear;
  pointer-events: none;
  z-index: 0;
}

.form-container>* {
  position: relative;
  z-index: 1;
}

@keyframes shimmer {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.input-group {
  margin-bottom: 1rem;
}

.label {
  display: block;
  margin-bottom: 0.4rem;
  color: #4a5568;
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.textarea-wrapper,
.select-wrapper,
.input-wrapper {
  position: relative;
  transition: transform 0.3s ease;
  box-sizing: border-box;
  width: 100%;
}

.textarea-wrapper:focus-within,
.select-wrapper:focus-within,
.input-wrapper:focus-within {
  transform: translateY(-2px);
}

.prompt-input {
  width: 100%;
  min-height: 180px;
  padding: 0.8rem;
  border: 2px solid rgba(147, 112, 219, 0.2);
  border-radius: 1rem;
  resize: vertical;
  transition: all 0.3s ease;
  font-size: 1rem;
  line-height: 1.6;
  background: rgba(255, 255, 255, 0.8);
  box-sizing: border-box;
  outline: none;
  /* 移除默认的黑色轮廓 */
}

.prompt-input:focus {
  border-color: #9370db;
  box-shadow: 0 0 0 2px rgba(147, 112, 219, 0.2);
  background: rgba(255, 255, 255, 0.95);
}

/* 为生成模式的输入框单独设置更大的高度 */
.mode-content[v-if="mode === 'generate'"] .prompt-input {
  min-height: 180px;
}

.textarea-wrapper:focus {
  outline: none;
  border-color: #9370db;
  box-shadow: 0 0 15px rgba(147, 112, 219, 0.2);
}

.char-count {
  position: absolute;
  right: 0.8rem;
  bottom: 0.8rem;
  font-size: 0.8rem;
  color: #9370db;
  opacity: 0.7;
  background: rgba(255, 255, 255, 0.8);
  padding: 0.2rem 0.4rem;
  border-radius: 0.4rem;
  pointer-events: none;
}

.select-wrapper {
  position: relative;
  width: 100%;
}




/* Custom arrow */
.select-arrow {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  pointer-events: none;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%239370db"><path d="M7 10l5 5 5-5z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.select-wrapper:hover .select-arrow {
  opacity: 1;
}

/* Size icons */
.size-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 8px;
  vertical-align: middle;
  background-repeat: no-repeat;
  background-position: center;
}

.size-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}


.generate-button {
  width: 100%;
  padding: 0.7rem;
  background: linear-gradient(135deg, #ff69b4, #9370db);
  color: white;
  border: none;
  border-radius: 0.7rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.generate-button:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
  transition: 0.5s;
}

.generate-button:hover:not(:disabled):before {
  left: 100%;
}

.generate-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}


.results-container {
  margin-top: 2rem;
}

.results-title {
  font-size: 1.2rem;
  color: #2c3e50;
  margin-bottom: 1rem;
  text-align: center;
  font-weight: 300;
  position: relative;
}

.results-title:before,
.results-title:after {
  content: '';
  position: absolute;
  top: 50%;
  width: 30px;
  height: 1px;
  background: linear-gradient(90deg, transparent, #9370db, transparent);
}

.results-title:before {
  left: -40px;
}

.results-title:after {
  right: -40px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.image-card {
  position: relative;
  border-radius: 0.7rem;
  overflow: hidden;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  padding-bottom: 0.5rem;
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
}

.image-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(147, 112, 219, 0.15);
}

.image-wrapper {
  position: relative;
  padding-top: 100%;
  overflow: hidden;
}

.generated-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.image-card:hover .generated-image {
  transform: scale(1.03);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 0.6rem;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), transparent);
  opacity: 0.8;
  transition: opacity 0.3s ease;
  display: flex;
  justify-content: flex-start;
}

.image-badge {
  color: white;
  font-size: 0.75rem;
  background: rgba(147, 112, 219, 0.5);
  padding: 0.15rem 0.5rem;
  border-radius: 1rem;
  backdrop-filter: blur(4px);
  font-family: 'Cormorant Garamond', serif;
  letter-spacing: 0.5px;
}

.image-actions {
  display: flex;
  flex-direction: row;
  gap: 0.4rem;
  padding: 0.4rem 0.4rem;
  justify-content: space-between;
}

.art-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  padding: 0.3rem 0;
  border: none;
  border-radius: 0.4rem;
  font-size: 0.8rem;
  font-family: 'Cormorant Garamond', serif;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  flex: 1;
}

.view-btn {
  background: rgba(255, 255, 255, 0.3);
  color: rgba(147, 112, 219, 0.9);
  border: 1px solid rgba(147, 112, 219, 0.15);
}

.download-btn {
  background: linear-gradient(135deg, #ffb6d5 0%, #b39ddb 100%);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.edit-btn {
  background: rgba(255, 255, 255, 0.4);
  color: #9370db;
  border: 1px solid rgba(147, 112, 219, 0.2);
}

.art-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 10px rgba(147, 112, 219, 0.15);
}

.download-btn:hover {
  background: linear-gradient(135deg, #ff9cbe 0%, #9d7fcd 100%);
}

.action-icon {
  opacity: 0.85;
}

/* 图片查看弹窗样式 */
.divine-image-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  /* High z-index but only when visible */
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  z-index: 0;
}

.modal-content {
  position: relative;
  max-width: 90vw;
  z-index: 1;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
}

.modal-close {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 2rem;
  height: 2rem;
  background: rgba(255, 255, 255, 0.3);
  border: none;
  border-radius: 50%;
  font-size: 1.5rem;
  line-height: 1;
  color: white;
  cursor: pointer;
  z-index: 2;
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.5);
  transform: rotate(90deg);
}

.modal-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 0.5rem 0.5rem 0 0;
}

.modal-info {
  padding: 1rem;
  background: white;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.modal-title {
  font-family: 'Cormorant Garamond', serif;
  font-size: 1.2rem;
  color: #2c3e50;
  background: linear-gradient(135deg, #ff69b4, #9370db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.modal-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1.2rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-family: 'Cormorant Garamond', serif;
  cursor: pointer;
  transition: all 0.2s ease;
  background: linear-gradient(135deg, rgba(255, 182, 193, 0.3), rgba(147, 112, 219, 0.3));
  color: #9370db;
  border: 1px solid rgba(147, 112, 219, 0.2);
}

.modal-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 10px rgba(147, 112, 219, 0.2);
  background: linear-gradient(135deg, rgba(255, 182, 193, 0.5), rgba(147, 112, 219, 0.5));
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .image-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    max-width: 400px;
  }

  .modal-image {
    max-height: 60vh;
  }

  .modal-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .image-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    max-width: 300px;
  }

  .image-card {
    max-width: 120px;
  }
}

/* Fade transition */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .image-generator-container {
    padding: 0.5rem;
  }

  .title {
    font-size: 1.1rem;
  }

  .form-container {
    padding: 0.5rem;
  }

  .image-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }

  .results-title:before,
  .results-title:after {
    width: 15px;
  }

  .results-title:before {
    left: -20px;
  }

  .results-title:after {
    right: -20px;
  }
}



/* Mode Selector Styles */
.mode-selector {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.2rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 0.7rem;
}

.mode-button {
  flex: 1;
  padding: 0.5rem;
  border: none;
  border-radius: 0.5rem;
  background: transparent;
  /* color: #666; */
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3rem;
  position: relative;
}

.mode-button.active {
  background: white;
  color: #9370db;
  box-shadow: 0 2px 10px rgba(147, 112, 219, 0.2);
  border: 1px solid rgba(147, 112, 219, 0.2);
}

.mode-icon {
  width: 18px;
  height: 18px;
  background-repeat: no-repeat;
  background-position: center;
}

.generate-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%239370db" stroke-width="1.5"><path d="M12 2l1.5 3.5L16 7l-2.5 2.5L12 16l-1.5-6.5L8 7l2.5-1.5L12 2z" stroke-linecap="round" stroke-linejoin="round"/><path d="M5 13.5c0 .8.2 1.5.5 2.1.3.6.8 1.1 1.4 1.4.6.3 1.3.5 2.1.5.8 0 1.5-.2 2.1-.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M19 13.5c0 .8-.2 1.5-.5 2.1-.3.6-.8 1.1-1.4 1.4-.6.3-1.3.5-2.1.5-.8 0-1.5-.2-2.1-.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16c-.8 2.4-2.3 4-4.5 4.8M12 16c.8 2.4 2.3 4 4.5 4.8" stroke-linecap="round" stroke-linejoin="round"/><path d="M8.5 3.5C6.8 4.5 5.5 6 4.5 8M15.5 3.5c1.7 1 3 2.5 4 4.5" stroke-linecap="round" stroke-linejoin="round"/><circle cx="12" cy="12" r="10" stroke-dasharray="2 2"/></svg>');
}

.edit-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%239370db" stroke-width="1.5"><path d="M12 3c4.97 0 9 4.03 9 9s-4.03 9-9 9-9-4.03-9-9 4.03-9 9-9z" stroke-dasharray="3 3"/><path d="M12 7c2.76 0 5 2.24 5 5s-2.24 5-5 5-5-2.24-5-5 2.24-5 5-5z" stroke-dasharray="2 2"/><path d="M15 12c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3z"/><path d="M12 8v8" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 12h8" stroke-linecap="round" stroke-linejoin="round"/><path d="M3.5 8.5c1-1.5 2.5-2.5 4-3M20.5 8.5c-1-1.5-2.5-2.5-4-3" stroke-linecap="round" stroke-linejoin="round"/><path d="M3.5 15.5c1 1.5 2.5 2.5 4 3M20.5 15.5c-1 1.5-2.5 2.5-4 3" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 编辑模式布局样式 */
.edit-layout-left {
  flex: 1;
  min-width: 0;
}


/* Mode Content Transition */
.mode-content {
  transition: all 0.3s ease;
}

/* Responsive Adjustments */
@media (max-width: 640px) {
  .mode-selector {
    flex-direction: row;
    gap: 0.2rem;
  }

  .mode-button {
    width: 100%;
  }

  .upload-area {
    min-height: 60px;
  }

  .button-group {
    flex-direction: column;
  }

  .option-button {
    width: 100%;
  }

  .option-content {
    flex-direction: row;
    justify-content: center;
  }

  .option-icon {
    margin-bottom: 0;
    margin-right: 0.5rem;
  }

  .settings-buttons {
    flex-direction: row;
  }
}

/* Add these styles to the style section */
.button-group {
  display: flex;
  gap: 0.3rem;
  width: 100%;
}

.option-button {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  padding: 0.4rem;
  border: 2px solid transparent;
  border-radius: 12px;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  height: auto;
  flex: 1;
}

.option-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: rgba(147, 112, 219, 0.4);
}

.option-button.active {
  border-color: #9370db;
  box-shadow: 0 0 0 2px rgba(147, 112, 219, 0.3);
  background: rgba(147, 112, 219, 0.1);
}

.option-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.2rem;
  text-align: center;
}

.option-title {
  display: block;
  font-size: 0.9rem;
  color: #2c3e50;
  margin-bottom: 0.1rem;
  text-align: center;
}

.option-icon {
  width: 18px;
  height: 18px;
  margin-bottom: 0.1rem;
}

.option-text {
  text-align: center;
}

.option-value {
  display: block;
  font-size: 0.8rem;
  color: #666;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .button-group {
    flex-direction: column;
  }

  .option-button {
    width: 100%;
  }

  .option-content {
    flex-direction: row;
    justify-content: center;
  }

  .option-icon {
    margin-bottom: 0;
    margin-right: 0.5rem;
  }
}



/* Add animation for the icon */
.mode-button:hover .generate-icon {
  animation: starlight 2s ease infinite;
}

@keyframes starlight {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
    filter: drop-shadow(0 0 2px rgba(147, 112, 219, 0.3));
  }

  50% {
    transform: scale(1.15) rotate(180deg);
    opacity: 1;
    filter: drop-shadow(0 0 8px rgba(147, 112, 219, 0.6));
  }

  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0.8;
    filter: drop-shadow(0 0 2px rgba(147, 112, 219, 0.3));
  }
}

/* Add animation for the edit icon */
.mode-button:hover .edit-icon {
  animation: rippleTransform 2s ease infinite;
}

@keyframes rippleTransform {
  0% {
    transform: scale(1);
    opacity: 0.8;
    filter: drop-shadow(0 0 2px rgba(147, 112, 219, 0.3));
  }

  50% {
    transform: scale(1.15);
    opacity: 1;
    filter: drop-shadow(0 0 8px rgba(147, 112, 219, 0.6));
  }

  75% {
    transform: scale(1.1) rotate(15deg);
    opacity: 0.9;
    filter: drop-shadow(0 0 5px rgba(147, 112, 219, 0.5));
  }

  100% {
    transform: scale(1);
    opacity: 0.8;
    filter: drop-shadow(0 0 2px rgba(147, 112, 219, 0.3));
  }
}

/* Add these styles to the style section */
.mode-button {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: white;
  border: 1px solid rgba(147, 112, 219, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mode-button:hover {
  background: rgba(147, 112, 219, 0.05);
  border-color: rgba(147, 112, 219, 0.3);
  transform: translateY(-2px);
}

.mode-button.active {
  background: rgba(147, 112, 219, 0.1);
  border-color: rgba(147, 112, 219, 0.4);
  color: #9370db;
}

.loading-wand {
  animation: wand-pulse 1.5s infinite ease-in-out;
}

@keyframes wand-pulse {

  0%,
  100% {
    filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.8));
    transform: rotate(0deg) scale(1);
  }

  50% {
    filter: drop-shadow(0 0 12px rgba(255, 255, 255, 0.9)) drop-shadow(0 0 5px rgba(255, 182, 193, 0.5));
    transform: rotate(15deg) scale(1.15);
  }
}

/* 移除重复的loading样式区域 */
.generate-button .loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.7rem;
  font-size: 1.1rem;
  color: white;
  font-family: 'Cormorant Garamond', serif;
  letter-spacing: 1px;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5), 0 0 20px rgba(255, 182, 193, 0.3);
}

.size-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%239370db"><path d="M19 12h-2v3h-3v2h5v-5zM7 9h3V7H5v5h2V9zm14-6H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16.01H3V4.99h18v14.02z"/></svg>');
}

.style-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%239370db"><path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8zm-5.5 9c-.83 0-1.5-.67-1.5-1.5S5.67 9 6.5 9 8 9.67 8 10.5 7.33 12 6.5 12zm3-4C8.67 8 8 7.33 8 6.5S8.67 5 9.5 5s1.5.67 1.5 1.5S10.33 8 9.5 8zm5 0c-.83 0-1.5-.67-1.5-1.5S13.67 5 14.5 5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/></svg>');
}

/* Adjust existing styles */
.button-group {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.3rem;
}

.option-button {
  height: auto;
  padding: 0.4rem;
}

.option-content {
  flex-direction: column;
  text-align: center;
}

.option-title {
  font-size: 0.9rem;
  margin-bottom: 0.1rem;
}

.option-value {
  font-size: 0.75rem;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .button-group {
    grid-template-columns: 1fr;
  }

  .option-content {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
  }

  .option-text {
    text-align: left;
  }
}

/* Settings Buttons Styles */
.settings-buttons {
  display: flex;
  flex-direction: row;
  gap: 0.8rem;
}

.settings-button {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.8rem;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(147, 112, 219, 0.2);
  border-radius: 0.7rem;
  cursor: pointer;
  transition: all 0.3s ease;
}


.settings-button:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(147, 112, 219, 0.4);
  transform: translateY(-1px);
}

.settings-button.has-value {
  background: rgba(147, 112, 219, 0.05);
  border-color: rgba(147, 112, 219, 0.3);
}

.setting-icon {
  width: 24px;
  height: 24px;
  opacity: 0.8;
}

.setting-text {
  flex: 1;
  text-align: left;
}

.setting-label {
  display: block;
  font-size: 0.8rem;
  color: #666;
}

.setting-value {
  display: block;
  font-size: 1rem;
  color: #2c3e50;
  margin-top: 0.1rem;
}

.arrow-icon {
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%239370db"><path d="M7 10l5 5 5-5z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.6;
  transition: transform 0.3s ease;
}

.settings-button:hover .arrow-icon {
  transform: translateY(2px);
}

/* Popover Styles */
.popover-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.popover-content {
  width: 90%;
  max-width: 800px;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: slideUp 0.3s ease;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.popover-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid rgba(147, 112, 219, 0.1);
}

.popover-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #2c3e50;
  font-weight: 500;
}

.close-button {
  width: 2rem;
  height: 2rem;
  border: none;
  background: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(147, 112, 219, 0.1);
  color: #9370db;
  transform: rotate(90deg);
}

.popover-body {
  padding: 1rem;
  overflow-y: auto;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.2rem;
  padding: 0.8rem;
}

.option-button {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  padding: 0;
  border: 2px solid transparent;
  border-radius: 12px;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.option-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.option-button.active {
  border-color: #9370db;
  box-shadow: 0 0 0 2px rgba(147, 112, 219, 0.3);
}

.option-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.style-preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

.option-text {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 8px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  border-radius: 0 0 10px 10px;
}

.option-title {
  display: block;
  font-size: 0.9rem;
  color: white;
  margin: 0;
  text-align: center;
}

.hot-tag {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 2px 6px;
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  color: white;
  font-size: 0.7rem;
  border-radius: 4px;
  z-index: 1;
}

/* 移动端适配优化 */
@media (max-width: 768px) {
  .popover-content {
    width: 100%;
    height: 100%;
    max-width: none;
    max-height: none;
    border-radius: 0;
    margin: 0;
  }

  .popover-header {
    padding: 1rem;
    position: sticky;
    top: 0;
    background: white;
    z-index: 1;
  }

  .popover-body {
    padding: 0.5rem;
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .options-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
    padding: 0.5rem;
  }

  .option-button {
    aspect-ratio: 4/3;
  }

  .option-content {
    flex-direction: row;
    align-items: center;
  }

  .style-preview-img {
    width: 100%;
    height: 100%;
    border-radius: 8px;
  }

  .option-text {
    padding: 6px;
  }

  .option-title {
    font-size: 0.85rem;
  }

  .hot-tag {
    top: 6px;
    right: 6px;
    font-size: 0.65rem;
  }
}

@media (max-width: 480px) {
  .options-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.6rem;
    padding: 0.4rem;
  }

  .option-button {
    aspect-ratio: 3/2;
  }

  .popover-header h3 {
    font-size: 1.1rem;
  }

  .close-button {
    width: 1.8rem;
    height: 1.8rem;
    font-size: 1.3rem;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Add new styles for the edit layout */
.edit-layout {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.edit-layout-left {
  flex: 1;
  min-width: 0;
  /* 防止flex子项溢出 */
}

.edit-layout-right {
  flex: 1;
  min-width: 0;
  /* 防止flex子项溢出 */
  display: flex;
  flex-direction: column;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  .edit-layout {
    flex-direction: column;
    gap: 1rem;
  }
}

/* 优化上传区域在左右布局下的样式 */
.upload-area {
  height: 100%;
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

.image-preview-grid {
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
}

/* 优化编辑描述在左右布局下的样式 */
.edit-layout-right .textarea-wrapper {
  height: 100%;
}

.edit-layout-right .prompt-input {
  height: 200px;
}

.header-actions {
  position: fixed;
  top: 20px;
  right: 100px;
  z-index: 1000;
}

.header-actions.right {
  right: 20px;
}

.header-actions.left {
  left: 20px;
}



/* Purchase Section Styles */
.purchase-section {
  margin-top: 4rem;
  padding: 2rem 0;
  border-top: 1px solid rgba(147, 112, 219, 0.2);
  text-align: center;
  position: relative;
}

.purchase-heading {
  font-size: 2.2rem;
  font-weight: 300;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #ff69b4, #9370db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.purchase-subheading {
  font-size: 1.1rem;
  color: #666;
  font-style: italic;
  margin-bottom: 2rem;
}

/* Fix z-index for background elements that might be covering purchase section */
.image-generator-container::before,
.image-generator-container::after {
  z-index: -5;
  /* Lower z-index to make sure it doesn't cover the purchase component */
}

@media (max-width: 768px) {
  .purchase-heading {
    font-size: 1.8rem;
  }

  .purchase-subheading {
    font-size: 1rem;
  }
}


.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(147, 112, 219, 0.1);
  color: #9370db;
  transform: rotate(90deg);
}



@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .size-drawer {
    width: 100%;
  }
}

/* Size Popover Styles */
.size-popover-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.size-popover {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  padding: 16px;
  animation: popIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  border: 1px solid rgba(147, 112, 219, 0.2);
}

.size-options-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  width: 280px;
}

.size-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border: 1px solid rgba(147, 112, 219, 0.2);
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.size-option:hover {
  border-color: rgba(147, 112, 219, 0.4);
  transform: translateY(-2px);
  background: rgba(147, 112, 219, 0.02);
}

.size-option.active {
  border-color: #9370db;
  background: rgba(147, 112, 219, 0.1);
}

.size-option-icon {
  font-size: 24px;
  color: #9370db;
  opacity: 0.8;
}

.size-option-title {
  font-size: 14px;
  color: #2c3e50;
}

.size-option.active .size-option-title {
  color: #9370db;
}

@keyframes popIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@media (max-width: 480px) {
  .size-options-grid {
    width: 240px;
    gap: 8px;
  }

  .size-option {
    padding: 12px;
  }
}

.page-wrapper:has(.image-generator-container.divine-presence) .background-elements::before {
  opacity: 0.85;
}



.error-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;
  font-size: 14px;
}

.error-icon {
  width: 20px;
  height: 20px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E") center/contain no-repeat;
}

/* 添加动画 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Copyright Section Styles */
.copyright-section {
  text-align: center;
  padding: 1.5rem 0;
  color: #666;
  font-size: 0.9rem;
  border-top: 1px solid rgba(147, 112, 219, 0.1);
  margin-top: 2rem;
  background-color: transparent;
}

.copyright-text {
  margin: 0;
  font-family: 'Cormorant Garamond', serif;
  opacity: 0.8;
}

@media (max-width: 768px) {
  .copyright-text {
    font-size: 0.8rem;
  }
}

/* Add new divine error styles */
.divine-error-container {
  position: fixed;
  top: 100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  width: 90%;
  max-width: 600px;
  animation: errorSlideDown 0.5s ease-out;
}

.divine-error-content {
  background: linear-gradient(135deg, #fff5f5, #fff0f7);
  border: 1px solid rgba(229, 62, 62, 0.3);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 10px 25px rgba(229, 62, 62, 0.15),
    0 5px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  position: relative;
  backdrop-filter: blur(10px);
}

.divine-error-icon {
  flex-shrink: 0;
  color: #e53e3e;
  animation: errorPulse 2s infinite;
}

.divine-error-message {
  flex: 1;
}

.divine-error-message h3 {
  color: #e53e3e;
  font-size: 1.1rem;
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.divine-error-message p {
  color: #666;
  font-size: 0.95rem;
  margin: 0;
  line-height: 1.5;
}

.divine-error-close {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 2rem;
  height: 2rem;
  border: none;
  background: none;
  font-size: 1.5rem;
  color: #e53e3e;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.divine-error-close:hover {
  background: rgba(229, 62, 62, 0.1);
  transform: rotate(90deg);
}

@keyframes errorSlideDown {
  from {
    opacity: 0;
    transform: translate(-50%, -20px);
  }

  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

@keyframes errorPulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* Add new error display styles */
.error-display {
  width: 100%;
  max-width: 600px;
  margin: 2rem auto;
  /* padding: 0 1rem; */
  animation: errorSlideIn 0.3s ease-out;
}

.error-display-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow:
    0 4px 24px rgba(0, 0, 0, 0.06),
    0 1px 2px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 105, 180, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.error-display-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff69b4, #9370db);
  opacity: 0.8;
}

.error-display-text {
  margin-bottom: 2rem;
}

.error-message {
  font-size: 1.1rem;
  color: #2c3e50;
  margin: 0;
  line-height: 1.6;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.error-message::before {
  content: '!';
  position: absolute;
  left: 50%;
  top: -30%;
  transform: translateX(-50%);
  width: 16px;
  height: 16px;
  background: #ff69b4;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.retry-button,
.recharge-button {
  padding: 0.8rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.retry-button {
  background: rgba(147, 112, 219, 0.1);
  color: #9370db;
  border: 1px solid rgba(147, 112, 219, 0.2);
}

.retry-button::before {
  content: '';
  display: block;
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%239370db" stroke-width="2"><path d="M23 4v6h-6M1 20v-6h6M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15"/></svg>');
  background-size: contain;
}

.recharge-button {
  background: linear-gradient(135deg, #ff69b4, #9370db);
  color: white;
  position: relative;
  overflow: hidden;
}

.recharge-button::before {
  content: '';
  display: block;
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2"><path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/></svg>');
  background-size: contain;
}

.recharge-button::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 60%);
  transform: rotate(45deg);
  animation: shimmerEffect 3s infinite linear;
}

@keyframes shimmerEffect {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.retry-button:hover,
.recharge-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(147, 112, 219, 0.15);
}

.retry-button:hover {
  background: rgba(147, 112, 219, 0.15);
}

.recharge-button:hover {
  background: linear-gradient(135deg, #ff4da6, #8a5cd1);
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 640px) {
  .error-display {
    margin: 1rem auto;
  }

  .error-display-content {
    padding: 1.5rem;
  }

  .error-message {
    font-size: 1rem;
  }

  .error-actions {
    flex-direction: column;
  }

  .retry-button,
  .recharge-button {
    width: 100%;
    justify-content: center;
    padding: 0.7rem 1.5rem;
    font-size: 0.9rem;
  }
}

@media screen and (max-width: 768px) {

  .error-message::before {
    left: 50%;
    top: -15%;
    transform: translateX(-50%);
  }
}
</style>