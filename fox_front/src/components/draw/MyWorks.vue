<template>
  <div class="my-works-container">
    <div class="back-btn-wrapper">
      <button class="back-btn" @click="goToPaint" title="返回创作画廊">
        <span class="back-icon"></span>
        <span class="back-text">返回创作</span>
      </button>
    </div>
    <div class="divine-header">
      <div class="sparkle left"></div>
      <h1 class="title">我的作品</h1>
      <div class="sparkle right"></div>
      <p class="subtitle">这里展示你生成的所有艺术作品</p>
    </div>
    <div class="works-grid">
      <div
        v-for="(work, idx) in works"
        :key="work.id"
        class="work-card"
        @mouseenter="hovered = idx"
        @mouseleave="hovered = null"
      >
        <div class="image-wrapper">
          <template v-if="work.generationStatus === 'success'">
            <img :src="getImageUrl(work.imageId)" :alt="work.prompt" class="work-image" />
            <transition name="fade">
              <div class="overlay" v-if="hovered === idx">
                <div class="overlay-content">
                  <div class="overlay-title">作品 #{{ work.id }}</div>
                  <div class="overlay-desc">{{ work.prompt }}</div>
                  <div class="overlay-actions">
                    <button class="overlay-btn" @click.stop="downloadImage(work.imageId)">
                      <span class="btn-icon download-icon"></span>
                      下载
                    </button>
                  </div>
                </div>
              </div>
            </transition>
          </template>
          <template v-else-if="work.generationStatus === 'generating'">
            <div class="generating-placeholder">
              <div class="loading-spinner"></div>
              <span class="generating-text">正在生成中...</span>
            </div>
          </template>
          <template v-else>
            <div class="error-placeholder">
              <span class="error-icon">❌</span>
              <span class="error-text">{{ work.errorMessage }}</span>
            </div>
          </template>
        </div>
      </div>
    </div>
    <div v-if="loading" class="loading-indicator">
      <div class="loading-spinner"></div>
      <span class="loading-text">正在加载更多作品...</span>
    </div>
    <div v-if="noMore" class="no-more">
      <span>已经到底啦 ~</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import api from '@/axios'
import { ElMessage } from 'element-plus'

const router = useRouter()
const goToPaint = () => {
  router.push('/paint')
}

const works = ref([])
const hovered = ref(null)
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(12)
const noMore = ref(false)

// 获取图片URL
const getImageUrl = (imageId) => {
  // 如果是生产环境，取 window.location.origin
  const baseUrl = import.meta.env.MODE === 'production' ? window.location.origin : import.meta.env.VITE_API_HOST
  return `${baseUrl}/api/download-image/${imageId}`
}

// 下载图片
const downloadImage = async (imageId) => {
  try {
    const response = await api.get(`/api/download-image/${imageId}`, {
      responseType: 'blob'
    })
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.download = `divine-creation-${imageId}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('下载图片失败，请稍后重试')
    console.error('下载图片出错:', error)
  }
}

// 加载作品列表
const loadWorks = async (isLoadMore = false) => {
  if (loading.value || (isLoadMore && noMore.value)) return

  loading.value = true
  try {
    const res = await api.post('/api/image-generation-records/page', {
      pageNum: currentPage.value,
      pageSize: pageSize.value
    })
    if (res.data.code === 0) {
      const newWorks = res.data.data
      if (newWorks.length > 0) {
        const processedWorks = newWorks.map(work => {
          // 处理生成状态
          let generationStatus = 'failed'
          if (work.successful) {
            if (work.status === 2) {
              generationStatus = 'success'
            } else if (work.status === 1) {
              generationStatus = 'generating'
            }
          }
          
          return {
            ...work,
            generationStatus,
            errorMessage: work.errorMessage || '生成失败',
            imageId: work.generatedImages && JSON.parse(work.generatedImages)[0] ? String(JSON.parse(work.generatedImages)[0]) : null
          }
        })
        works.value = isLoadMore ? [...works.value, ...processedWorks] : processedWorks
        currentPage.value++
      } else {
        noMore.value = true
      }
    } else {
      ElMessage.error(res.data.msg || '获取作品列表失败')
    }
  } catch (error) {
    ElMessage.error('获取作品列表失败，请稍后重试')
    console.error('获取作品列表出错:', error)
  } finally {
    loading.value = false
  }
}

// 防抖函数
const debounce = (fn, delay) => {
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 处理滚动事件
const handleScroll = debounce(() => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const windowHeight = window.innerHeight
  const documentHeight = document.documentElement.scrollHeight

  // 当滚动到距离底部100px时加载更多
  if (documentHeight - scrollTop - windowHeight < 100) {
    loadWorks(true)
  }
}, 200)

onMounted(() => {
  loadWorks()
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})

</script>

<style scoped>
.my-works-container {
  max-width: 1100px;
  margin: 0 auto;
  padding: 1.5rem 1rem 2rem 1rem;
  font-family: 'Cormorant Garamond', 'Times New Roman', serif;
  min-height: 80vh;
  position: relative;
  z-index: 1;
}

.my-works-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('@/assets/draw/works-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.18;
  z-index: 0;
  transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.my-works-container::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 248, 251, 0.9),
    rgba(251, 244, 255, 0.85)
  );
  backdrop-filter: blur(2px);
  z-index: -1;
}

.divine-header {
  text-align: center;
  position: relative;
  margin-bottom: 2rem;
  padding: 1rem 0;
  overflow: hidden;
}
.divine-header::before,
.divine-header::after {
  content: '';
  position: absolute;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 192, 203, 0.2) 0%, transparent 70%);
  animation: float 8s infinite ease-in-out;
}
.divine-header::before {
  top: -90px;
  left: -90px;
  animation-delay: 0s;
}
.divine-header::after {
  bottom: -90px;
  right: -90px;
  animation-delay: -4s;
}
@keyframes float {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.5;
  }
  50% {
    transform: translate(20px, -20px) scale(1.1);
    opacity: 0.8;
  }
}
.title {
  font-size: 2.2rem;
  font-weight: 300;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #ff69b4, #9370db);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.subtitle {
  font-size: 1rem;
  color: #666;
  font-style: italic;
  margin-bottom: 1rem;
}
.works-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1.5rem;
  width: 100%;
}
.work-card {
  background: rgba(255,255,255,0.85);
  border-radius: 1.1rem;
  box-shadow: 0 6px 24px rgba(147,112,219,0.10);
  overflow: hidden;
  position: relative;
  transition: box-shadow 0.3s, transform 0.3s;
  cursor: pointer;
}
.work-card:hover {
  box-shadow: 0 12px 32px rgba(147,112,219,0.18);
  transform: translateY(-4px) scale(1.03);
}
.image-wrapper {
  position: relative;
  width: 100%;
  aspect-ratio: 3/4;
  overflow: hidden;
}
.work-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
  display: block;
}
.work-card:hover .work-image {
  transform: scale(1.07);
}
.overlay {
  position: absolute;
  inset: 0;
  /* background: linear-gradient(135deg, rgba(147,112,219,0.70), rgba(255,182,193,0.65)); */
  color: #fff;
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  opacity: 1;
  transition: opacity 0.3s;
  z-index: 2;
  padding: 0;
}
.overlay-content {
  width: 100%;
  padding: 1.1rem 1rem 1.2rem 1rem;
  background: linear-gradient(0deg, rgba(44,62,80,0.18) 60%, transparent 100%);
  border-radius: 0 0 1.1rem 1.1rem;
}
.overlay-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.3rem;
  letter-spacing: 1px;
  color: #fff;
  text-shadow: 0 2px 8px rgba(147,112,219,0.18);
}
.overlay-desc {
  font-size: 0.95rem;
  color: #fff;
  opacity: 0.92;
  text-shadow: 0 1px 4px rgba(147,112,219,0.10);
}
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
@media (max-width: 700px) {
  .works-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.7rem;
  }
  .overlay-content {
    padding: 0.7rem 0.5rem 0.8rem 0.5rem;
  }
}
.back-btn-wrapper {
  position: fixed;
  top: 32px;
  left: 32px;
  z-index: 1001;
}
@media (max-width: 700px) {
  .back-btn-wrapper {
    top: 12px;
    left: 10px;
  }
}
.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #ffb6d5 0%, #b39ddb 100%);
  color: #fff;
  border: none;
  border-radius: 2rem;
  padding: 0.5rem 1.2rem 0.5rem 0.9rem;
  font-size: 1.05rem;
  font-family: 'Cormorant Garamond', serif;
  font-weight: 500;
  box-shadow: 0 4px 16px rgba(147,112,219,0.10);
  cursor: pointer;
  transition: box-shadow 0.25s, transform 0.18s, background 0.3s;
  position: relative;
  overflow: hidden;
}
.back-btn:hover {
  background: linear-gradient(135deg, #ff69b4 0%, #9370db 100%);
  box-shadow: 0 8px 24px rgba(147,112,219,0.18);
  transform: translateY(-2px) scale(1.04);
}
.back-icon {
  width: 20px;
  height: 20px;
  display: inline-block;
  background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.5 16L7.5 10L12.5 4" stroke="%23fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  filter: drop-shadow(0 1px 2px rgba(147,112,219,0.10));
}
.back-text {
  letter-spacing: 1px;
  font-size: 1.05rem;
  color: #fff;
  text-shadow: 0 1px 4px rgba(147,112,219,0.10);
}
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #9370db;
}
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(147, 112, 219, 0.1);
  border-top-color: #9370db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}
.loading-text {
  font-size: 1rem;
  color: #9370db;
  font-style: italic;
}
.no-more {
  text-align: center;
  padding: 2rem;
  color: #999;
  font-style: italic;
  font-size: 1rem;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
.generating-placeholder,
.error-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 1rem;
  text-align: center;
}

.generating-text {
  margin-top: 1rem;
  color: #9370db;
  font-style: italic;
}

.error-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.error-text {
  color: #dc3545;
  font-size: 0.9rem;
  line-height: 1.4;
}
</style> 