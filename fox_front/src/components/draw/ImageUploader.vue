<template>
  <div class="input-group">
    <div class="label-row">
      <label class="label">{{ label }}</label>
      <span v-if="selectedImages.length > 0" class="image-count">
        已选择 {{ selectedImages.length }} 张图片
        <button class="clear-all-btn" @click.stop="clearAllImages">
          <i class="clear-icon"></i>
          清除全部
        </button>
      </span>
    </div>
    <div
      class="upload-area"
      :class="{ 'has-images': selectedImages.length > 0, 'dragover': isDragging }"
      @click="triggerImageUpload"
      @drop.prevent="handleImageDrop"
      @dragover.prevent
      @dragenter.prevent="handleDragEnter"
      @dragleave.prevent="handleDragLeave"
    >
      <input
        type="file"
        ref="imageInput"
        class="file-input"
        accept="image/png,image/jpeg,image/webp"
        @change="handleImageSelect"
        multiple
      >
      <div v-if="!selectedImages.length" class="upload-placeholder">
        <i class="upload-icon"></i>
        <p>点击或拖放图片至此处</p>
        <p class="upload-hint">支持 PNG、JPEG、WEBP 格式，每张小于 25MB</p>
        <p class="upload-hint">最多可上传 {{ maxImages }} 张图片</p>
      </div>
      <div v-else class="image-preview-grid">
        <div v-for="(image, index) in selectedImagePreviews" :key="index" class="preview-item">
          <img :src="image" class="preview-image" alt="Selected image">
          <div class="preview-overlay">
            <span class="image-number">#{{ index + 1 }}</span>
            <button class="remove-image" @click.stop="removeImage(index)" title="移除此图片">×</button>
            <button v-if="index === 0" class="edit-mask" @click.stop="openMaskEditor(index)" title="编辑遮罩">
              <i class="mask-icon"></i>
            </button>
          </div>
        </div>
        <div v-if="selectedImages.length < maxImages" class="preview-item add-more" @click.stop="triggerImageUpload">
          <div class="add-more-content">
            <i class="add-icon"></i>
            <span>添加更多</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <MaskEditor
    v-if="showMaskEditor"
    :image-url="selectedImagePreviews[0]"
    :existing-mask="existingMask"
    @save="handleMaskSave"
    @cancel="showMaskEditor = false"
  />
</template>

<script setup>
import { ref } from 'vue'
import MaskEditor from './MaskEditor.vue'

const props = defineProps({
  label: {
    type: String,
    default: '上传图片'
  },
  maxSize: {
    type: Number,
    default: 25 * 1024 * 1024 // 25MB
  },
  acceptedTypes: {
    type: Array,
    default: () => ['image/png', 'image/jpeg', 'image/webp']
  },
  maxImages: {
    type: Number,
    default: 6
  },
  existingMask: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['update:images', 'error', 'mask-created'])

const imageInput = ref(null)
const isDragging = ref(false)
const selectedImages = ref([])
const selectedImagePreviews = ref([])
const showMaskEditor = ref(false)
const selectedMask = ref(null)

const triggerImageUpload = () => {
  imageInput.value.click()
}

const handleDragEnter = () => {
  isDragging.value = true
}

const handleDragLeave = () => {
  isDragging.value = false
}

const handleImageSelect = (event) => {
  const files = event.target.files
  if (files) handleImageFiles(Array.from(files))
}

const handleImageFiles = (files) => {
  if (!files || files.length === 0) return

  // 检查是否超过最大图片数量限制
  if (selectedImages.value.length + files.length > props.maxImages) {
    emit('error', `最多只能上传 ${props.maxImages} 张图片，请删除一些图片后重试。`)
    return
  }

  const validFiles = files.filter(file => {
    if (!props.acceptedTypes.includes(file.type)) {
      emit('error', `请上传 ${props.acceptedTypes.map(type => type.split('/')[1].toUpperCase()).join('、')} 格式的图片。`)
      return false
    }

    if (file.size > props.maxSize) {
      emit('error', `单张图片大小不能超过 ${props.maxSize / (1024 * 1024)}MB，请压缩后重试。`)
      return false
    }
    return true
  })

  if (validFiles.length === 0) return

  // 追加新文件而不是覆盖
  selectedImages.value = [...selectedImages.value, ...validFiles]

  // 为新添加的文件创建预览
  validFiles.forEach(file => {
    const reader = new FileReader()
    reader.onload = e => {
      selectedImagePreviews.value.push(e.target.result)
    }
    reader.readAsDataURL(file)
  })

  // 向父组件发送更新后的图片数组
  emit('update:images', selectedImages.value)
}

const handleImageDrop = (event) => {
  isDragging.value = false
  const files = event.dataTransfer.files
  if (files) handleImageFiles(Array.from(files))
}

const removeImage = (index) => {
  selectedImages.value.splice(index, 1)
  selectedImagePreviews.value.splice(index, 1)
  emit('update:images', selectedImages.value)
}

const clearAllImages = () => {
  selectedImages.value = []
  selectedImagePreviews.value = []
  emit('update:images', [])
}

const openMaskEditor = (index) => {
  showMaskEditor.value = true
}

const handleMaskSave = (maskDataUrl) => {
  selectedMask.value = maskDataUrl
  showMaskEditor.value = false
  // 这里可以发出事件通知父组件遮罩已创建
  emit('mask-created', maskDataUrl)
}

// 暴露方法给父组件
defineExpose({
  clearAllImages,
  handleImageFiles
})
</script>

<style scoped>
.input-group {
  margin-bottom: 1.5rem;
}

.label-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
}

.image-count {
  font-size: 0.8rem;
  color: #666;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.clear-all-btn {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  padding: 0.2rem 0.5rem;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(147, 112, 219, 0.2);
  border-radius: 0.3rem;
  font-size: 0.8rem;
  color: #d63031;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-all-btn:hover {
  background: rgba(214, 48, 49, 0.1);
  border-color: rgba(214, 48, 49, 0.3);
}

.clear-icon {
  width: 14px;
  height: 14px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
}

.upload-area {
  position: relative;
  width: 100%;
  min-height: 90px;
  border: 2px dashed rgba(147, 112, 219, 0.3);
  border-radius: 0.7rem;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease, border-color 0.2s ease;
  overflow: hidden;
  height: 100%;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.upload-area:hover {
  border-color: rgba(147, 112, 219, 0.6);
  background: rgba(255, 255, 255, 0.7);
}

.upload-area.dragover {
  border-color: #9370db;
  background: rgba(255, 255, 255, 0.9);
  transform: scale(1.01);
}

.upload-area.has-images {
  background: rgba(255, 255, 255, 0.7);
  border-style: solid;
}

.file-input {
  display: none;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  height: 100%;
  justify-content: center;
  margin-top: 2rem;
}

.upload-icon {
  display: block;
  width: 32px;
  height: 32px;
  margin: 0 auto 0.5rem;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%239370db"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.6;
}

.upload-hint {
  font-size: 0.9rem;
  color: #999;
  margin-top: 0.5rem;
}

.image-preview-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.8rem;
  padding: 0.5rem;
  width: 100%;
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
  box-sizing: border-box;
}

.preview-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 2px solid rgba(147, 112, 219, 0.2);
  transition: all 0.3s ease;
}

.preview-item:hover {
  border-color: rgba(147, 112, 219, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(147, 112, 219, 0.1);
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 0.25rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.preview-item:hover .preview-overlay {
  opacity: 1;
}

.image-number {
  font-size: 0.7rem;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 0.1rem 0.3rem;
  border-radius: 0.3rem;
}

.remove-image {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(147, 112, 219, 0.3);
  color: #d63031;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1rem;
  line-height: 1;
  transition: all 0.2s ease;
  opacity: 0;
}

.preview-item:hover .remove-image {
  opacity: 1;
}

.remove-image:hover {
  background: #d63031;
  color: white;
  transform: scale(1.1);
}

.add-more {
  border: 2px dashed rgba(147, 112, 219, 0.3);
  background: rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-more:hover {
  border-color: rgba(147, 112, 219, 0.6);
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
}

.add-more-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
  color: #9370db;
}

.add-icon {
  width: 24px;
  height: 24px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%239370db"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.7;
}

.edit-mask {
  position: absolute;
  top: 0.25rem;
  right: 2rem;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(147, 112, 219, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0;
}

.preview-item:hover .edit-mask {
  opacity: 1;
}

.edit-mask:hover {
  background: rgba(147, 112, 219, 0.1);
  border-color: rgba(147, 112, 219, 0.6);
  transform: scale(1.1);
}

.mask-icon {
  width: 14px;
  height: 14px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%239370db" stroke-width="2"><path d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
}

@media (max-width: 640px) {
  .upload-area {
    min-height: 60px;
  }

  .image-preview-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
}
</style>