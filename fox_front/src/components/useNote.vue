<template>
    <div v-richText="content" v-if="content"></div>
    <el-empty :image="empty" v-if="!content"></el-empty>
</template>
<script setup>
import { ref } from 'vue'
import api from '@/axios'
import empty from '@/assets/node/empty.png'
const props = defineProps({
    content: String,
    config: {
        type: Object,
        required: true
    }
})
const content = ref('')
const getUseNote = async () => {
    const res = await api.get('/api/notification/getLatest?typeList=3')
    if (res.status !== 200) {
        ElMessage.error('获取使用说明失败')
        return
    }
    if (res.data.code == 0) {
        const arr = res.data.data
        if (arr.length) {

            //按照createdAt排序
            arr.sort((a, b) => {
                return new Date(b.createdAt) - new Date(a.createdAt)
            })
            content.value = arr[0].content
        }
    } else {
        content.value = '暂无使用说明'

    }
}
getUseNote()

</script>