<template>
  <div class="layout">
    <!-- Website Information Section (1/3 of the layout) -->
    <div class="website-info">
      <el-image :src="config.systemLogo || websiteLogoUrl" style="width: 40px; height: 40px;" class="website-logo" />
      <span class="website-name">{{ config.systemName }}</span>
    </div>

    <!-- User Information Section (2/3 of the layout) -->
    <div class="user-info">
      <el-avatar :src="avatarUrl" :size="64" class="user-avatar" />
      <h2 class="username">{{ user.userToken }}</h2>
      <p class="expiration-date">有效期 {{ $dateFormat(user.expireTime) }}</p>
      <p class="expiration-date">速率: {{ user.limit }}/{{ user.per }}</p>
      <el-button type="text" class="logout-button" @click="logout">退出登录</el-button>
      <el-button type="text" class="change-password-button" @click="showPasswordDialog">修改密码</el-button>
    </div>

    <!-- Password Change Dialog -->
    <el-dialog title="修改密码" v-model="passwordDialogVisible" width="400px" class="password-dialog">
      <div class="dialog-content">
        <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="80px">
          <el-form-item label="当前密码" prop="currentPassword">
            <el-input v-model="passwordForm.currentPassword" type="password" placeholder="请输入当前密码"></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input v-model="passwordForm.newPassword" type="password" placeholder="请输入新密码"></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input v-model="passwordForm.confirmPassword" type="password" placeholder="请确认新密码"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="passwordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitPasswordChange(passwordFormRef)">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, defineProps } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import useUserStore from '../store/user'
import api from '../axios'
import { el } from 'element-plus/es/locales.mjs'

defineProps({
  config: {
    type: Object,
    required: true,
  },

})

const userStore = useUserStore()
const user = ref(userStore.user)
const websiteLogoUrl = ref('/src/assets/chatgpt.svg')

const avatarUrl = ref('/src/assets/avatar.png')
const router = useRouter()
let passwordFormRef = ref(null)
// Logout function
const logout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    userStore.logout()
    ElMessage.info('已退出登录')
    router.push("/login")
  })
}

// Password change dialog setup
const passwordDialogVisible = ref(false)
const showPasswordDialog = () => {
  passwordDialogVisible.value = true
}

// Form data and validation rules
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
})

const passwordRules = {
  currentPassword: [{ required: true, message: '请输入当前密码', trigger: 'blur' },
  { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  newPassword: [{ required: true, message: '请输入新密码', trigger: 'blur' },
  { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else if (value === passwordForm.value.currentPassword) {
          callback(new Error('新密码不能与当前密码相同'))
        } else {
          callback()
        }
      }, trigger: 'blur'
    },
  ],
}

// Password change function
const submitPasswordChange = (passwordFormRef) => {
  passwordFormRef.validate(async (valid) => {
    if (valid) {
      console.log('Changing password...', user.value.userToken)

      const res = await api.post(`/api/chatGptUser/updatePassword`,
        { username: user.value.userToken, oldPassword: passwordForm.value.currentPassword, newPassword: passwordForm.value.newPassword })
      console.log(res)
      if (res.status !== 200 || res.data.code != 0) {
        ElMessage.error(res.data.msg)
        return
      }
      // Add password change logic here
      ElMessage.success('密码修改成功,请重新登录')
      userStore.logout()
      router.push("/login")
      passwordDialogVisible.value = false
    } else {
      ElMessage.error('请检查输入')
    }
  })
}

</script>

<style scoped>
.layout {
  display: flex;
  flex-direction: column;
  gap: 10px;
  /* padding: 10px; */
  padding-right: 10px;
  background-color: #f0f2f5;
}

.website-info {
  flex: 1;
  text-align: center;
  padding: 20px;
  border-radius: 15px;
  background-color: #ffffff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.website-logo {
  margin-bottom: 10px;
  border-radius: 50%;
}

.website-name {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
  /* margin-left: 20px; */
}

.user-info {
  flex: 2;
  text-align: center;
  padding: 20px;
  border-radius: 15px;
  background-color: #ffffff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  margin-bottom: 10px;
  border-radius: 50%;
}

.username {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.expiration-date {
  font-size: 14px;
  color: #606266;
  margin-bottom: 15px;
}

.logout-button,
.change-password-button {
  margin-top: 10px;
  border-radius: 8px;
  color: #409EFF;
}

.password-dialog .dialog-content {
  padding: 10px 0;
}

.password-dialog .el-dialog__body {
  padding: 20px 20px 10px 20px;
}
</style>
