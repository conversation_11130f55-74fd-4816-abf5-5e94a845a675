<template>

     <!-- 登录弹窗 -->
  <el-dialog v-model="showLoginDialog" title="登录提醒" width="30%">
    <span>您还未登录，请先登录以访问完整功能。</span>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showLoginDialog = false">取消</el-button>
        <el-button type="primary" @click="goToLogin">
          去登录
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>


export default {
    setup() {

        return {}
    },
    props: {
        showLoginDialog: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        goToLogin() {
            this.$router.push('/login')
        }
    }
};
</script>