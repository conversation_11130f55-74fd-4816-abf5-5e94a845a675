._root_1n8aj_45 {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    text-align: center;
    padding: var(--spacing-40) var(--padding-LG) 0 var(--padding-LG);
    margin: auto
}

._titleBlock_1n8aj_54 {
    margin-bottom: var(--padding-2XL)
}

._heading_1n8aj_58 {
    margin: 0
}

._subTitle_1n8aj_62 {
    margin-top: var(--padding-MD)
}

._fullHeight_1n8aj_66 {
    height: 100dvh
}

._footer_1n8aj_70 {
    margin-top: auto;
    margin-bottom: var(--padding-LG)
}

@media screen and (min-width: 450px) {
    ._root_1n8aj_45 {
        --max-inner-width: 21.25rem;
        padding-top: 15vh;
        max-width: calc(var(--max-inner-width) + 2 * var(--padding-LG));
        height: unset
    }

    ._wide_1n8aj_82 {
        --max-inner-width: 25rem
    }

    ._fullHeight_1n8aj_66 {
        height: auto
    }

    ._footer_1n8aj_70 {
        margin-top: var(--padding-XL)
    }
}

._root_gn36r_1 {
    font-size: var(--spacing-32);
    line-height: var(--spacing-40);
    font-weight: 500
}

:root {
    --padding-2XL: 2rem;
    --padding-XL: 1.5rem;
    --padding-LG: 1rem;
    --padding-MD: .75rem;
    --padding-SM: .5rem;
    --padding-XS: .25rem;
    --gap-2XL: 2rem;
    --gap-XL: 1.5rem;
    --gap-LG: 1rem;
    --gap-MD: .75rem;
    --gap-SM: .5rem;
    --gap-XS: .25rem;
    --spacing-2: .125rem;
    --spacing-4: .25rem;
    --spacing-6: .375rem;
    --spacing-8: .5rem;
    --spacing-10: .625rem;
    --spacing-12: .75rem;
    --spacing-14: .875rem;
    --spacing-16: 1rem;
    --spacing-18: 1.125rem;
    --spacing-20: 1.25rem;
    --spacing-24: 1.5rem;
    --spacing-28: 1.75rem;
    --spacing-32: 2rem;
    --spacing-36: 2.25rem;
    --spacing-40: 2.5rem;
    --spacing-44: 2.75rem;
    --spacing-48: 3rem;
    --spacing-56: 3.5rem;
    --spacing-64: 4rem;
    --spacing-72: 4.5rem;
    --spacing-80: 5rem;
    --spacing-96: 6rem;
    --spacing-112: 7rem;
    --spacing-128: 8rem;
    --spacing-144: 9rem;
    --radius-xsmall: .375rem;
    --radius-small: .5rem;
    --radius-medium: .75rem;
    --radius-circle: 99999px
}

:where(._xsmall_17l91_45) {
    font-size: .75rem;
    line-height: 1rem
}

:where(._small_17l91_50) {
    font-size: .875rem;
    line-height: 1.25rem
}

:where(._base_17l91_55) {
    font-size: 1rem;
    line-height: 1.5rem
}

:where(._large_17l91_60) {
    font-size: 1.125rem;
    line-height: 1.75rem
}

:where(._xlarge_17l91_65) {
    font-size: 1.25rem;
    line-height: 1.75rem
}

:where(._extralight_17l91_70) {
    font-weight: 200
}

:where(._light_17l91_74) {
    font-weight: 300
}

:where(._normal_17l91_78) {
    font-weight: 400
}

:where(._medium_17l91_82) {
    font-weight: 500
}

:where(._semibold_17l91_86) {
    font-weight: 600
}

:where(._bold_17l91_90) {
    font-weight: 700
}

:where(._primary_17l91_94) {
    color: var(--text-primary)
}

:where(._primary_17l91_94) :where(a) {
    color: var(--accent-blue);
    text-decoration: none
}

:where(:where(._primary_17l91_94) :where(a):hover) {
    text-decoration: underline
}

:where(._secondary_17l91_105) {
    color: var(--text-secondary)
}

:where(._tertiary_17l91_109) {
    color: var(--text-tertiary)
}

:where(._quaternary_17l91_113) {
    color: var(--text-quaternary)
}

:where(._secondary_17l91_105,._tertiary_17l91_109,._quaternary_17l91_113) :where(a) {
    color: inherit
}
