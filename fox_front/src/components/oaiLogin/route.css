@charset "UTF-8";:root {
    font-family: <PERSON><PERSON><PERSON><PERSON>,-apple-system,BlinkMacSystemFont,Helvetica,sans-serif;
    line-height: 1.5;
    font-weight: 400;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.page-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 100%;
    overflow: hidden;
}

.oai-header {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    background-color: #fff
}

.oai-header img {
    display: flex;
    height: 32px;
    width: 32px;
    background-color: #fff;
    margin: 32px 0 0;
    fill: #202123
}

body {
    background-color: #fff;
    display: block;
    margin: 0
}

a {
    font-weight: 400;
    text-decoration: inherit;
    color: #10a37f
}

.main-container {
    flex: 1 0 auto;
    min-height: 0;
    display: grid;
    box-sizing: border-box;
    grid-template-rows: [left-start center-start right-start] 1fr [left-end center-end right-end];
    grid-template-columns: [left-start center-start] 1fr [left-end right-start] 1fr [center-end right-end];
    align-items: center;
    justify-content: center;
    justify-items: center;
    grid-column-gap: 160px;
    column-gap: 160px;
    padding: 80px;
    width: 100%;
    padding-top: 30px;
    padding-bottom: 20px;
}

@media (max-width: 480px) {
    .main-container {
        padding:0
    }
}

.content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: auto;
    white-space: normal;
    border-radius: 5px;
    position: relative;
    grid-area: center;
    box-shadow: none;
    vertical-align: baseline;
    box-sizing: content-box
}

.login-container {
    background-color: #fff;
    padding: 0 40px 40px;
    border-radius: 3px;
    box-shadow: none;
    width: 320px;
    box-sizing: content-box;
    flex-shrink: 0
}

.title-wrapper {
    padding: 40px 40px 24px;
    box-sizing: content-box
}

.title {
    font-size: 32px;
    font: "Söhne";
    margin: 24px 0 0;
    color: #2d333a;
    width: 320px;
    text-align: center
}

.subtitle {
    font-size: 24px;
    font: "Söhne";
    margin: 24px 0 0;
    color: #2d333a;
    text-align: center
}

.sso-list {
    margin-top: 16px
}

.input-wrapper {
    position: relative;
    margin-bottom: 25px;
    width: 320px;
    box-sizing: content-box
}

.email-input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border: 1px solid #c2c8d0;
    border-radius: 6px;
    box-sizing: border-box;
    color: #2d333a;
    font-family: inherit;
    font-size: 16px;
    height: 52px;
    line-height: 1.1;
    outline: none;
    padding-block:1px;padding-inline:2px;padding: 0 16px;
    transition: box-shadow .2s ease-in-out,border-color .2s ease-in-out;
    width: 100%;
    text-rendering: auto;
    letter-spacing: normal;
    word-spacing: normal;
    text-transform: none;
    text-indent: 0;
    text-shadow: none;
    display: inline-block;
    text-align: start;
    margin: 0
}

.phone-input .email-input {
    padding-left: 94px
}

.email-input:focus,.email-input:valid {
    border: 1px solid #10a37f;
    outline: none
}

.email-input:focus-within {
    box-shadow: 1px #10a37f
}

.email-input:focus+.email-label {
    font-size: 14px;
    top: 0;
    left: 10px;
    color: #10a37f;
    background-color: #fff
}

.email-input.error {
    border: 1px solid #d00e17
}

.email-input.error+.email-label {
    color: #d00e17
}

.email-label.filled {
    font-size: 14px;
    top: 0;
    left: 10px
}

.email-label {
    position: absolute;
    top: 26px;
    left: 16px;
    background-color: #fff;
    color: #6f7780;
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 8px;
    max-width: 90%;
    overflow: hidden;
    pointer-events: none;
    padding: 1px 6px;
    text-overflow: ellipsis;
    transform: translateY(-50%);
    transform-origin: 0;
    transition: transform .15s ease-in-out,top .15s ease-in-out,padding .15s ease-in-out;
    white-space: nowrap;
    z-index: 1
}

.invalid-email-error-message {
    color: #d00e17;
    display: none;
    margin: 4px 0 12px;
    font-size: 12px
}

.invalid-email-error-message.visible {
    display: flex
}

.continue-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 52px;
    width: 320px;
    background-color: #10a37f;
    color: #fff;
    margin: 24px 0 0;
    border-radius: 6px;
    padding: 4px 16px;
    font: inherit;
    border-width: 0;
    cursor: pointer
}

.continue-btn:hover {
    box-shadow: inset 0 0 0 150px #0000001a
}

.otp-link-btn {
    width: 100%;
    color: #10a37f;
    text-align: center;
    font-size: 14px;
    font-style: normal;
    align-self: stretch;
    margin-top: 10px;
    margin-bottom: 0;
    background: none;
    border: none;
    cursor: pointer;
    justify-content: center
}

.other-page {
    text-align: center;
    margin-top: 16px;
    margin-bottom: 0;
    font-size: 14px;
    width: 320px
}

.other-page-link {
    padding: 4px
}

.divider-wrapper {
    display: flex;
    flex-direction: row;
    text-transform: uppercase;
    border: none;
    font-size: 12px;
    font-weight: 400;
    margin: 0;
    padding: 24px 0 0;
    align-items: center;
    justify-content: center;
    width: 320px;
    vertical-align: baseline
}

.divider-wrapper:before,.divider-wrapper:after {
    content: "";
    border-bottom: 1px solid #c2c8d0;
    flex: 1 0 auto;
    height: .5em;
    margin: 0
}

.divider {
    text-align: center;
    flex: .2 0 auto;
    margin: 0;
    height: 12px
}

.social-section {
    margin-top: 24px
}

.social-btn {
    position: relative;
    width: 320px;
    border: 1px solid #c2c8d0;
    border-radius: 6px;
    font-size: 16px;
    align-items: center;
    background-color: #fff;
    height: 52px;
    transition: box-shadow .15 ease-in-out,background-color .15s ease-in-out;
    cursor: pointer;
    color: #2d333a;
    margin-bottom: 8px;
    display: flex;
    outline: 0
}

.social-btn {
    padding: 0 8px 0 52px
}

.social-btn:hover {
    box-shadow: inset 0 0 0 150px #0000001a
}

.social-btn:focus {
    outline: none
}

.social-logo-wrapper {
    position: absolute;
    left: 26px;
    top: 50%;
    transform: translate(-50%) translateY(-50%)
}

.social-logo {
    width: 20px;
    height: 20px;
    display: inline-block
}

.error-icon {
    display: inline-block;
    height: 16px;
    width: 16px;
    margin-right: 8px;
    flex-shrink: 0;
    background-position: 0 0;
    background-repeat: no-repeat;
    background-size: 16px
}

.social-text {
    text-align: left;
    position: relative
}

.oai-footer {
    text-align: center;
    padding: 12px 0 24px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6e6e80;
    flex: 0 0 auto;
    padding-bottom: 5px;
}

.separator:before {
    content: " | ";
    margin: 0 8px
}

.phone-number-country-code-selector {
    height: 50px;
    position: absolute;
    color: #2d333a;
    border: none;
    top: 1px;
    left: 0;
    background: none;
    width: 86px;
    box-sizing: border-box;
    font-size: 16px;
    padding: 0 0 0 16px;
    text-align: end;
    -webkit-user-select: none;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: start;
    cursor: pointer
}

.country-options-list {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 100%;
    background: #fff;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1;
    border-radius: 6px;
    box-shadow: 0 8px 32px #0003
}

.country-option-item {
    padding: 8px 16px;
    white-space: nowrap;
    text-align: start;
    display: flex;
    cursor: pointer;
    font-size: 14px
}

.country-option-item:hover {
    background-color: #f0f0f0
}

.country-option-item-country-emoji {
    margin-right: 8px
}

.country-option-item-country-code {
    width: 60px
}

.country-option-item-country-name {
    white-space: nowrap;
    color: #0d0d0d;
    margin-right: 4px
}

.country-selected {
    display: flex;
    align-items: center
}

.country-selected-chevron {
    position: absolute;
    right: 0;
    width: 12px;
    height: 12px;
    color: #b9b9b9
}

.country-selected-value {
    flex: 1;
    white-space: nowrap
}

._loginChallengePage_p12g4_2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100dvh;
    background-color: var(--main-surface-primary);
    font-family: Arial,sans-serif;
    text-align: center
}

._loginChallengePage_p12g4_2 h1 {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--text-primary)
}

._loginChallengePage_p12g4_2 p {
    font-size: 16px;
    margin-bottom: 20px;
    color: var(--text-secondary)
}

._loginChallengePage_p12g4_2 form {
    display: flex;
    flex-direction: column;
    align-items: center
}

._codeInput_p12g4_28 {
    width: 300px;
    padding: 10px;
    font-size: 16px;
    border-radius: 5px;
    border: 1px solid var(--border-medium);
    margin-bottom: 20px;
    transition: border-color .3s ease
}

._codeInput_p12g4_28:focus {
    outline: none;
    border-color: var(--text-primary)
}

._continueButton_p12g4_42 {
    width: 300px;
    padding: 10px;
    font-size: 16px;
    color: var(--white);
    background-color: var(--text-primary);
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-bottom: 20px;
    transition: background-color .3s ease
}

._continueButton_p12g4_42:hover {
    background-color: var(--gray-900)
}

._continueButton_p12g4_42:active {
    background-color: var(--gray-700);
    transform: scale(.98)
}

._resendButton_p12g4_62 {
    font-size: 14px;
    color: var(--text-primary);
    background: none;
    border: none;
    cursor: pointer;
    text-decoration: underline;
    transition: color .3s ease
}

._resendButton_p12g4_62:hover {
    color: var(--text-tertiary)
}

._resendButton_p12g4_62:active {
    color: var(--gray-700);
    transform: scale(.98)
}

._errorMessage_p12g4_79 {
    color: var(--error-color);
    font-size: 14px;
    margin-bottom: 20px;
    display: flex;
    align-items: center
}

._errorMessage_p12g4_79:before {
    content: "⚠️";
    margin-right: 8px;
    font-size: 16px
}

._wrapper_wiadn_1 {
    max-width: 400px;
    margin: 0 auto;
    padding-top: 50px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 100dvh;
    text-align: center
}

._footer_wiadn_12 {
    text-align: center;
    color: #6e6e80;
    padding: 12px 0 24px;
    flex: 0 0 auto
}

._footer_wiadn_12 a {
    color: #10a37f;
    margin: 0 10px
}

._main_wiadn_23 {
    display: flex;
    flex-direction: column;
    align-items: center
}

._troubleLink_wiadn_29 {
    margin-top: 20px;
    color: #10a37f;
    text-decoration: underline;
    cursor: pointer
}

._separator_wiadn_36:after {
    content: "|"
}

._wrapper_15l2h_1 {
    max-width: 400px;
    margin: 0 auto;
    padding-top: 50px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 100dvh;
    text-align: center
}

._separator_15l2h_12:after {
    content: "|"
}

._main_15l2h_16 {
    display: flex;
    flex-direction: column;
    align-items: center
}

._title_15l2h_22 {
    font-weight: 700;
    font-size: 24px;
    margin-bottom: 20px
}

._instructions_15l2h_28 {
    font-size: 16px;
    margin-bottom: 20px;
    max-width: 300px;
    color: #202123
}

._codeBox_15l2h_35 {
    font-family: monospace;
    font-size: 18px;
    padding: 10px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 6px;
    margin-bottom: 20px;
    width: 100%;
    max-width: 300px;
    text-align: center;
    word-wrap: break-word
}

._input_15l2h_49 {
    width: 100%;
    max-width: 300px;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #ddd;
    box-shadow: 0 1px 3px #0000001a;
    font-size: 16px
}

._copyButton_15l2h_59 {
    background-color: #fff;
    color: #202123;
    padding: 10px 20px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    margin-bottom: 20px;
    width: 100%;
    max-width: 300px
}

._copyButton_15l2h_59:hover {
    background-color: #f5f5f5;
    border: 1px solid #ddd
}

._checkboxWrapper_15l2h_76 {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    justify-content: flex-start
}

._checkboxWrapper_15l2h_76 ._checkbox_15l2h_76 {
    margin-right: 10px
}

._checkboxWrapper_15l2h_76 ._checkboxLabel_15l2h_85 {
    font-size: 14px;
    color: #202123
}

._continueButtonWithChangeOnHover_15l2h_90 {
    background-color: #10a37f;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    width: 100%;
    max-width: 300px
}

._continueButtonWithChangeOnHover_15l2h_90:disabled {
    background-color: #ccc;
    cursor: not-allowed
}

._continueButtonWithChangeOnHover_15l2h_90._loading_15l2h_105 {
    background-color: #ccc;
    cursor: progress
}

._continueButtonWithChangeOnHover_15l2h_90._error_15l2h_109 {
    border: 2px solid #d9534f
}

._errorMessage_15l2h_113 {
    color: #a94442;
    margin-top: 10px;
    font-size: 14px;
    text-align: center
}

._continueButton_15l2h_90 {
    background-color: #10a37f;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    width: 100%;
    max-width: 300px;
    margin-top: 20px
}

._footer_15l2h_133 {
    text-align: center;
    color: #6e6e80;
    padding: 12px 0 24px;
    flex: 0 0 auto
}

._footer_15l2h_133 a {
    color: #10a37f;
    margin: 0 10px
}

._dividerWithText_187so_1 {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 0;
    width: 100%
}

._dividerLine_187so_9 {
    flex: 1;
    height: 1px;
    background-color: #ddd
}

._dividerText_187so_15 {
    margin: 0 10px;
    font-size: 14px;
    color: #6e6e80
}

._inputWrapper_kuufs_2 {
    margin-bottom: 20px
}

._label_kuufs_6 {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #202123
}

._input_kuufs_2 {
    width: 100%;
    max-width: 300px;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #ddd;
    box-shadow: 0 1px 3px #0000001a;
    font-size: 16px;
    transition: border-color .3s ease
}

._inputError_kuufs_24 {
    border-color: red
}

._button_kuufs_28 {
    background-color: #10a37f;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    width: 100%;
    max-width: 300px;
    margin-top: 20px
}

._error_kuufs_41 {
    color: red;
    font-size: 14px;
    margin-top: 10px;
    display: flex;
    align-items: center
}

._error_kuufs_41:before {
    content: "⚠️";
    margin-right: 8px;
    font-size: 16px
}

._header_zf2fb_1 {
    padding: 32px 0 0;
    flex: 0 0 auto
}

._logo_zf2fb_6 {
    margin: 0 auto;
    display: block
}

._title_zf2fb_11 {
    font-weight: 700;
    font-size: 32px;
    margin: 20px 0
}

._wrapper_fxk0j_1 {
    max-width: 400px;
    margin: 0 auto;
    padding-top: 50px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 100dvh;
    text-align: center
}

._footer_fxk0j_12 {
    text-align: center;
    color: #6e6e80;
    padding: 12px 0 24px;
    flex: 0 0 auto
}

._footer_fxk0j_12 a {
    color: #10a37f;
    margin: 0 10px
}

._main_fxk0j_23 {
    display: flex;
    flex-direction: column;
    align-items: center
}

._separator_fxk0j_29:after {
    content: "|"
}
