:root {
    --white: #fff;
    --black: #000;
    --gray-50: #f9f9f9;
    --gray-100: #ececec;
    --gray-200: #e3e3e3;
    --gray-300: #cdcdcd;
    --gray-400: #b4b4b4;
    --gray-500: #9b9b9b;
    --gray-600: #676767;
    --gray-700: #424242;
    --gray-750: #2f2f2f;
    --gray-800: #212121;
    --gray-900: #171717;
    --gray-950: #0d0d0d;
    --red-500: #ef4444;
    --red-700: #b91c1c;
    --brand-purple: #ab68ff;
    --openai-dot-com-background: #f9f9f9;
    --platform-green: #10a37f;
    --platform-error: #d00e17;
    --accent-blue: #3e68ff;
    --scrim: rgba(0, 0, 0, .4);
    --dialog-shadow: 0px 20px 25px -5px rgba(0, 0, 0, .1), 0px 8px 10px -6px rgba(0, 0, 0, .1);
    --popover-shadow: 0px 8px 32px 0px rgba(0, 0, 0, .2);
    --menu-primary-surface-item-hover: rgba(0, 0, 0, .05);
    --menu-primary-surface-item-press: rgba(0, 0, 0, .1);
    --icon-default-tertiary: #a4a4a4;
    --text-primary: var(--gray-950);
    --text-secondary: #5d5d5d;
    --text-tertiary: var(--gray-400);
    --text-quaternary: var(--gray-300);
    --text-selection: var(--white);
    --text-error: #f93a37;
    --surface-error: 249 58 55;
    --border-xlight: rgb(0 0 0 / 5%);
    --border-light: rgb(0 0 0 / 10%);
    --border-medium: rgb(0 0 0 / 15%);
    --border-heavy: rgb(0 0 0 / 20%);
    --border-xheavy: rgb(0 0 0 / 25%);
    --border-sharp: rgb(0 0 0 / 5%);
    --border-white-10: rgb(255 255 255 / 10%);
    --main-surface-primary: var(--white);
    --main-surface-secondary: var(--gray-50);
    --main-surface-tertiary: var(--gray-100);
    --sidebar-surface-primary: var(--gray-50);
    --sidebar-surface-secondary: var(--gray-100);
    --sidebar-surface-tertiary: var(--gray-200);
    --link: #2964aa;
    --link-hover: #749ac8;
    --selection-blue: #007aff;
    --main-page-background: var(--main-surface-primary);
    --selection: var(--selection-blue)
}

:root {
    --padding-2XL: 2rem;
    --padding-XL: 1.5rem;
    --padding-LG: 1rem;
    --padding-MD: .75rem;
    --padding-SM: .5rem;
    --padding-XS: .25rem;
    --gap-2XL: 2rem;
    --gap-XL: 1.5rem;
    --gap-LG: 1rem;
    --gap-MD: .75rem;
    --gap-SM: .5rem;
    --gap-XS: .25rem;
    --spacing-2: .125rem;
    --spacing-4: .25rem;
    --spacing-6: .375rem;
    --spacing-8: .5rem;
    --spacing-10: .625rem;
    --spacing-12: .75rem;
    --spacing-14: .875rem;
    --spacing-16: 1rem;
    --spacing-18: 1.125rem;
    --spacing-20: 1.25rem;
    --spacing-24: 1.5rem;
    --spacing-28: 1.75rem;
    --spacing-32: 2rem;
    --spacing-36: 2.25rem;
    --spacing-40: 2.5rem;
    --spacing-44: 2.75rem;
    --spacing-48: 3rem;
    --spacing-56: 3.5rem;
    --spacing-64: 4rem;
    --spacing-72: 4.5rem;
    --spacing-80: 5rem;
    --spacing-96: 6rem;
    --spacing-112: 7rem;
    --spacing-128: 8rem;
    --spacing-144: 9rem;
    --radius-xsmall: .375rem;
    --radius-small: .5rem;
    --radius-medium: .75rem;
    --radius-circle: 99999px
}

* {
    box-sizing: border-box;
    text-wrap: pretty;
    -webkit-tap-highlight-color: transparent;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-smooth: always
}

html {
    font-family: SF Pro,-apple-system,BlinkMacSystemFont,Helvetica,sans-serif
}

body {
    margin: 0;
    background-color: var(--main-page-background)
}

._brandingChatGPT_17lt2_1 {
    --main-page-background: var(--openai-dot-com-background);
    --selection: var(--selection-blue)
}

._brandingPlatform_17lt2_6 {
    --main-page-background: var(--main-surface-primary);
    --selection: var(--platform-green)
}
