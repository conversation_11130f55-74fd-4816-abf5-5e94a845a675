<template>

    <body class="_brandingChatGPT_17lt2_1" style="background-color: rgb(255, 255, 255);">
        <div class="page-wrapper">
            <header class="oai-header">
                <chatGptLogo />
            </header>
            <main class="main-container">
                <section class="content-wrapper">
                    <div class="title-wrapper">
                        <h1 class="title">重置密码</h1>
                    </div>
                    <div class="login-container">
                        <!-- 第一步：邮箱验证 -->
                        <div>
                            <div class="input-wrapper">
                                <input class="email-input" v-model="resetForm.email" inputmode="email" id="email-input"
                                    name="email" placeholder="" :disabled="isEmailSent" />
                                <label :class="['email-label', { filled: resetForm.email }]"
                                    for="email-input">电子邮件地址*</label>
                            </div>

                            <button v-if="!isEmailSent" class="continue-btn" @click="getEmailCode"
                                :disabled="isEmailSent">
                                {{ isEmailSent ? '已发送验证邮件' : '继续' }}
                            </button>
                        </div>

                        <!-- 第二步：重置密码表单 -->
                        <div v-if="isEmailSent">
                            <div class="input-wrapper verify-code-wrapper">
                                <input class="email-input" v-model="resetForm.captchaCode" id="verify-code-input"
                                    name="verifyCode" placeholder="" maxlength="6" />
                                <label :class="['email-label', { filled: resetForm.captchaCode }]"
                                    for="verify-code-input">验证码*</label>
                                <span class="resend-code" @click="handleResendCode"
                                    :class="{ disabled: countdown > 0 }">
                                    {{ countdown > 0 ? `${countdown}秒后重新发送` : '重新发送' }}
                                </span>
                            </div>

                            <div class="input-wrapper">
                                <input class="email-input" v-model="resetForm.newPassword" id="password-input"
                                    name="password" placeholder="" :type="passwordVisible ? 'text' : 'password'" />
                                <label :class="['email-label', { filled: resetForm.newPassword }]"
                                    for="password-input">新密码*</label>
                                    <span class="eye-icon" @click="handlePasswordVisible">
                                        <eye v-show="passwordVisible" />
                                        <eyeclose v-show="!passwordVisible" />
                                    </span>
                            </div>

                            <div class="input-wrapper">
                                <input class="email-input" v-model="resetForm.confirmPassword"
                                    id="confirm-password-input" name="confirmPassword" placeholder=""
                                    :type="confirmPasswordVisible ? 'text' : 'password'" />
                                <label :class="['email-label', { filled: resetForm.confirmPassword }]"
                                    for="confirm-password-input">确认新密码*</label>
                                <span class="eye-icon" @click="handleConfirmPasswordVisible">
                                    <svg v-show="confirmPasswordVisible" xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24">
                                        <g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round">
                                            <circle cx="12" cy="12" r="2"></circle>
                                            <path
                                                d="M22 12c-2.667 4.667-6 7-10 7s-7.333-2.333-10-7c2.667-4.667 6-7 10-7s7.333 2.333 10 7">
                                            </path>
                                        </g>
                                    </svg>
                                    <svg v-show="!confirmPasswordVisible" xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24">
                                        <g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round">
                                            <path d="M3 3l18 18"></path>
                                            <path d="M10.584 10.587a2 2 0 0 0 2.828 2.83"></path>
                                            <path
                                                d="M9.363 5.365A9.466 9.466 0 0 1 12 5c4 0 7.333 2.333 10 7c-.778 1.361-1.612 2.524-2.503 3.488m-2.14 1.861C15.726 18.449 13.942 19 12 19c-4 0-7.333-2.333-10-7c1.369-2.395 2.913-4.175 4.632-5.341">
                                            </path>
                                        </g>
                                    </svg>
                                </span>
                            </div>

                            <button class="continue-btn" @click="handleResetPassword">重置密码</button>
                        </div>

                        <p class="other-page">想起密码了？<a class="other-page-link" @click="router.push('/login')">登录</a></p>
                    </div>
                </section>
            </main>
            <footer class="oai-footer">
                <a href="">使用条款</a><span class="separator"></span><a href="">隐私政策</a>
            </footer>
        </div>
    </body>
</template>

<script setup>
import { ref, reactive } from 'vue'
import chatGptLogo from '@/assets/chatgpt.svg'
import { useRouter } from 'vue-router'
import useUserStore from '@/store/user'
import { ElMessage } from 'element-plus'
import api from '@/axios'
import eye from '@/assets/eye.svg'
import eyeclose from '@/assets/eyeclose.svg'
const userStore = useUserStore()
const router = useRouter()

// 邮箱验证相关
const isEmailSent = ref(false)
const countdown = ref(0)

// 重置密码表单
const resetForm = reactive({
    email: '',
    captchaCode: '',
    newPassword: '',
    confirmPassword: ''
})

// 密码可见性控制
const passwordVisible = ref(false)
const confirmPasswordVisible = ref(false)

const handlePasswordVisible = () => {
    passwordVisible.value = !passwordVisible.value
}

const handleConfirmPasswordVisible = () => {
    confirmPasswordVisible.value = !confirmPasswordVisible.value
}

// 发送验证邮件
const getEmailCode = async () => {
    if (!resetForm.email) {
        ElMessage.warning('请先输入邮箱地址')
        return
    }

    if (!/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(resetForm.email)) {
        ElMessage.warning('请输入正确的邮箱地址')
        return
    }

    try {
        const res = await api.get('/api/chatGptUser/sendEmailCode?email=' + resetForm.email)
        if (res.data.code !== 0) {
            ElMessage.error('发送验证码失败')
            return
        }
        ElMessage.success('验证码已发送，请查收')
        isEmailSent.value = true
        startCountdown()
    } catch (error) {
        ElMessage.error('发送验证码失败')
    }
}

// 倒计时函数
const startCountdown = () => {
    countdown.value = 60
    const timer = setInterval(() => {
        if (countdown.value > 0) {
            countdown.value--
        } else {
            clearInterval(timer)
        }
    }, 1000)
}

// 重新发送验证码
const handleResendCode = () => {
    if (countdown.value === 0) {
        getEmailCode()
    }
}

// 重置密码
const handleResetPassword = async () => {
    if (!resetForm.email) {
        ElMessage.error('请输入邮箱')
        return
    }
    if (!resetForm.captchaCode) {
        ElMessage.error('请输入验证码')
        return
    }
    if (!resetForm.newPassword) {
        ElMessage.error('请输入新密码')
        return
    }
    if (!resetForm.confirmPassword) {
        ElMessage.error('请确认新密码')
        return
    }
    if (resetForm.newPassword !== resetForm.confirmPassword) {
        ElMessage.error('两次输入的密码不一致')
        return
    }

    try {
        const res = await api.post('/api/chatGptUser/forgetPassword', {
            email: resetForm.email,
            captchaCode: resetForm.captchaCode,
            password: resetForm.newPassword
        })

        if (res.data.code === 0) {
            ElMessage.success('密码重置成功')
            router.push('/login')
        } else {
            ElMessage.error(res.data.msg)
        }
    } catch (error) {
        ElMessage.error('密码重置失败')
    }
}
</script>

<style scoped>
@import url(authStepLayout.css);
@import url(button.css);
@import url(root.css);
@import url(route.css);

.eye-icon {
    position: absolute;
    right: 0.5rem;
    top: 10px;
    width: 30px;
    height: 30px;
}

.verify-code-wrapper {
    position: relative;
}

.resend-code {
    position: absolute;
    right: 0.5rem;
    top: 10px;
    color: #10a37f;
    cursor: pointer;
    font-size: 14px;
}

.resend-code.disabled {
    color: #ccc;
    cursor: not-allowed;
}
</style>