._root_1jk9q_5 {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--padding-MD) var(--padding-XL);
    border: none;
    border-radius: var(--button-border-radius);
    cursor: pointer;
    transition: background-color .1s ease;
    font-size: 1rem;
    line-height: 1.5rem;
    text-align: center;
    font-family: inherit;
    font-weight: 400;
    text-decoration: none;
    color: var(--text-primary)
}

._root_1jk9q_5:active {
    opacity: .8
}

._root_1jk9q_5:focus-visible {
    outline: 0;
    box-shadow: #fff 0 0 0 2px,var(--focus-ring-color) 0 0 0 4px,#0000 0 0
}

._root_1jk9q_5:disabled {
    cursor: not-allowed;
    opacity: .5
}

._leftAlign_1jk9q_35 {
    justify-content: start
}

._primary_1jk9q_39 {
    background-color: var(--primary);
    color: var(--text-on-primary)
}

._primary_1jk9q_39:hover {
    background-color: var(--primary-hover)
}

._outline_1jk9q_47 {
    background-color: transparent;
    color: var(--text-on-base);
    border: 1px solid var(--border-medium)
}

._outline_1jk9q_47:hover {
    background-color: var(--outline-hover)
}

._transparent_1jk9q_56 {
    background-color: transparent;
    color: var(--text-on-base);
    height: auto;
    padding: 0;
    border-radius: 0;
    width: fit-content
}

._chatGPT_1jk9q_65 {
    --primary: #131313;
    --primary-hover: #333333;
    --outline-hover: #ececec;
    --text-on-primary: #ffffff;
    --focus-ring-color: rgb(155, 155, 155);
    --button-border-radius: var(--radius-circle)
}

._platfrom_1jk9q_74 {
    --primary: var(--platform-green);
    --primary-hover: #139373;
    --outline-hover: #dddddd;
    --text-on-primary: #ffffff;
    --focus-ring-color: rgb(100, 100, 100);
    --button-border-radius: var(--radius-xsmall)
}

._decoration_1jk9q_83 {
    margin-right: var(--padding-SM);
    display: flex;
    align-items: center
}

._leftAlign_1jk9q_35 ._decoration_1jk9q_83 {
    margin-right: var(--padding-LG)
}
