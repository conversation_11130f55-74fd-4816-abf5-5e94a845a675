<template>

    <body class="_brandingChatGPT_17lt2_1" style="background-color: rgb(255, 255, 255);">
        <div class="page-wrapper">
            <header class="oai-header">
                <chatGptLogo />
            </header>
            <main class="main-container">
                <section class="content-wrapper">
                    <div class="title-wrapper">
                        <h1 class="title">{{ $t('register.createAccount') }}</h1>
                    </div>
                    <div class="login-container">
                        <!-- 第一步：邮箱验证 -->
                        <div>
                            <div class="input-wrapper">
                                <input class="email-input" v-model="registerForm.email" inputmode="email"
                                    id="email-input" name="email" placeholder="" :disabled="isEmailSent" />
                                <label :class="['email-label', { filled: registerForm.email }]"
                                    for="email-input">{{ $t('register.email') }}</label>
                            </div>

                            <button v-if="!isEmailSent" class="continue-btn" @click="getEmailCode"
                                :disabled="isEmailSent">
                                {{ isEmailSent ? $t('register.emailSent') : $t('register.continue') }}
                            </button>

                            <!-- 验证码输入框，仅在发送邮件后显示 -->

                        </div>

                        <!-- 第二步：完整注册表单 -->
                        <div v-if="isEmailSent">
                            <div class="input-wrapper verify-code-wrapper">
                                <input class="email-input" v-model="registerForm.captchaCode" id="verify-code-input"
                                    name="verifyCode" placeholder="" maxlength="6" />
                                <label :class="['email-label', { filled: registerForm.captchaCode }]"
                                    for="verify-code-input">{{ $t('register.verificationCode') }}</label>
                                <span class="resend-code" @click="handleResendCode"
                                    :class="{ disabled: countdown > 0 }">
                                    {{ countdown > 0 ? $t('register.resendCountdown', { seconds: countdown }) : $t('register.resend') }}
                                </span>
                            </div>
                            <div class="input-wrapper">
                                <input class="email-input" v-model="registerForm.username" id="username-input"
                                    name="username" placeholder="" @input="validateUsername" />
                                <label :class="['email-label', { filled: registerForm.username }]"
                                    for="username-input">{{ $t('register.username') }}</label>
                            </div>

                            <div class="input-wrapper">
                                <input class="email-input" v-model="registerForm.password" id="password-input"
                                    name="password" placeholder="" :type="passwordVisible ? 'text' : 'password'" />
                                <label :class="['email-label', { filled: registerForm.password }]"
                                    for="password-input">{{ $t('register.password') }}</label>
                                    <span class="eye-icon" @click="handlePasswordVisible">
                                        <eye v-show="passwordVisible" />
                                        <eyeclose v-show="!passwordVisible" />
                                    </span>
                            </div>

                            <div class="input-wrapper">
                                <input class="email-input" v-model="registerForm.inviteCode" id="invite-input"
                                    name="invite" placeholder="" />
                                <label :class="['email-label', { filled: registerForm.inviteCode }]"
                                    for="invite-input">{{ $t('register.inviteCode') }}</label>
                            </div>

                            <button class="continue-btn" @click="handleRegister">{{ $t('register.registerButton') }}</button>
                        </div>

                        <p class="other-page">{{ $t('register.hasAccount') }}<a class="other-page-link" @click="router.push('/login')">{{ $t('register.login') }}</a></p>
                    </div>
                </section>
            </main>
            <footer class="oai-footer">
                <a href="">{{ $t('register.termsOfService') }}</a><span class="separator"></span><a href="">{{ $t('register.privacyPolicy') }}</a>
            </footer>
        </div>
    </body>
</template>

<script setup>
import { ref, reactive } from 'vue'
import chatGptLogo from '@/assets/chatgpt.svg'
import { useRouter } from 'vue-router'
import useUserStore from '@/store/user'
import { ElMessage } from 'element-plus'
import api from '@/axios'
import { useI18n } from 'vue-i18n'
import eye from '@/assets/eye.svg'
import eyeclose from '@/assets/eyeclose.svg'
// 邮箱验证相关

const { t } = useI18n()
const userStore = useUserStore()
const router = useRouter()

const isEmailSent = ref(false)
const countdown = ref(0)

// 注册表单
const registerForm = reactive({
    username: '',
    password: '',
    inviteCode: '',
    email: '',
    captchaCode: ''
})

// 发送验证邮件
const getEmailCode = async () => {
    if (!registerForm.email) {
        ElMessage.warning(t('register.errors.enterEmail'))
        return
    }

    if (!/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(registerForm.email)) {
        ElMessage.warning(t('register.errors.invalidEmail'))
        return
    }


    await api.get('/api/chatGptUser/sendEmailCode?email=' + registerForm.email)
    // 开始倒计时
    countdown.value = t('register.emailSent')
    ElMessage.success(t('register.errors.verificationSent'))
    isEmailSent.value = true
    startCountdown()
    // 这里添加发送邮箱验证码的逻辑
    console.log('发送验证码到邮箱：', registerForm.email)
}

// 倒计时函数
const startCountdown = () => {
    countdown.value = 60
    const timer = setInterval(() => {
        if (countdown.value > 0) {
            countdown.value--
        } else {
            clearInterval(timer)
        }
    }, 1000)
}

// 重新发送验证码
const handleResendCode = () => {
    if (countdown.value === 0) {
        handleEmailVerification()
    }
}


// 注册
const validateUsername = () => {
    // 检查是否包含中文字符
    if (/[\u4e00-\u9fa5]/.test(registerForm.username)) {
        ElMessage.error(t('register.errors.noChineseAllowed') || '用户名不能包含中文字符')
        return false
    }
    return true
}

const handleRegister = async () => {
    if (!registerForm.email) {
        ElMessage.error(t('register.errors.enterEmail'))
        return
    }
    if (!registerForm.username) {
        ElMessage.error(t('register.errors.enterUsername'))
        return
    }
    if (!validateUsername()) {
        return
    }
    if (!registerForm.password) {
        ElMessage.error(t('register.errors.enterPassword'))
        return
    }

    const res = await api.post('/api/chatGptUser/register', registerForm)
    if (res.data.code === 0) {
        ElMessage.success(t('register.errors.verificationSuccess'))
        // 这里添加你的跳转逻辑
        router.push({ path: "login", params: { username: registerForm.username, password: registerForm.password } })
    }

    else {
        ElMessage.error(res.data.msg)
    }
}

const passwordVisible = ref(false)
const handlePasswordVisible = () => {
    passwordVisible.value = !passwordVisible.value
}
</script>

<style scoped>
@import url(authStepLayout.css);
@import url(button.css);
@import url(root.css);
@import url(route.css);

.eye-icon {
    position: absolute;
    right: 0.5rem;
    top: 10px;
    width: 30px;
    height: 30px;
}

.verify-code-wrapper {
    position: relative;
}

.resend-code {
    position: absolute;
    right: 0.5rem;
    top: 10px;
    color: #10a37f;
    cursor: pointer;
    font-size: 14px;
}

.resend-code.disabled {
    color: #ccc;
    cursor: not-allowed;
}
</style>