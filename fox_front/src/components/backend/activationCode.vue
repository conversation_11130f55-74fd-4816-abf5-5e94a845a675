<template>
    <div class="activation-code-container">
        <!-- 工具栏部分改为响应式布局 -->
        <el-row :gutter="10" class="toolbar">
            <el-col :xs="24" :sm="8" :md="6" :lg="4" class="toolbar-item">
                <el-input v-model="searchQuery.query" placeholder="请输入激活码/备注"></el-input>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="primary" @click="fetchData" :icon="Search">刷新</el-button>
            </el-col>
            <el-col :xs="12" :sm="6" :md="5" :lg="4" class="toolbar-item">
                <el-button type="primary" @click="openGenerateDialog">批量生成激活码</el-button>
            </el-col>
            <el-col :xs="12" :sm="6" :md="5" :lg="4" class="toolbar-item" v-if="selected.length > 0">
                <el-button type="danger" @click="batchDelete">批量删除</el-button>
            </el-col>
        </el-row>

        <!-- 表格响应式处理 -->
        <div class="table-container">
            <el-table :data="tableData" border @sort-change="sortData" 
                     style="margin-top: 10px;" 
                     max-height="500px"
                     @selection-change="handleSelectionChange"
                     :class="{ 'mobile-table': isMobile }"
                     v-bind="isMobile ? { size: 'small' } : {}">
                <el-table-column type="selection"/>
                <el-table-column prop="id" label="ID" width="50" />
                <el-table-column prop="activationCode" label="激活码"/>
                <el-table-column prop="salesPlanName" label="订阅名称" width="120" />
                <el-table-column prop="status" label="状态" width="100">
                    <template #default="scope">
                        <el-tag :type="scope.row.status === 0 ? 'success' : 'info'">
                            {{ scope.row.status === 0 ? '未使用' : '已使用' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="usedBy" label="兑换人" width="120" />
                <el-table-column prop="usedAt" label="兑换时间" width="150" :formatter="formatDate" />
                <el-table-column prop="expirationTime" label="到期时间" width="150" :formatter="formatDate" />
                <el-table-column prop="remark" width="150" overflow-showtooltip label="备注" />
                <el-table-column prop="createdAt" label="创建时间" sortable="custom" width="150" :formatter="formatDate" />
                <el-table-column label="操作" width="120">
                    <template #default="scope">
                        <el-button size="small" type="danger" @click="deleteCode(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页组件响应式处理 -->
        <el-pagination v-model:currentPage="searchQuery.pageNum" 
                      :page-size="searchQuery.pageSize"
                      :total="searchQuery.totalItems" 
                      :layout="isMobile ? 'prev, pager, next' : 'total, prev, pager, next'"
                      :page-sizes="[10, 20, 50, 100]"
                      @current-change="handlePageChange" 
                      background 
                      class="pagination-container" />

        <!-- 生成激活码弹窗 -->
        <el-dialog title="批量生成激活码" v-model="generateDialogVisible" width="500px">
            <el-form :model="generateForm" :rules="rules" ref="formRef" label-width="100px">
                <el-form-item label="生成数量" prop="num">
                    <el-input-number v-model="generateForm.num" :min="1" :max="100" />
                </el-form-item>

                <el-form-item label="选择订阅" prop="salesPlanId">
                    <el-select v-model="generateForm.salesPlanId" placeholder="选择订阅类型">
                        <el-option v-for="plan in salesPlans" :key="plan.id" :label="plan.name" :value="plan.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="到期时间" prop="expirationDate">
                    <el-date-picker v-model="generateForm.expirationDate" type="datetime" placeholder="选择到期时间" />
                </el-form-item>

                <!-- <el-form-item label="备注" prop="remark">
                    <el-input type="textarea" v-model="generateForm.remark" placeholder="请输入备注"></el-input>
                </el-form-item> -->
            </el-form>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="generateDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitGenerateForm">确定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 显示生成的激活码的对话框 -->
        <el-dialog title="生成的激活码" v-model="showActivationCodeDialog" width="500px">
            <div class="activation-code-display">
                <el-input
                    type="textarea"
                    v-model="generatedActivationCode"
                    readonly
                    rows="5"
                    placeholder="生成的激活码将显示在这里"
                />
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showActivationCodeDialog = false">关闭</el-button>
                    <el-button type="primary" @click="copyActivationCode">
                        复制激活码
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { ref, reactive, getCurrentInstance, onMounted, onBeforeUnmount } from 'vue';
import api from '@/axios';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';

export default {
    setup() {
        const { proxy } = getCurrentInstance();
        const generateDialogVisible = ref(false);
        const tableData = ref([]);
        const selected = ref([]);

        const generateForm = reactive({
            num: 1,
            salesPlanId: '',
            expirationDate: '',
            remark: ''
        });

        const searchQuery = ref({
            pageNum: 1,
            pageSize: 10,
            totalItems: 0,
            query: ''
        });

        const handleSelectionChange = (val) => {
            selected.value = val.map(item => item.id);
        };

        const fetchData = async () => {
            const res = await api.post("/api/activationCode/page", searchQuery.value);
            if (res.data.code == 0) {
                tableData.value = res.data.data;
                searchQuery.value.totalItems = res.data.total;
            } else {
                ElMessage.error(res.data.msg);
            }
        };

        const formatDate = (row, col, cellvalue) => {
            return cellvalue ? proxy.$dateFormat(cellvalue) : '-';
        };

        const showActivationCodeDialog = ref(false);
        const generatedActivationCode = ref('');

        const submitGenerateForm = async () => {
            await proxy.$refs.formRef.validate();
            const res = await api.post("/api/activationCode/batchAdd", generateForm);
            if (res.data.code === 0) {
                generatedActivationCode.value = res.data.data;
                generateDialogVisible.value = false;
                showActivationCodeDialog.value = true;
                fetchData();
                Object.assign(generateForm, {
                    num: 1,
                    salesPlanId: '',
                    expirationDate: '',
                    remark: ''
                });
            } else {
                ElMessage.error(res.data.msg);
            }
        };

        const deleteCode = async (row) => {
            ElMessageBox.confirm('确定删除该激活码?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(async () => {
                const res = await api.post("/api/activationCode/deleteByIds", [row.id]);
                if (res.data.code === 0) {
                    ElMessage.success('删除成功');
                    fetchData();
                } else {
                    ElMessage.error(res.data.msg);
                }
            });
        };

        const batchDelete = async () => {
            if (selected.value.length === 0) {
                ElMessage.warning('请选择要删除的激活码');
                return;
            }
            ElMessageBox.confirm(`确定删除选中的 ${selected.value.length} 个激活码?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(async () => {
                const res = await api.post("/api/activationCode/deleteByIds", selected.value);
                if (res.data.code === 0) {
                    ElMessage.success('删除成功');
                    fetchData();
                } else {
                    ElMessage.error(res.data.msg);
                }
            });
        };

        const sortData = ({ prop, order }) => {
            fetchData();
        };

        const openGenerateDialog = () => {
            generateDialogVisible.value = true;
        };

        const rules = ref({
            num: [{ required: true, message: '请输入生成数量', trigger: 'blur' }],
            salesPlanId: [{ required: true, message: '请选择订阅类型', trigger: 'change' }],
        });

        const salesPlans = ref([]);
        const fetchSalesPlans = async () => {
            const payload = {
                pageNum: 1,
                pageSize: 10000
            }
            const res = await api.post('/api/salesPlan/page', payload)
            salesPlans.value = res.data.data;
        };

        const handlePageChange = (page) => {
            searchQuery.value.pageNum = page;
            fetchData();
        };

        const isMobile = ref(window.innerWidth <= 768);
        
        const checkMobile = () => {
            isMobile.value = window.innerWidth <= 768;
        };

        const copyActivationCode = async () => {
            try {
                await navigator.clipboard.writeText(generatedActivationCode.value);
                ElMessage.success('已复制激活码到剪贴板');
            } catch (err) {
                ElMessage.error('复制失败，请手动复制');
            }
        };

        onMounted(() => {
            window.addEventListener('resize', checkMobile);
        });

        onBeforeUnmount(() => {
            window.removeEventListener('resize', checkMobile);
        });

        fetchSalesPlans();
        fetchData();

        return {
            tableData,
            fetchData,
            searchQuery,
            formatDate,
            generateDialogVisible,
            submitGenerateForm,
            deleteCode,
            sortData,
            Search,
            openGenerateDialog,
            generateForm,
            rules,
            salesPlans,
            handlePageChange,
            handleSelectionChange,
            selected,
            batchDelete,
            isMobile,
            showActivationCodeDialog,
            generatedActivationCode,
            copyActivationCode,
        };
    },
};
</script>

<style scoped>
.activation-code-container {
    padding: 10px;
}

.toolbar {
    margin-bottom: 15px;
}

.toolbar-item {
    margin-bottom: 10px;
}

.table-container {
    width: 100%;
    overflow-x: auto;
}

.mobile-table {
    font-size: 12px;
}

.mobile-table :deep(.el-table-column) {
    padding: 5px !important;
}

.pagination-container {
    margin-top: 15px;
    display: flex;
    justify-content: center;
}

@media screen and (max-width: 768px) {
    .el-button {
        width: 100%;
    }
    
    .el-pagination {
        font-size: 12px;
    }
}

.activation-code-display {
    margin-bottom: 20px;
}

.activation-code-display :deep(.el-textarea__inner) {
    font-family: monospace;
    font-size: 14px;
}
</style>