<template>
    <div class="chat-container" style="margin-bottom: 10px;">
        <!-- 工具栏部分 -->
        <el-row :gutter="20">
            <el-col :span="4">
                <el-input v-model="searchQuery.query" placeholder="请输入用户名称"></el-input>
            </el-col>
            <el-col :span="2">
                <el-button type="primary" @click="fetchData" :icon="Search">刷新</el-button>
            </el-col>
            <!-- 批量删除 -->
            <el-col :span="4">
                <el-button type="danger" @click="batchDelete">批量删除</el-button>
            </el-col>
        </el-row>

        <el-table :data="tableData" border @sort-change="sortChats" style="margin-top: 10px;" max-height="500px"
            :header-cell-style="{ 'text-align': 'center' }" :cell-style="{ 'text-align': 'center' }"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="ID" width="50" />
            <el-table-column prop="userToken" label="用户名称" width="120" />
            <el-table-column prop="email" label="官网邮箱" width="180" />
            <el-table-column prop="convid" label="会话ID" width="180" />
            <el-table-column prop="title" label="会话标题" width="150" />
            <el-table-column prop="content" label="聊天内容" show-overflow-tooltip width="300">
                <template #default="scope">
                    <div style="text-align: left; white-space: pre-wrap;">{{ scope.row.content }}</div>
                </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" sortable="custom" width="150" :formatter="formatDate" />
            <el-table-column prop="updateTime" label="修改时间" sortable="custom" width="150" :formatter="formatDate" />
            <el-table-column label="操作" width="120">
                <template #default="scope">
                    <el-button size="small" type="primary" @click="deleteChat(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination v-model:currentPage="searchQuery.pageNum" :page-size="searchQuery.pageSize"
            :total="searchQuery.totalItems" layout="total, prev, pager, next" :page-sizes="[10, 20, 50, 100]"
            @current-change="handlePageChange" background />
    </div>
</template>

<script>
import { ref, getCurrentInstance } from 'vue';
import api from '@/axios';
import { Search } from '@element-plus/icons-vue';

export default {
    setup() {
        const { proxy } = getCurrentInstance();
        const tableData = ref([]);
        const detailVisible = ref(false);
        const currentChat = ref({});
        const selected = ref([]);

        const handleSelectionChange = (val) => {
            selected.value = val.map(item => item.id);
        };

        const searchQuery = ref({
            pageNum: 1,
            pageSize: 10,
            totalItems: 0,
            query: '',
            conversationId: ''
        });

        const fetchData = async () => {
            const res = await api.post("/api/conversation/page", searchQuery.value);
            tableData.value = res.data.data;
            searchQuery.value.totalItems = res.data.total;
        };
        fetchData();

        const formatDate = (row, col, cellvalue) => {
            return proxy.$dateFormat(cellvalue);
        };

        const sortChats = ({ prop, order }) => {
            // 处理排序逻辑
            fetchData();
        };

        const viewDetail = (row) => {
            currentChat.value = row;
            detailVisible.value = true;
        };

        const handlePageChange = (page) => {
            searchQuery.value.pageNum = page;
            fetchData();
        };
        const deleteChat = async (row) => {
            const res = await api.post("/api/conversation/deleteById", [row.id]);
            if (res.data.code == 0) {
                fetchData();
            }
        };
        const batchDelete = async () => {
            const res = await api.post("/api/conversation/deleteByIds", selected.value);
            if (res.data.code == 0) {
                fetchData();
            }
        };

        return {
            tableData,
            searchQuery,
            detailVisible,
            currentChat,
            fetchData,
            formatDate,
            sortChats,
            viewDetail,
            Search,
            handlePageChange,
            handleSelectionChange,
            batchDelete,
            deleteChat
        };
    },
};
</script>

<style scoped>
.chat-container {
    padding: 20px;
}

.el-table {
    margin-bottom: 20px;
}

.chat-detail {
    padding: 20px;
}

.detail-item {
    margin-bottom: 15px;
}

.detail-item label {
    font-weight: bold;
    margin-right: 10px;
}

.chat-content {
    margin-top: 10px;
    white-space: pre-wrap;
    background: #f5f7fa;
    padding: 10px;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
}
</style>