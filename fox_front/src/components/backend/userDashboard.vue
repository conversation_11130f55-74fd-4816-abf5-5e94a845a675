<template>
  <div class="user-distribution-chart">
    <!-- 用户增长趋势卡片 -->
    <el-card class="box-card growth-trend">
      <template #header>
        <div class="card-header">
          <span>用户增长趋势</span>
          <div class="period-selector">
            <el-radio-group v-model="trendPeriod" size="small" @change="fetchUserGrowthTrend">
              <el-radio-button :label="7">7天</el-radio-button>
              <el-radio-button :label="30">30天</el-radio-button>
              <el-radio-button :label="90">90天</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      <div class="growth-summary">
        <div class="growth-rate">
          <span class="label">增长率：</span>
          <span class="value" :class="{ 'positive': isPositiveGrowth }">{{ growthRate }}</span>
        </div>
      </div>
      <div ref="trendChartContainer" style="height: 300px;"></div>
    </el-card>
    
    <!-- 用户分组分布卡片 -->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>用户分组分布</span>
        </div>
      </template>
      <div class="distribution-summary">
        <div class="total-users">
          <span class="label">总用户数：</span>
          <span class="value">{{ totalUsers }}</span>
        </div>
      </div>
      <div ref="pieChartContainer" style="height: 300px;"></div>
    </el-card>
    
    <!-- 分组详细信息卡片 -->
    <el-card class="box-card group-details">
      <template #header>
        <div class="card-header">
          <span>分组详细信息</span>
        </div>
      </template>
      <el-table :data="groupData" style="width: 100%">
        <el-table-column prop="groupTitle" label="分组名称" />
        <el-table-column prop="userCount" label="用户数量" />
        <el-table-column prop="percentage" label="占比" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import * as echarts from 'echarts';
import api from '@/axios';

// 用户分组分布数据
const totalUsers = ref(0);
const groupData = ref([]);
const pieChartContainer = ref(null);
let pieChart = null;

// 用户增长趋势数据
const trendPeriod = ref(7);
const growthRate = ref('0.00%');
const trendChartContainer = ref(null);
const dailyNewUsers = ref([]);
const totalUserTrend = ref([]);
let trendChart = null;

const isPositiveGrowth = computed(() => {
  const rate = parseFloat(growthRate.value);
  return !isNaN(rate) && rate > 0;
});

// 获取用户分组分布数据
const fetchUserDistribution = async () => {
  try {
    const response = await api.get('/api/dashboard/users/group-distribution');
    if (response.data.code === 0) {
      const data = response.data.data;
      totalUsers.value = data.totalUsers;
      groupData.value = data.groupData;
      renderPieChart();
    }
  } catch (error) {
    console.error('获取用户分布数据失败:', error);
  }
};

// 获取用户增长趋势数据
const fetchUserGrowthTrend = async () => {
  try {
    const response = await api.get(`/api/dashboard/users/growth?days=${trendPeriod.value}`);
    if (response.data.code === 0) {
      const data = response.data.data;
      dailyNewUsers.value = data.dailyNewUsers;
      totalUserTrend.value = data.totalUserTrend;
      growthRate.value = data.growthRate || '0.00%';
      renderTrendChart();
    }
  } catch (error) {
    console.error('获取用户增长趋势数据失败:', error);
  }
};

// 渲染用户分组分布饼图
const renderPieChart = () => {
  if (!pieChartContainer.value) return;
  
  if (!pieChart) {
    pieChart = echarts.init(pieChartContainer.value);
  }
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 'bottom',
      data: groupData.value.map(item => item.groupTitle)
    },
    series: [
      {
        name: '用户分组',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: groupData.value.map(item => ({
          value: item.userCount,
          name: item.groupTitle
        }))
      }
    ]
  };
  
  pieChart.setOption(option);
};

// 渲染用户增长趋势图
const renderTrendChart = () => {
  if (!trendChartContainer.value) return;
  
  if (!trendChart) {
    trendChart = echarts.init(trendChartContainer.value);
  }
  
  const dates = totalUserTrend.value.map(item => item.date);
  const newUsers = dailyNewUsers.value.map(item => item.newUsers);
  const totalUsers = totalUserTrend.value.map(item => item.totalUsers);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['新增用户', '总用户数'],
      bottom: 'bottom'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: dates,
        axisLabel: {
          rotate: 45,
          formatter: (value) => {
            // 格式化日期，只显示月和日
            const date = new Date(value);
            return `${date.getMonth() + 1}/${date.getDate()}`;
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '新增用户',
        position: 'left',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#5470C6'
          }
        },
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '总用户数',
        position: 'right',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#91CC75'
          }
        },
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: '新增用户',
        type: 'bar',
        data: newUsers
      },
      {
        name: '总用户数',
        type: 'line',
        yAxisIndex: 1,
        data: totalUsers,
        smooth: true
      }
    ]
  };
  
  trendChart.setOption(option);
};

onMounted(() => {
  fetchUserDistribution();
  fetchUserGrowthTrend();
  
  window.addEventListener('resize', () => {
    pieChart?.resize();
    trendChart?.resize();
  });
});
</script>

<style scoped>
.user-distribution-chart {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.distribution-summary, .growth-summary {
  margin-bottom: 15px;
}

.total-users, .growth-rate {
  font-size: 16px;
}

.total-users .value, .growth-rate .value {
  font-weight: bold;
}

.growth-rate .positive {
  color: #67C23A;
}

.growth-rate .negative {
  color: #F56C6C;
}

.group-details, .growth-trend {
  margin-top: 20px;
}

.growth-trend {
  margin-bottom: 20px;
}

.period-selector {
  display: flex;
  align-items: center;
}
</style>