<template>
    <div class="pagination-container" style="margin-bottom: 10px;">
        <!-- 工具栏部分 -->
        <el-row :gutter="20">
            <el-col :span="2">
                <el-button type="primary" @click="fetchData" :icon="Search">刷新</el-button>
            </el-col>
            <el-col :span="4">
                <el-button type="primary" @click="openAddPaymentMethodDialog">添加</el-button>
            </el-col>
        </el-row>
        <el-table :data="tableData" border @sort-change="sortPaymentMethod" style="margin-top: 10px;" max-height="500px"
            :header-cell-style="{ 'text-align': 'center' }" :cell-style="{ 'text-align': 'center' }"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="ID" width="50" />
            <el-table-column prop="name" label="名称" width="100" />
            <el-table-column prop="paymentType" label="支付方式" width="150">
                <template #default="scope">
                    {{ getPaymentLbalByValue(scope.row.paymentType) }}
                </template>
            </el-table-column>
            <el-table-column prop="appid" label="AppID" width="150" />
            <el-table-column prop="appkey" label="AppKey" width="150" />
            <el-table-column prop="callbackUrl" label="回调地址" width="200" />
            <el-table-column prop="paymentUrl" label="支付地址" width="200" />
            <el-table-column prop="isEnabled" label="是否启用" width="100">
                <template #default="scope">
                    <el-switch v-model="scope.row.isEnabled"
                        @change="(newValue) => updatePaymethod(scope.row, newValue)"></el-switch>
                </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="创建时间" sortable="custom" width="200" :formatter="formatDate" />
            <el-table-column prop="updatedAt" label="修改时间" sortable="custom" width="200" :formatter="formatDate" />
            <el-table-column label="操作" width="180">
                <template #default="scope">
                    <el-button size="small" type="primary" @click="editPaymentMethod(scope.row)">编辑</el-button>
                    <el-button size="small" type="danger" @click="deletePaymentMethod(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination v-model:currentPage="searchQuery.pageNum" :page-size="searchQuery.pageSize"
            :total="searchQuery.totalItems" layout="total, prev, pager, next" :page-sizes="[10, 20, 50, 100]"
            @current-change="handlePageChange" background />
    </div>

    <!-- 添加支付方式弹窗 -->
    <el-dialog title="添加支付方式" v-model="addPaymentMethodVisible" width="500px">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
            <el-form-item label="名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入名称"></el-input>
            </el-form-item>
            <el-form-item label="支付方式" prop="paymentType">
                <el-select v-model="form.paymentType" placeholder="请选择支付方式">
                    <el-option v-for="item in paymentTypes" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="AppID" prop="appid">
                <el-input v-model="form.appid" placeholder="请输入AppID"></el-input>
            </el-form-item>
            <el-form-item label="AppKey" prop="appkey">
                <el-input v-model="form.appkey" placeholder="请输入AppKey"></el-input>
            </el-form-item>
            <el-form-item label="回调地址" prop="callbackUrl">
                <el-input v-model="form.callbackUrl" placeholder="输入你的平台域名即可,如 https://域名"></el-input>
            </el-form-item>
            <el-form-item label="支付地址" prop="paymentUrl">
                <el-input v-model="form.paymentUrl" placeholder="请输入易支付地址,如 https://易支付域名"></el-input>
            </el-form-item>
            <el-form-item label="是否启用" prop="isEnabled">
                <el-switch v-model="form.isEnabled"></el-switch>
            </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
            <el-button @click="addPaymentMethodVisible = false">取消</el-button>
            <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
    </el-dialog>
    <!-- 添加支付方式弹窗 -->
    <el-dialog title="修改支付方式" v-model="updatePaymentMethodVisible" width="500px">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
            <el-form-item label="名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入名称"></el-input>
            </el-form-item>
            <el-form-item label="支付方式" prop="paymentType">
                <el-select v-model="form.paymentType" placeholder="请选择支付方式">
                    <el-option v-for="item in paymentTypes" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="AppID" prop="appid">
                <el-input v-model="form.appid" placeholder="请输入AppID"></el-input>
            </el-form-item>
            <el-form-item label="AppKey" prop="appkey">
                <el-input v-model="form.appkey" placeholder="请输入AppKey"></el-input>
            </el-form-item>
            <el-form-item label="回调地址" prop="callbackUrl">
                <el-input v-model="form.callbackUrl" placeholder="输入你的平台域名即可,如 https://域名"></el-input>
            </el-form-item>
            <el-form-item label="支付地址" prop="paymentUrl">
                <el-input v-model="form.paymentUrl" placeholder="请输入易支付地址,如 https://易支付域名"></el-input>
            </el-form-item>
            <el-form-item label="是否启用" prop="isEnabled">
                <el-switch v-model="form.isEnabled"></el-switch>
            </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
            <el-button @click="updatePaymentMethodVisible = false">取消</el-button>
            <el-button type="primary" @click="submitUpdateForm">确定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { ref, reactive, toRefs } from 'vue';
import api from '@/axios';
import { getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';

export default {
    setup() {
        const { proxy } = getCurrentInstance();
        const addPaymentMethodVisible = ref(false);
        const updatePaymentMethodVisible = ref(false);
        const tableData = ref([]);
        const selected = ref([]);

        const handleSelectionChange = (val) => {
            selected.value = val.map(item => item.id);
            console.log(val);
        };

        const form = reactive({
            name: '',
            paymentType: '',
            appid: '',
            appkey: '',
            callbackUrl: '',
            paymentUrl: '',
            isEnabled: true
        });

        const defaultForm = { ...form };

        const searchQuery = ref({
            query: '',
            pageNum: 1,
            pageSize: 10,
            sortField: '',
            sortOrder: '',
            totalItems: 0
        });

        const handlePageChange = (page) => {
            searchQuery.pageNum = page;
            fetchData();
        };

        const fetchData = async () => {
            const res = await api.post("/api/paymentMethod/page", searchQuery.value);
            tableData.value = res.data.data;
            searchQuery.value.totalItems = res.data.total;
            console.log(searchQuery.totalItems);
        };

        fetchData();

        const formatDate = (row, col, cellvalue) => {
            return proxy.$dateFormat(cellvalue);
        };

        const submitForm = async () => {
            await proxy.$refs.formRef.validate();
            const formCopy = { ...form };
            const res = await api.post("/api/paymentMethod/addOrUpdate", formCopy);
            if (res.data.code === 0) {
                ElMessage.success('添加成功');
                addPaymentMethodVisible.value = false;
                fetchData();
                Object.assign(form, defaultForm);
            } else {
                ElMessage.error(res.data.msg);
            }
        };
        const submitUpdateForm = async () => {
            await proxy.$refs.formRef.validate();
            const formCopy = { ...form };
            const res = await api.post("/api/paymentMethod/addOrUpdate", formCopy);
            if (res.data.code === 0) {
                ElMessage.success('修改成功');
                updatePaymentMethodVisible.value = false;
                fetchData();
                Object.assign(form, defaultForm);
            } else {
                ElMessage.error(res.data.msg);
            }
        };
        const updatePaymethod = async (row, newValue) => {
            const res = await api.post("/api/paymentMethod/addOrUpdate", row);
            if (res.data.code === 0) {
                ElMessage.success('修改成功');
                updatePaymentMethodVisible.value = false;
                fetchData();
                Object.assign(form, defaultForm);
            } else {
                ElMessage.error(res.data.msg);
            }
        };
        const deletePaymentMethod = async (row) => {
            ElMessageBox.confirm('确定删除该支付方式?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(async () => {
                const res = await api.post("/api/paymentMethod/deleteById?id=" + row.id);
                if (res.data.code === 0) {
                    ElMessage.success('删除成功');
                    fetchData();
                } else {
                    ElMessage.error(res.data.msg);
                }
            });
        };

        const sortPaymentMethod = ({ prop, order }) => {
            const sortOrder = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : '';
            searchQuery.value.sortField = prop;
            searchQuery.value.sortOrder = sortOrder;
            fetchData();
        };

        const editPaymentMethod = async (row) => {
            Object.assign(form, row);
            updatePaymentMethodVisible.value = true;
        };

        const rules = ref({
            name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
            paymentType: [{ required: true, message: '请输入支付方式', trigger: 'blur' }],
            appid: [{ required: true, message: '请输入AppID', trigger: 'blur' }],
            appkey: [{ required: true, message: '请输入AppKey', trigger: 'blur' }],
            callbackUrl: [{ required: true, message: '请输入回调地址', trigger: 'blur' }],
            paymentUrl: [{ required: true, message: '请输入支付地址', trigger: 'blur' }],
        });
        const paymentTypes = reactive([
            { label: '易支付（微信）', value: 'yzf_wxpay' },
            { label: '易支付（支付宝）', value: 'yzf_alipay' },
        ]);
        const getPaymentLbalByValue = (value) => {
            return paymentTypes.find(item => item.value === value).label;
        };

        return {
            tableData,
            handlePageChange,
            searchQuery,
            formatDate,
            addPaymentMethodVisible,
            submitForm,
            submitUpdateForm,
            deletePaymentMethod,
            fetchData,
            sortPaymentMethod,
            Search,
            openAddPaymentMethodDialog: () => { addPaymentMethodVisible.value = true },
            handleSelectionChange,
            form,
            rules,
            editPaymentMethod,
            paymentTypes,
            getPaymentLbalByValue,
            selected,
            updatePaymentMethodVisible,
            updatePaymethod
        };
    },
};
</script>

<style scoped>
.pagination-container {
    padding: 20px;
}

.el-table {
    margin-bottom: 20px;
    text-align: center;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}
</style>
