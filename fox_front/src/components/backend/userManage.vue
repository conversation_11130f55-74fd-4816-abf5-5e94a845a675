<template>
    <div class="pagination-container">
        <!-- 工具栏部分 -->
        <el-row :gutter="10" class="toolbar">
            <el-col :xs="24" :sm="8" :md="6" :lg="4" class="toolbar-item">
                <el-input v-model="searchQuery.query" placeholder="请输入用户名/邮箱/备注"></el-input>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="primary" @click="fetchData" :icon="Search">查询</el-button>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="primary" @click="addUserDialogVisible = true">添加用户</el-button>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="success" @click="batchAddUserDialogVisible = true">批量添加用户</el-button>
            </el-col>
        </el-row>

        <!-- PC端表格 -->
        <div v-if="!isMobile()" class="table-container">
            <el-table :data="tableData" border @sort-change="sortCustomer" style="width: 100%;margin-top: 10px;"
                :max-height="tableMaxHeight" :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }" class="responsive-table">
                <!-- 保持原有的表格列不变 -->
                <el-table-column type="selection" width="55" />
                <el-table-column prop="id" label="id" width="50" />
                <el-table-column prop="userToken" label="userToken" width="150" />
                <el-table-column prop="email" label="邮箱" width="150" />
                <el-table-column prop="status" label="账号状态" width="110">
                    <template #default="scope">
                        <el-switch v-model="scope.row.status" inline-prompt :active-value="0" :inactive-value="-1"
                            active-text="正常" inactive-text="封禁"
                            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                            @change="(newValue) => updateUser(scope.row, newValue)" />
                    </template>
                </el-table-column>
                <el-table-column prop="groupId" label="分组" width="150">
                    <template #default="scope">
                        {{ getUserGroupName(scope.row.groupId) }}
                    </template>
                </el-table-column>
                <el-table-column prop="expireTime" label="普通会员过期时间" width="150" :formatter="formatDate" />
                <el-table-column prop="plusExpireTime" label="高级会员过期时间" width="150" :formatter="formatDate" />
                <el-table-column prop="limit" label="绘图积分" width="100" />
                <el-table-column prop="remark" label="备注" width="150" />
                <el-table-column prop="createTime" label="创建时间" sortable="custom" width="150" :formatter="formatDate" />
                <el-table-column prop="updateTime" label="修改时间" width="150" :formatter="formatDate" />
                <el-table-column fixed="right" label="操作" width="280">
                    <template #default="scope">
                        <el-button size="small" type="primary" @click="editRow(scope.row)">编辑</el-button>
                        <el-button size="small" type="warning" @click="editUserRateLimit(scope.row)">设置专属速率</el-button>
                        <el-button size="small" type="danger" @click="deleteUser(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 移动端卡片列表 -->
        <div v-else class="mobile-card-list">
            <el-card v-for="item in tableData" :key="item.id" class="mobile-card" shadow="hover">
                <div class="mobile-card-header">
                    <span class="mobile-card-title">用户: {{ item.userToken }}</span>
                    <el-switch v-model="item.status" inline-prompt :active-value="0" :inactive-value="-1"
                        active-text="正常" inactive-text="封禁"
                        style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                        @change="(newValue) => updateUser(item, newValue)" />
                </div>
                <div class="mobile-card-content">
                    <div class="mobile-card-item">
                        <span class="label">邮箱:</span>
                        <span class="value">{{ item.email || '-' }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">分组:</span>
                        <span class="value">{{ getUserGroupName(item.groupId) }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">普通会员过期:</span>
                        <span class="value">{{ formatDate(item, null, item.expireTime) }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">高级会员过期:</span>
                        <span class="value">{{ formatDate(item, null, item.plusExpireTime) }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">绘图积分:</span>
                        <span class="value">{{ item.limit }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">备注:</span>
                        <span class="value">{{ item.remark || '-' }}</span>
                    </div>
                </div>
                <div class="mobile-card-footer">
                    <el-button size="small" type="primary" @click="editRow(item)">编辑</el-button>
                    <el-button size="small" type="warning" @click="editUserRateLimit(item)">设置专属速率</el-button>
                    <el-button size="small" type="danger" @click="deleteUser(item)">删除</el-button>
                </div>
            </el-card>
        </div>

        <!-- 分页器 -->
        <el-pagination v-model:currentPage="searchQuery.pageNum" :page-size="searchQuery.pageSize"
            :total="searchQuery.totalItems" :layout="isMobile() ? 'prev, pager, next' : 'total, prev, pager, next'"
            :page-sizes="[10, 20, 50, 100]" @current-change="handlePageChange" background />
    </div>

    <!-- 添加用户弹窗 -->
    <el-dialog title="添加用户" v-model="addUserDialogVisible" :width="isMobile() ? '95%' : '900px'" :close-on-click-modal="false">
        <el-form ref="formRef" :model="form" :rules="rules" :label-width="isMobile() ? 'auto' : '120px'"
            :label-position="isMobile() ? 'top' : 'right'" status-icon>
            <el-row :gutter="isMobile() ? 0 : 20">
                <el-col :xs="24" :sm="12">
                    <el-form-item label="用户名" prop="username">
                        <el-input v-model="form.username" placeholder="请输入用户名">
                            <template #suffix>
                                <el-icon class="el-input__icon"><User /></el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                    <el-form-item label="密码" prop="password">
                        <el-input v-model="form.password" required placeholder="请输入密码">
                            <template #suffix>
                                <el-icon class="el-input__icon"><Lock /></el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="isMobile() ? 0 : 20">
                <el-col :xs="24" :sm="12">
                    <el-form-item label="邮箱" prop="email">
                        <el-input v-model="form.email" placeholder="请输入邮箱">
                            <template #suffix>
                                <el-icon class="el-input__icon"><Message /></el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                    <el-form-item label="分组" prop="groupId">
                        <el-select v-model="form.groupId" placeholder="请选择分组" style="width: 100%">
                            <el-option v-for="g in filteredUserGroups" :key="g.id" :label="g.title" :value="g.id" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="isMobile() ? 0 : 20">
                <el-col :xs="24" :sm="12">
                    <el-form-item label="过期时间(普通)" prop="expireTime">
                        <el-date-picker v-model="form.expireTime" type="datetime" value-format="YYYY-MM-DDTHH:mm:ss" placeholder="请选择过期时间"
                            :shortcuts="shortcuts"
                            style="width: 100%" />
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                    <el-form-item label="过期时间(高级)" prop="plusExpireTime">
                        <el-date-picker v-model="form.plusExpireTime" type="datetime" value-format="YYYY-MM-DDTHH:mm:ss" placeholder="请选择过期时间"
                            :shortcuts="shortcuts"
                            style="width: 100%" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="isMobile() ? 0 : 20">
                <el-col :xs="24" :sm="12">
                    <el-form-item label="绘图积分" prop="limit">
                        <el-input v-model.number="form.limit" type="number" placeholder="请输入绘图积分">
                            <template #suffix>
                                <el-icon class="el-input__icon"><Picture /></el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                    <el-form-item label="Token登录" prop="enableUserTokenLogin">
                        <el-switch v-model="form.enableUserTokenLogin" inline-prompt :active-value="1" :inactive-value="0"
                            active-text="开启" inactive-text="关闭"
                            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="备注">
                <el-input v-model="form.remark" placeholder="请输入备注" :rows="3" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="addUserDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 编辑用户弹窗 -->
    <el-dialog title="编辑用户" v-model="updateUserDialogVisible" :width="isMobile() ? '95%' : '900px'" :close-on-click-modal="false">
        <el-tabs v-model="activeTab">
            <el-tab-pane label="基本信息" name="basic">
                <el-form ref="formRef" :model="form" :rules="editRules" :label-width="isMobile() ? 'auto' : '120px'"
                    :label-position="isMobile() ? 'top' : 'right'" status-icon>
                    <!-- 使用与添加用户弹窗相同的响应式布局结构 -->
                    <el-row :gutter="isMobile() ? 0 : 20">
                        <el-col :xs="24" :sm="12">
                            <el-form-item label="用户名" prop="username">
                                <el-input v-model="form.username" placeholder="请输入用户名">
                                    <template #suffix>
                                        <el-icon class="el-input__icon"><User /></el-icon>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12">
                            <el-form-item label="密码" prop="password">
                                <el-input v-model="form.password" placeholder="请输入密码">
                                    <template #suffix>
                                        <el-icon class="el-input__icon"><Lock /></el-icon>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="isMobile() ? 0 : 20">
                        <el-col :xs="24" :sm="12">
                            <el-form-item label="邮箱" prop="email">
                                <el-input v-model="form.email" placeholder="请输入邮箱">
                                    <template #suffix>
                                        <el-icon class="el-input__icon"><Message /></el-icon>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12">
                            <el-form-item label="分组" prop="groupId">
                                <el-select v-model="form.groupId" placeholder="请选择分组" style="width: 100%" @change="handleGroupChange">
                                    <el-option v-for="g in filteredUserGroups" :key="g.id" :label="g.title" :value="g.id" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="isMobile() ? 0 : 20">
                        <el-col :xs="24" :sm="12">
                            <el-form-item label="过期时间(普通)" prop="expireTime">
                                <el-date-picker v-model="form.expireTime" type="datetime" value-format="YYYY-MM-DDTHH:mm:ss" placeholder="请选择过期时间"
                                    :shortcuts="shortcuts"
                                    style="width: 100%" />
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12">
                            <el-form-item label="过期时间(高级)" prop="plusExpireTime">
                                <el-date-picker v-model="form.plusExpireTime" type="datetime" value-format="YYYY-MM-DDTHH:mm:ss" placeholder="请选择过期时间"
                                    :shortcuts="shortcuts"
                                    style="width: 100%" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="isMobile() ? 0 : 20">
                        <el-col :xs="24" :sm="12">
                            <el-form-item label="绘图积分" prop="limit">
                                <el-input v-model.number="form.limit" type="number" placeholder="请输入绘图积分">
                                    <template #suffix>
                                        <el-icon class="el-input__icon"><Picture /></el-icon>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="isMobile() ? 0 : 20">
                        <el-col :xs="24" :sm="12">
                            <el-form-item label="Token登录" prop="enableUserTokenLogin">
                                <el-switch v-model="form.enableUserTokenLogin" inline-prompt :active-value="1" :inactive-value="0"
                                    active-text="开启" inactive-text="关闭"
                                    style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item label="备注">
                        <el-input v-model="form.remark" placeholder="请输入备注" :rows="3" />
                    </el-form-item>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="速率限制" name="rateLimit">
                <div class="rate-limit-container">
                    <div class="rate-limit-header">
                        <h3>用户专属速率限制</h3>
                        <p class="rate-limit-description">设置此用户的专属速率限制，优先级高于分组限制。如果未设置，将使用分组限制。</p>
                    </div>

                    <el-row class="rate-limit-header" style="font-weight:bold; margin-bottom: 8px; text-align:center;">
                        <el-col :span="4">模型</el-col>
                        <el-col :span="5">周期</el-col>
                        <el-col :span="5">速率</el-col>
                        <el-col :span="4">倍数</el-col>
                        <el-col :span="4">来源</el-col>
                        <el-col :span="2"></el-col>
                    </el-row>

                    <!-- 用户自定义速率限制 -->
                    <el-row v-for="(item, idx) in userRateLimits" :key="item._key" :gutter="10" style="margin-bottom: 10px; align-items: center;">
                        <el-col :span="4">
                            <el-input v-if="item.isCustom" v-model="item.model" placeholder="模型名" />
                            <el-text v-else>{{ item.label || item.model }}</el-text>
                        </el-col>
                        <el-col :span="5">
                            <el-select v-model="item.period" placeholder="选择周期">
                                <el-option v-for="per in useLimitPers" :key="per.value" :label="per.label" :value="per.value" />
                            </el-select>
                        </el-col>
                        <el-col :span="5">
                            <el-input v-model.number="item.rate" placeholder="速率" type="number" min="0" />
                        </el-col>
                        <el-col :span="4">
                            <el-input v-model.number="item.multiplier" placeholder="倍数" type="number" min="1" />
                        </el-col>
                        <el-col :span="4">
                            <el-tag type="success">用户</el-tag>
                        </el-col>
                        <el-col :span="2" style="text-align:center;">
                            <el-button type="danger" size="small" :icon="Delete" @click="removeUserRateLimit(item)"></el-button>
                        </el-col>
                    </el-row>

                    <!-- 添加自定义模型按钮 -->
                    <el-row style="margin-bottom: 20px;">
                        <el-col :span="24" style="text-align:left;">
                            <el-button type="primary" plain icon="el-icon-plus" @click="addUserRateLimit">添加用户限速</el-button>
                        </el-col>
                    </el-row>

                    <!-- 分组继承的速率限制 -->
                    <div class="rate-limit-header" style="margin-top: 20px;">
                        <h3>分组继承速率限制</h3>
                        <p class="rate-limit-description">以下是从分组继承的速率限制，仅在用户没有设置对应模型的专属限制时生效。</p>
                    </div>

                    <el-row v-for="(item, idx) in groupRateLimits" :key="'group-'+idx" :gutter="10" style="margin-bottom: 10px; align-items: center;">
                        <el-col :span="4">
                            <el-text>{{ item.model }}</el-text>
                        </el-col>
                        <el-col :span="5">
                            <el-text>{{ item.per }}</el-text>
                        </el-col>
                        <el-col :span="5">
                            <el-text>{{ item.limit }}</el-text>
                        </el-col>
                        <el-col :span="4">
                            <el-text>{{ item.multiplier }}</el-text>
                        </el-col>
                        <el-col :span="4">
                            <el-tag type="info">分组</el-tag>
                        </el-col>
                        <el-col :span="2" style="text-align:center;">
                            <el-button type="primary" size="small" @click="copyToUserLimit(item)">复制</el-button>
                        </el-col>
                    </el-row>
                </div>
            </el-tab-pane>
        </el-tabs>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="updateUserDialogVisible = false">取消</el-button>
                <el-button v-if="activeTab === 'basic'" type="primary" @click="submitUpdateForm">确定</el-button>
                <el-button v-else type="primary" @click="submitRateLimitsOnly">仅保存速率限制</el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 批量添加用户对话框 -->
    <el-dialog title="批量添加用户" v-model="batchAddUserDialogVisible" :width="isMobile() ? '95%' : '600px'" :close-on-click-modal="false">
        <el-form ref="batchFormRef" :model="batchForm" :rules="batchRules" :label-width="isMobile() ? 'auto' : '120px'"
            :label-position="isMobile() ? 'top' : 'right'" status-icon>

            <!-- 选择模式 -->
            <el-form-item label="设置模式" prop="mode">
                <el-radio-group v-model="batchForm.mode" @change="onModeChange">
                    <el-radio label="salesPlan">选择售卖计划</el-radio>
                    <el-radio label="manual">手动设置</el-radio>
                </el-radio-group>
            </el-form-item>

            <!-- 售卖计划模式 -->
            <el-form-item v-if="batchForm.mode === 'salesPlan'" label="售卖计划" prop="salesPlanId">
                <el-select v-model="batchForm.salesPlanId" placeholder="请选择售卖计划" style="width: 100%">
                    <el-option
                        v-for="plan in salesPlans"
                        :key="plan.id"
                        :label="`${plan.name} (${plan.validDays}天 - ${plan.membershipType})`"
                        :value="plan.id">
                    </el-option>
                </el-select>
            </el-form-item>

            <!-- 手动设置模式 -->
            <div v-if="batchForm.mode === 'manual'">
                <el-form-item label="用户组" prop="groupId">
                    <el-select v-model="batchForm.groupId" placeholder="请选择用户组" style="width: 100%">
                        <el-option
                            v-for="group in filteredUserGroups"
                            :key="group.id"
                            :label="group.title"
                            :value="group.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="有效天数" prop="validDays">
                    <el-input-number v-model="batchForm.validDays" :min="1" :max="3650" placeholder="请输入有效天数" style="width: 100%" />
                </el-form-item>
            </div>
            <el-form-item label="用户数量" prop="num">
                <el-input-number v-model="batchForm.num" :min="1" :max="100" placeholder="请输入用户数量" style="width: 100%" />
            </el-form-item>
            <el-form-item label="用户名类型" prop="userTokenType">
                <el-radio-group v-model="batchForm.userTokenType">
                    <el-radio :label="1">UUID</el-radio>
                    <el-radio :label="2">时间戳</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="用户名前缀" prop="userTokenPrefix">
                <el-input v-model="batchForm.userTokenPrefix" placeholder="可选，用户名前缀" />
            </el-form-item>
            <el-form-item label="默认密码" prop="defaultPassword">
                <el-input v-model="batchForm.defaultPassword" placeholder="可选，不填则随机生成" show-password />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
                <el-input v-model="batchForm.remark" placeholder="可选，用户备注" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="batchAddUserDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitBatchAddForm">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue';
import api from '@/axios';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Delete } from '@element-plus/icons-vue';
import { User, Lock, Message, Picture } from '@element-plus/icons-vue';
import { isMobile as isMobileUtil } from '@/utils';
import { shortcuts } from '@/utils';

const { proxy } = getCurrentInstance();
const addUserDialogVisible = ref(false);
const updateUserDialogVisible = ref(false);
const batchAddUserDialogVisible = ref(false);
const tableData = ref([]);
const userGroups = ref([]);
const salesPlans = ref([]);
const activeTab = ref('basic');
const userRateLimits = ref([]);
const groupRateLimits = ref([]);
const currentUserToken = ref('');

const filteredUserGroups = computed(() => {
    return userGroups.value.filter(group => group.id !== -1);
});

const getUserGroupName = (groupId) => {
    const group = userGroups.value.find(g => g.id === groupId);
    return group ? group.title : '-';
};

const defaultForm = {
    username: '',
    password: '',
    email: '',
    status: 0,
    groupId: '',
    expireTime: '',
    plusExpireTime: '',
    limit: null,
    enableUserTokenLogin: 0,
    remark: ''
};

const form = reactive({
    username: '',
    password: '',
    email: '',
    status: 0,
    groupId: '',
    expireTime: '',
    plusExpireTime: '',
    limit: null,
    enableUserTokenLogin: 0,
    remark: ''
});
const searchQuery = reactive({
    query: '',
    pageNum: 1,
    pageSize: 10,
    sortField: '',
    sortOrder: '',
    totalItems: 0
});
const rules = {
    username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { validator: (rule, value, callback) => {
            if (/[\u4e00-\u9fa5]/.test(value)) {
                callback(new Error('用户名不能包含中文字符'));
            } else {
                callback();
            }
        }, trigger: 'blur' }
    ],
    email: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
    ],
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 12, message: '密码长度在 6 到 12 个字符之间', trigger: 'blur' }
    ],
    groupId: [
        { required: true, message: '请选择分组', trigger: 'change' }
    ],
    expireTime: [
        { required: true, message: '请选择过期时间(普通)', trigger: 'change' }
    ],
    plusExpireTime: [
        { required: true, message: '请选择过期时间(高级)', trigger: 'change' }
    ],
    limit: [
        { type: 'number', message: '绘图积分必须为数字', trigger: 'blur' },
        { validator: (rule, value, callback) => {
            if (value === null || value === '') {
                callback();
            } else if (isNaN(value) || value < 0) {
                callback(new Error('绘图积分必须为非负数'));
            } else {
                callback();
            }
        }, trigger: 'blur' }
    ],
    remark: [
        { max: 200, message: '备注不能超过200个字符', trigger: 'blur' }
    ]
};

// 批量添加用户表单
const batchForm = reactive({
    mode: 'salesPlan', // 默认使用售卖计划模式
    salesPlanId: null,
    groupId: null,
    validDays: 30,
    num: 1,
    userTokenType: 1,
    userTokenPrefix: '',
    defaultPassword: '',
    remark: '批量添加用户'
});

// 批量添加用户验证规则
const batchRules = {
    mode: [
        { required: true, message: '请选择设置模式', trigger: 'change' }
    ],
    salesPlanId: [
        {
            validator: (rule, value, callback) => {
                if (batchForm.mode === 'salesPlan' && !value) {
                    callback(new Error('请选择售卖计划'));
                } else {
                    callback();
                }
            },
            trigger: 'change'
        }
    ],
    groupId: [
        {
            validator: (rule, value, callback) => {
                if (batchForm.mode === 'manual' && !value) {
                    callback(new Error('请选择用户组'));
                } else {
                    callback();
                }
            },
            trigger: 'change'
        }
    ],
    validDays: [
        {
            validator: (rule, value, callback) => {
                if (batchForm.mode === 'manual') {
                    if (!value || value <= 0) {
                        callback(new Error('请输入有效的天数'));
                    } else if (value > 3650) {
                        callback(new Error('有效天数不能超过10年'));
                    } else {
                        callback();
                    }
                } else {
                    callback();
                }
            },
            trigger: 'blur'
        }
    ],
    num: [
        { required: true, message: '请输入用户数量', trigger: 'blur' },
        { type: 'number', min: 1, max: 100, message: '用户数量必须在1-100之间', trigger: 'blur' }
    ],
    userTokenType: [
        { required: true, message: '请选择用户名类型', trigger: 'change' }
    ]
};

const editRules = reactive({ ...rules });
delete editRules.password;
const isMobile = () => isMobileUtil();

const fetchUserGroups = async () => {
    try {
        const res = await api.post('/api/userGroup', { pageNum: 1, pageSize: 100 });
        if (res.data.code === 0) {
            userGroups.value = res.data.data || [];
        } else {
            ElMessage.error(res.data.msg || '获取用户组失败');
        }
    } catch (error) {
        console.error('获取用户组失败:', error);
        ElMessage.error('获取用户组失败');
    }
};

// 获取售卖计划
const fetchSalesPlans = async () => {
    try {
        const res = await api.post('/api/salesPlan/page', { pageNum: 1, pageSize: 100 });
        if (res.data.code === 0) {
            salesPlans.value = res.data.data || [];
        } else {
            ElMessage.error(res.data.msg || '获取售卖计划失败');
        }
    } catch (error) {
        console.error('获取售卖计划失败:', error);
        ElMessage.error('获取售卖计划失败');
    }
};

const fetchData = async () => {
    const res = await api.post("/api/chatGptUser/pageUser", searchQuery)
    tableData.value = res.data.data
    searchQuery.totalItems = res.data.total
};
onMounted(() => {
    fetchUserGroups();
    fetchSalesPlans();
    fetchData();
});
const formatDate = (row, col, cellvalue) => {
    if (!cellvalue) return '-';
    return proxy.$dateFormat(cellvalue);
};
const handlePageChange = (page) => {
    searchQuery.pageNum = page;
    fetchData();
};
const sortCustomer = ({ prop, order }) => {
    const sortOrder = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : '';
    searchQuery.sortField = prop;
    searchQuery.sortOrder = sortOrder;
    fetchData();
};
const submitForm = async () => {
    await formRef.value.validate();
    const res = await api.post("/api/chatGptUser/addUser", form)
    if (res.data.code === 0) {
        ElMessage.success('添加成功')
        addUserDialogVisible.value = false
        fetchData()
        Object.assign(form, defaultForm)
    } else {
        ElMessage.error(res.data.msg)
    }
};

// 模式切换处理
const onModeChange = (mode) => {
    // 清空相关字段
    if (mode === 'salesPlan') {
        batchForm.groupId = null;
        batchForm.validDays = 30;
    } else {
        batchForm.salesPlanId = null;
    }
};

// 批量添加用户提交
const submitBatchAddForm = async () => {
    try {
        await proxy.$refs.batchFormRef.validate();

        // 根据模式准备提交数据
        const submitData = {
            num: batchForm.num,
            userTokenType: batchForm.userTokenType,
            userTokenPrefix: batchForm.userTokenPrefix,
            defaultPassword: batchForm.defaultPassword,
            remark: batchForm.remark
        };

        if (batchForm.mode === 'salesPlan') {
            submitData.salesPlanId = batchForm.salesPlanId;
        } else {
            submitData.groupId = batchForm.groupId;
            submitData.validDays = batchForm.validDays;
        }

        const res = await api.post("/api/chatGptUser/batchAddUser", submitData);
        if (res.data.code === 0) {
            const createdUsers = res.data.data || '';
            // 复制用户信息到剪贴板
            await navigator.clipboard.writeText(createdUsers);
            ElMessage.success('批量添加成功，用户信息已复制到剪贴板');

            batchAddUserDialogVisible.value = false;
            fetchData();
            // 重置表单
            Object.assign(batchForm, {
                mode: 'salesPlan',
                salesPlanId: null,
                groupId: null,
                validDays: 30,
                num: 1,
                userTokenType: 1,
                userTokenPrefix: '',
                defaultPassword: '',
                remark: '批量添加用户'
            });
        } else {
            ElMessage.error(res.data.msg || '批量添加失败');
        }
    } catch (error) {
        console.error('批量添加用户失败:', error);
        ElMessage.error('批量添加用户失败');
    }
};

// 仅保存用户速率限制
const submitRateLimitsOnly = async () => {
    try {
        // 转换用户速率限制格式
        const rateLimits = userRateLimits.value.map(limit => ({
            model: limit.model,
            period: limit.period,
            rate: limit.rate,
            multiplier: limit.multiplier || 1
        })).filter(limit => limit.model && limit.rate > 0); // 过滤掉无效的限制

        // 调用API保存用户速率限制
        const rateLimitRes = await api.post(`/api/userRateLimit/update?userToken=${currentUserToken.value}`, rateLimits);

        if (rateLimitRes.data.code !== 0) {
            ElMessage.error(rateLimitRes.data.msg || '保存用户速率限制失败');
            return;
        }

        ElMessage.success('速率限制设置成功');
        updateUserDialogVisible.value = false;
    } catch (error) {
        console.error('保存用户速率限制失败:', error);
        ElMessage.error('保存用户速率限制失败');
    }
};

const submitUpdateForm = async () => {
    await formRef.value.validate();

    // 保存用户基本信息
    const res = await api.post("/api/chatGptUser/addUser", form);
    if (res.data.code !== 0) {
        ElMessage.error(res.data.msg || '保存用户信息失败');
        return;
    }

    ElMessage.success('用户信息修改成功');
    updateUserDialogVisible.value = false;
    fetchData();
    Object.assign(form, defaultForm);
};
const updateUser = async (row, newVal) => {
    const res = await api.post("/api/chatGptUser/addUser", row)
    if (res.data.code === 0) {
        ElMessage.success('修改成功')
        updateUserDialogVisible.value = false
        fetchData()
        Object.assign(form, defaultForm)
    } else {
        ElMessage.error(res.data.msg)
    }
};
const deleteUser = async (row) => {
    ElMessageBox.confirm('确定删除该用户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        const res = await api.post("/api/chatGptUser/deleteUser?id=" + row.id)
        if (res.data.code === 0) {
            ElMessage.success('删除成功')
            fetchData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
};
// 用于速率限制的常量
const useLimitPers = [
    { value: '1h', label: '1小时' },
    { value: '3h', label: '3小时' },
    { value: '1d', label: '每天' },
    { value: '1w', label: '每周' }
];

let uniqueKey = 0;
const rateLimitModels = [
    { model: 'auto', label: 'auto' },
    { model: 'gpt-4o-mini', label: 'gpt-4o-mini' },
    { model: 'gpt-4o', label: 'gpt-4o' },
    { model: 'o4-mini-high', label: 'o4-mini-high' },
    { model: 'o4-mini', label: 'o4-mini' },
    { model: 'o3', label: 'o3' },
    { model: 'gpt-4-5', label: 'gpt-4-5' }
];

// 获取用户的速率限制
const fetchUserRateLimits = async (userToken) => {
    try {
        const res = await api.get('/api/userRateLimit/detail', { params: { userToken } });
        if (res.data.code === 0) {
            const limits = res.data.data.rateLimits || [];
            userRateLimits.value = limits.map(limit => ({
                model: limit.model,
                label: rateLimitModels.find(m => m.model === limit.model)?.label || limit.model,
                period: limit.per,
                rate: limit.limit,
                multiplier: limit.multiplier || 1,
                isCustom: !rateLimitModels.some(m => m.model === limit.model),
                _key: uniqueKey++
            }));
        } else {
            ElMessage.error(res.data.msg || '获取用户速率限制失败');
        }
    } catch (error) {
        console.error('获取用户速率限制失败:', error);
        ElMessage.error('获取用户速率限制失败');
    }
};

// 获取分组的速率限制
const fetchGroupRateLimits = async (groupId) => {
    try {
        const res = await api.get('/api/userGroup/detail', { params: { groupId } });
        if (res.data.code === 0) {
            groupRateLimits.value = res.data.data.rateLimits || [];
        } else {
            ElMessage.error(res.data.msg || '获取分组速率限制失败');
        }
    } catch (error) {
        console.error('获取分组速率限制失败:', error);
        ElMessage.error('获取分组速率限制失败');
    }
};

// 添加用户自定义速率限制
const addUserRateLimit = () => {
    userRateLimits.value.push({
        model: '',
        label: '',
        period: '1h',
        rate: 0,
        multiplier: 1,
        isCustom: true,
        _key: uniqueKey++
    });
};

// 删除用户自定义速率限制
const removeUserRateLimit = (item) => {
    const index = userRateLimits.value.findIndex(i => i._key === item._key);
    if (index !== -1) {
        userRateLimits.value.splice(index, 1);
    }
};

// 从分组限制复制到用户限制
const copyToUserLimit = (groupLimit) => {
    // 检查是否已存在相同模型的用户限制
    const existingIndex = userRateLimits.value.findIndex(item => item.model === groupLimit.model);

    if (existingIndex !== -1) {
        // 如果已存在，更新它
        userRateLimits.value[existingIndex].period = groupLimit.per;
        userRateLimits.value[existingIndex].rate = groupLimit.limit;
        userRateLimits.value[existingIndex].multiplier = groupLimit.multiplier || 1;
        ElMessage.success(`已更新 ${groupLimit.model} 的用户限制`);
    } else {
        // 如果不存在，添加新的
        userRateLimits.value.push({
            model: groupLimit.model,
            label: rateLimitModels.find(m => m.model === groupLimit.model)?.label || groupLimit.model,
            period: groupLimit.per,
            rate: groupLimit.limit,
            multiplier: groupLimit.multiplier || 1,
            isCustom: false,
            _key: uniqueKey++
        });
        ElMessage.success(`已添加 ${groupLimit.model} 的用户限制`);
    }
};

// 当分组变更时，重新获取分组速率限制
const handleGroupChange = async (groupId) => {
    if (groupId) {
        await fetchGroupRateLimits(groupId);
    }
};

const assignRow2Form = (row) => {
    Object.assign(form, row);
    form.username = row.userToken;
    form.password = '';
    form.groupId = row.groupId || '';
    form.enableUserTokenLogin = typeof row.enableUserTokenLogin === 'boolean' && row.enableUserTokenLogin ? 1 : 0;
    currentUserToken.value = row.userToken;

    // 重置并获取速率限制数据
    userRateLimits.value = [];
    groupRateLimits.value = [];
    activeTab.value = 'basic';

    // 获取用户和分组的速率限制
    fetchUserRateLimits(row.userToken);
    if (row.groupId) {
        fetchGroupRateLimits(row.groupId);
    }
};

const editRow = (row) => {
    updateUserDialogVisible.value = true;
    activeTab.value = 'basic';
    assignRow2Form(row);
};

const editUserRateLimit = (row) => {
    updateUserDialogVisible.value = true;
    activeTab.value = 'rateLimit';
    assignRow2Form(row);
};
const formRef = ref();
const dialogWidth = '900px';
const formLabelWidth = '120px';
</script>

<style scoped>
.pagination-container {
    padding: 10px;
}

.toolbar {
    margin-bottom: 15px;
}

.toolbar-item {
    margin-bottom: 10px;
}

.mobile-card-list {
    margin-top: 15px;
}

.mobile-card {
    margin-bottom: 15px;
}

.mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.mobile-card-title {
    font-size: 16px;
    font-weight: bold;
}

.mobile-card-content {
    margin-bottom: 15px;
}

.mobile-card-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;
}

.mobile-card-item .label {
    color: #909399;
    min-width: 90px;
}

.mobile-card-item .value {
    color: #303133;
    flex: 1;
}

.mobile-card-footer {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.mobile-card-footer .el-button {
    flex: 1;
    min-width: 80px;
}

@media screen and (max-width: 768px) {
    .pagination-container {
        padding: 5px;
    }

    .el-dialog {
        width: 95% !important;
        margin: 10px auto !important;
    }

    .el-form-item {
        margin-bottom: 15px;
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
    }

    .dialog-footer .el-button {
        flex: 1;
    }
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}

.responsive-table {
    width: 100%;
    overflow-x: auto;
}

@media screen and (max-width: 768px) {
    .el-table {
        display: none;
    }
}
</style>