<template>
    <div style="position: relative;">
        <!-- GitHub 图标链接 -->
        <div style="position: absolute; top: 0; right: 0; z-index: 10;">
            <el-tooltip content="访问 GitHub 项目" placement="left">
                <el-link href="https://github.com/xiaomifengD/chatgpt-share-server-fox-deploy" target="_blank" :underline="false">
                    <el-icon size="24" style="color: #606266; transition: color 0.3s;" @mouseenter="$event.target.style.color='#409EFF'" @mouseleave="$event.target.style.color='#606266'">
                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                            <path fill="currentColor" d="M511.6 76.3C264.3 76.2 64 276.4 64 523.5 64 718.9 189.3 885 363.8 946c23.5 5.9 19.9-10.8 19.9-22.2v-77.5c-135.7 15.9-141.2-73.9-150.3-88.9C215 726 171.5 718 184.5 703c30.9-15.9 62.4 4 98.9 57.9 26.4 39.1 77.9 32.5 104 26 5.7-23.5 17.9-44.5 34.7-60.8-140.6-25.2-199.2-111-199.2-213 0-49.5 16.3-95 48.3-131.7-20.4-60.5 1.9-112.3 4.9-120 58.1-5.2 118.5 41.6 123.2 45.3 33-8.9 70.7-13.6 112.9-13.6 42.4 0 80.2 4.9 113.5 13.9 11.3-8.6 67.3-48.8 121.3-43.9 2.9 7.7 24.7 58.3 5.5 118 32.4 36.8 48.9 82.7 48.9 132.3 0 102.2-59 188.1-200 212.9a127.5 127.5 0 0 1 38.1 91v112.5c.8 9 0 17.9 15 17.9 177.1-59.7 304.6-227 304.6-424.1 0-247.2-200.4-447.3-447.5-447.3z"/>
                        </svg>
                    </el-icon>
                </el-link>
            </el-tooltip>
        </div>

        <el-tooltip content="在这里配置系统的基本设置" placement="top">
            <el-alert title="系统配置" type="info" :closable="false"></el-alert>
        </el-tooltip>
        <el-form :model="form" label-width="auto" style="max-width: 500px;margin-top: 20px;">
        <el-form-item label="授权状态">
            <el-tag v-if="authStatus.expireTime" type="success">已授权</el-tag>
            <el-tag v-else type="danger">未授权</el-tag>
        </el-form-item>
        <el-form-item label="授权过期时间">
            <el-input v-model="authStatus.expireTime" disabled></el-input>
        </el-form-item>
        <el-form-item label="系统版本">
            <el-input v-model="authStatus.ver" disabled></el-input>
        </el-form-item>
        <el-form-item label="更新日志">
            <el-link href="https://docs.qq.com/aio/DYWt6cWVxeFZkUWNO?p=KUhSO34p1Kxvzsc4fX5zt4" target="_blank" type="primary">
                查看更新日志
            </el-link>
        </el-form-item>

        <el-form-item style="justify-content: flex-end;">
            <div style="justify-content: flex-end;">
                <el-button type="primary" @click="refreshAuth">刷新授权</el-button>
            </div>
        </el-form-item>
        </el-form>
    </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import api from '@/axios'
import { ElMessage } from 'element-plus'
const authStatus = reactive({
    expireTime: '',
    ver: ''
})
const getAuthStatus = async () => {
    const res = await api.get('/api/auth/status')
    if (res.data.code === 0) {
        authStatus.expireTime = res.data.data.expireTime
        authStatus.ver = res.data.data.ver
    }
}
const refreshAuth = async () => {
    const res = await api.get('/api/auth/refresh')
    if (res.data.code === 0) {
        ElMessage.success('刷新成功')
        getAuthStatus()
    }
}
onMounted(() => {
    getAuthStatus()
})
</script>
