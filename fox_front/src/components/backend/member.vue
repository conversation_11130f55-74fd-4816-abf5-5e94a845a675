<template>
    <el-tooltip content="在这里管理系统会员信息" placement="top">
        <el-alert title="会员管理" type="info" :closable="false"></el-alert>
    </el-tooltip>
    <el-form :model="form.member" label-width="150px" style="max-width: 600px;">
        <el-form-item label="是否开启游客模式">
            <el-switch v-model="form.member.enableVisitor" placeholder="是否开启游客模式"></el-switch>
        </el-form-item>
        <el-form-item label="是否开启注册">
            <el-switch v-model="form.member.canRegister" placeholder="输入会员名称"></el-switch>
        </el-form-item>
        <el-form-item label="注册体验时长">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-input v-model="form.member.registerUseTime" :min="0"></el-input>
                </el-col>
                <el-col :span="8">
                    <el-text size="large" tag="mark" class="unit-text">小时</el-text>

                </el-col>
            </el-row>
        </el-form-item>
        <el-form-item label="邀请赠送时长">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-input v-model="form.member.registerGiftTime" :min="0"></el-input>
                </el-col>
                <el-col :span="8">
                    <el-text size="large" tag="mark" class="unit-text">小时</el-text>

                </el-col>
            </el-row>
        </el-form-item>
        <el-form-item label="注册是否赠送PLUS">
            <el-switch v-model="form.member.registerGiftPlus" placeholder="输入会员名称"></el-switch>
        </el-form-item>
        <el-form-item label="允许多端登录">
            <el-switch v-model="form.member.canLoginMulti" placeholder="输入会员名称"></el-switch>
        </el-form-item>
        <el-form-item label="允许Token登录">
            <el-tooltip content="开启后，用户可以使用Token进行登录，需要在用户管理中单独为用户开启" placement="top">
                <el-switch v-model="form.member.enableUserTokenLogin" placeholder="是否允许Token登录"></el-switch>
            </el-tooltip>
        </el-form-item>
        <el-form-item label="登录后过期时间">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-input v-model="form.member.loginExpire" :min="0"></el-input>
                </el-col>
                <el-col :span="4">
                    <el-text size="large" tag="mark" class="unit-text">天</el-text>

                </el-col>
            </el-row>

        </el-form-item>
        <!-- 继续添加其他会员管理相关项 -->
        <el-form-item style="justify-content: flex-end;">
            <el-button type="primary" @click="submitMemberConfig">保存</el-button>
        </el-form-item>
    </el-form>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/axios'
const form = reactive({
    member: {
        enableVisitor: false,
        canRegister: true,
        registerUseTime: 0,
        registerGiftTime: 0,
        registerGiftPlus: true,
        canLoginMulti: true,
        enableUserTokenLogin: false,
        loginExpire: 0
    },
})
const submitMemberConfig = () => {
    api.post('/api/config/addOrUpdate', form.member).then(res => {
        ElMessage.success('保存成功')
    }).catch(err => {
        ElMessage.error('保存失败')
    })
}
const getMemberConfig = () => {
    api.post('/api/config/get', Object.keys(form.member)).then(res => {
        form.member = res.data.data
        form.member.enableVisitor = form.member.enableVisitor === 'true'
        form.member.canRegister = form.member.canRegister === 'true'
        form.member.registerGiftPlus = form.member.registerGiftPlus === 'true'
        form.member.canLoginMulti = form.member.canLoginMulti === 'true'
        form.member.enableUserTokenLogin = form.member.enableUserTokenLogin === 'true'
        
    }).catch(err => {
        ElMessage.error('获取失败')
    })
}
getMemberConfig()
</script>

<style scoped>
.unit-text {
    display: inline-block;
    padding: 0 8px;
    line-height: 32px;
    background-color: var(--el-color-info-light-9);
    border-radius: 4px;
    color: var(--el-text-color-primary);
}
</style>