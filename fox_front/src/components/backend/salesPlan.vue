<template>
    <div class="pagination-container" style="margin-bottom: 10px;">
        <!-- 工具栏部分 -->
        <el-row :gutter="20">
            <el-col :span="2">
                <el-button type="primary" @click="fetchData" :icon="Search">刷新</el-button>
            </el-col>
            <el-col :span="4">
                <el-button type="primary" @click="openAddSalesPlanDialog">添加</el-button>
            </el-col>

        </el-row>
        <el-table :data="tableData" border @sort-change="sortCustomer" style="margin-top: 10px;" max-height="500px"
            :header-cell-style="{ 'text-align': 'center' }" :cell-style="{ 'text-align': 'center' }"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />

            <el-table-column prop="id" label="id" width="50" />
            <el-table-column prop="name" label="订阅名称" width="100" />
            <el-table-column prop="amount" label="金额" width="100" />
            <el-table-column prop="validDays" label="有效时长" width="100" />
            <el-table-column prop="membershipType" label="会员类型" width="150">
                <template #default="scope">

                    <el-tag size="large" :type="userType[scope.row.membershipType][0]">{{
                        userType[scope.row.membershipType][1]
                    }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="isDisplayOnFront" label="前台展示" width="100">
                <template #default="scope">
                    {{ scope.row.isDisplayOnFront ? '是' : '否' }}
                </template>
            </el-table-column>
            <el-table-column prop="isHot" label="是否热门" width="100">
                <template #default="scope">
                    {{ scope.row.isHot ? '是' : '否' }}
                </template>
            </el-table-column>
            <el-table-column prop="order" label="排序字段" width="100" />

            <el-table-column prop="createdAt" label="创建时间" sortable="custom" width="150" :formatter="formatDate" />
            <el-table-column prop="updatedAt" label="修改时间" sortable="custom" width="150" :formatter="formatDate" />
            <el-table-column label="操作" width="180">
                <template #default="scope">
                    <el-button size="small" type="primary" @click="editSalesPlan(scope.row)">编辑</el-button>
                    <el-button size="small" type="danger" @click="deleteSalesPlan(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination v-model:currentPage="searchQuery.pageNum" :page-size="searchQuery.pageSize"
            :total="searchQuery.totalItems" layout="total,prev, pager, next" :page-sizes="[10, 20, 50, 100]"
            @current-change="handlePageChange" background />
    </div>
    <!-- 添加账号弹窗 -->
    <el-dialog title="添加售卖计划" v-model="addSalesPlanVisible" width="500px">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
            <el-form-item label="订阅名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入订阅名称"></el-input>
            </el-form-item>

            <el-form-item label="金额" prop="amount">
                <el-input v-model="form.amount" placeholder="请输入金额" type="number"></el-input>
            </el-form-item>

            <el-form-item label="有效时长" prop="validDays">
                <el-input v-model="form.validDays" placeholder="请输入有效时长" type="number"></el-input>
            </el-form-item>

            <el-form-item label="会员类型" prop="membershipType">
                <el-select v-model="form.membershipType" placeholder="请选择会员类型">
                    <el-option v-for="group in userGroups" :key="group.name" :label="group.title" :value="group.name" />
                </el-select>
            </el-form-item>

            <el-form-item label="前台展示" prop="isDisplayOnFront">
                <el-switch v-model="form.isDisplayOnFront"></el-switch>
            </el-form-item>

            <el-form-item label="是否热门" prop="isHot">
                <el-switch v-model="form.isHot"></el-switch>
            </el-form-item>

            <el-form-item label="标签" prop="tags">
                <el-select v-model="form.tags" placeholder="Select" multiple style="width: 240px">
                    <el-option v-for="item in tags" :key="item" :label="item" :value="item" />
                    <template #footer>
                        <el-button v-if="!isAdding" text bg size="small" @click="isAdding = true">
                            Add an option
                        </el-button>
                        <template v-else>
                            <el-input v-model="optionName" class="option-input" placeholder="请输入标签" size="small" />
                            <el-button type="primary" size="small" @click="onConfirm">
                                confirm
                            </el-button>
                            <el-button size="small" @click="clear">cancel</el-button>
                        </template>
                    </template>
                </el-select>
            </el-form-item>
            <el-form-item label="排序字段" prop="order">
                <el-input v-model="form.order" placeholder="请输入排序字段" type="number"></el-input>
            </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
            <el-button @click="addSalesPlanVisible = false">取消</el-button>
            <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
    </el-dialog>
    <el-dialog title="修改售卖计划" v-model="updateSalesPlanVisible" width="500px">
        <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
            <el-form-item label="订阅名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入订阅名称"></el-input>
            </el-form-item>

            <el-form-item label="金额" prop="amount">
                <el-input v-model="form.amount" placeholder="请输入金额" type="number"></el-input>
            </el-form-item>

            <el-form-item label="有效时长" prop="validDays">
                <el-input v-model="form.validDays" placeholder="请输入有效时长" type="number"></el-input>
            </el-form-item>

            <el-form-item label="会员类型" prop="membershipType">
                <el-select v-model="form.membershipType" placeholder="请选择会员类型">
                    <el-option v-for="group in userGroups" :key="group.name" :label="group.title" :value="group.name" />
                </el-select>
            </el-form-item>

            <el-form-item label="前台展示" prop="isDisplayOnFront">
                <el-switch v-model="form.isDisplayOnFront"></el-switch>
            </el-form-item>

            <el-form-item label="是否热门" prop="isHot">
                <el-switch v-model="form.isHot"></el-switch>
            </el-form-item>

            <el-form-item label="标签" prop="tags">
                <el-select v-model="form.tags" placeholder="Select" multiple style="width: 240px">
                    <el-option v-for="item in tags" :key="item" :label="item" :value="item" />
                    <template #footer>
                        <el-button v-if="!isAdding" text bg size="small" @click="isAdding = true">
                            Add an option
                        </el-button>
                        <template v-else>
                            <el-input v-model="optionName" class="option-input" placeholder="请输入标签" size="small" />
                            <el-button type="primary" size="small" @click="onConfirm">
                                confirm
                            </el-button>
                            <el-button size="small" @click="clear">cancel</el-button>
                        </template>
                    </template>
                </el-select>
            </el-form-item>
            <el-form-item label="排序字段" prop="order">
                <el-input v-model="form.order" placeholder="请输入排序字段" type="number"></el-input>
            </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
            <el-button @click="updateSalesPlanVisible = false">取消</el-button>
            <el-button type="primary" @click="submitUpdateForm">确定</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { ref, reactive, toRefs, computed } from 'vue';
import api from '@/axios'
import { getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
export default {
    setup() {
        const { proxy } = getCurrentInstance();
        const addSalesPlanVisible = ref(false);
        const updateSalesPlanVisible = ref(false);
        const tableData = ref([]);
        const selected = ref([])
        const handleSelectionChange = (val) => {
            selected.value = val.map(item => item.id)
            console.log(val)
        }

        const userGroups = ref([]);
        const fetchUserGroups = async () => {
            const res = await api.post("/api/userGroup", {
                pageNum: 1,
                pageSize: 100,
                sortField: '',
                sortOrder: ''
            });
            // Filter out free user groups
            userGroups.value = res.data.data.filter(group => group.name !== 'free');
        };

        const userType = computed(() => {
            const types = {};
            userGroups.value.forEach(group => {
                if (group.name !== 'free') {  // Additional check for safety
                    types[group.name] = ['success', group.title];
                }
            });
            return types;
        });

        const form = reactive({
            id: null,
            name: '',
            amount: 0,
            validDays: 30,
            membershipType: '',
            isDisplayOnFront: true,
            isHot: false,
            order: 1,
            tags: []
        });

        const defaultForm = {
            id: null,
            name: '',
            amount: 0,
            validDays: 30,
            membershipType: '',
            isDisplayOnFront: true,
            isHot: false,
            order: 1,
            tags: []
        };

        const searchQuery = ref({
            query: '',
            pageNum: 1,
            pageSize: 10,
            sortField: '',
            sortOrder: '',
            totalItems: 0
        });
        const tags = ref([
            "官网同款功能",
            "官网UI还原",
            "客服快速响应",
            "可以使用4o节点",
            "可以使用PLUS节点",
            "可以使用Claude节点"
        ])
        const isAdding = ref(false)
        const optionName = ref('')
        const onConfirm = () => {
            tags.value.push(optionName.value)
            form.tags.push(optionName.value)
            optionName.value = ''
            isAdding.value = false
        }
        const openAddSalesPlanDialog = async () => {
            // Reset form to default values including clearing the id
            Object.assign(form, defaultForm);
            addSalesPlanVisible.value = true;
        }

        const handlePageChange = (page) => {
            searchQuery.pageNum = page;
            fetchData();
        };

        const fetchData = async () => {
            await fetchUserGroups(); // Fetch user groups first
            // 模拟数据请求
            const res = await api.post("/api/salesPlan/page", searchQuery.value)
            tableData.value = res.data.data
            searchQuery.value.totalItems = res.data.total
            console.log(searchQuery.totalItems)
        };
        fetchData();
        const formatDate = (row, col, cellvalue) => {
            return proxy.$dateFormat(cellvalue)
        };
        const submitForm = async () => {
            await proxy.$refs.formRef.validate()
            const formCopy = { ...form }
            console.log(formCopy)
            formCopy.tags =  formCopy.tags.join(",")
            const res = await api.post("/api/salesPlan/addOrUpdate", formCopy)
            if (res.data.code === 0) {
                ElMessage.success('添加成功')
                addSalesPlanVisible.value = false
                fetchData()
                Object.assign(form, defaultForm)
            } else {
                ElMessage.error(res.data.msg)
            }
        }
        const submitUpdateForm = async () => {
            await proxy.$refs.formRef.validate()
            const formCopy = { ...form }
            console.log(formCopy)
            formCopy.tags = formCopy.tags.join(",")
            const res = await api.post("/api/salesPlan/addOrUpdate", formCopy)
            if (res.data.code === 0) {
                ElMessage.success('修改成功')
                updateSalesPlanVisible.value = false
                fetchData()
                Object.assign(form, defaultForm)
            } else {
                ElMessage.error(res.data.msg)
            }
        }
        const deleteSalesPlan = async (row) => {
            ElMessageBox.confirm('确定删除该售卖计划?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(async () => {
                const res = await api.post("/api/salesPlan/deleteById?id=" + row.id)
                if (res.data.code === 0) {
                    ElMessage.success('删除成功')
                    fetchData()
                } else {
                    ElMessage.error(res.data.msg)
                }

            })
        }
        const sortCustomer = ({ prop, order }) => {
            // 将排序参数传递给后端
            const sortOrder = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : '';
            console.log(prop, order)
            searchQuery.value.sortField = prop
            searchQuery.value.sortOrder = sortOrder
            fetchData()
        };
        const assignRow2Form = (row) => {
            Object.assign(form, row)
            form.tags = row.tags.split(',')
        }
        const editSalesPlan = async (row) => {
            console.log(row)
            updateSalesPlanVisible.value = true
            assignRow2Form(row)
        }
        const rules = ref({
            name: [{ required: true, message: '请输入订阅名称', trigger: 'blur' }],
            amount: [{ required: true, message: '请输入金额', trigger: 'blur' }],
            validDays: [{ required: true, message: '请输入有效时长', trigger: 'blur' }],
            membershipType: [{ required: true, message: '请选择会员类型', trigger: 'change' }],
            order: [{ required: true, message: '请输入排序字段', trigger: 'blur' }],
        })
        return {
            tableData,
            handlePageChange,
            searchQuery,
            formatDate,
            addSalesPlanVisible,
            submitForm,
            deleteSalesPlan,
            fetchData,
            sortCustomer,
            Search,
            openAddSalesPlanDialog,
            updateSalesPlanVisible,
            handleSelectionChange,
            form,
            userType,
            rules,
            tags,
            isAdding,
            optionName,
            onConfirm,
            editSalesPlan,
            submitUpdateForm,
            userGroups
        };
    },
};
</script>

<style scoped>
.pagination-container {
    padding: 20px;
}

.el-table {
    margin-bottom: 20px;
    text-align: center;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to

/* .fade-leave-active in <2.1.8 */
    {
    opacity: 0;
}
</style>