<template>
    <div class="order-container" style="margin-bottom: 10px;">
        <!-- 工具栏部分 -->
        <el-row :gutter="20">
            <el-col :span="4">
                <el-input v-model="searchQuery.query" placeholder="请输入订单号/用户名"></el-input>
            </el-col>
            <el-col :span="2">
                <el-button type="primary" @click="fetchData" :icon="Search">查询</el-button>
            </el-col>
        </el-row>

        <el-table :data="tableData" border @sort-change="sortOrders" style="margin-top: 10px;" max-height="500px"
            :header-cell-style="{ 'text-align': 'center' }" :cell-style="{ 'text-align': 'center' }">
            <el-table-column prop="id" label="ID" width="50" />
            <el-table-column prop="username" label="用户名" width="150" />
            <el-table-column prop="tradeNo" label="三方订单号" width="150" />
            <el-table-column prop="outTradeNo" label="内部订单号" width="150" />
            <el-table-column prop="amount" label="订单金额" width="100" />
            <el-table-column prop="status" label="订单状态" width="100">

                <template #default="scope">
                    <el-tag :type="scope.row.status === 0 ? 'warning' : 'success'">{{ formatStatus(scope.row,
                        scope.column, scope.row.status) }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="salesPlan" label="订阅计划" width="150" />
            <el-table-column prop="couponCode" label="优惠券码" width="150" />

            <el-table-column prop="remarks" label="备注" width="150" />
            <el-table-column prop="createdAt" label="创建时间" sortable="custom" width="150" :formatter="formatDate" />
            <el-table-column prop="updatedAt" label="修改时间" sortable="custom" width="150" :formatter="formatDate" />
            <el-table-column label="操作" width="150">
                <template #default="scope">
                    <el-button type="primary" @click="change2Success(scope.row)"
                        :disabled="scope.row.status === 1">更新为已支付</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination v-model:currentPage="searchQuery.pageNum" :page-size="searchQuery.pageSize"
            :total="searchQuery.totalItems" layout="total, prev, pager, next" :page-sizes="[10, 20, 50, 100]"
            @current-change="handlePageChange" background />
    </div>
</template>

<script>
import { ref, reactive, toRefs, getCurrentInstance } from 'vue';
import api from '@/axios';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

export default {
    setup() {
        const { proxy } = getCurrentInstance();
        const tableData = ref([]);

        const searchQuery = ref({
            query: '',
            pageNum: 1,
            pageSize: 10,
            totalItems: 0,
            sortField: 'createdAt',
            sortOrder: 'desc'
        });

        const fetchData = async () => {
            const payload = {
                ...searchQuery.value
            };
            const res = await api.post("/api/order/page", payload);
            if (res.data.code !== 0) {
                ElMessage.error(res.data.msg);
                return;
            }
            tableData.value = res.data.data;
            searchQuery.value.totalItems = res.data.total;
        };
        fetchData();

        const formatDate = (row, col, cellValue) => {
            return proxy.$dateFormat(cellValue);
        };

        const formatStatus = (row, col, cellValue) => {
            const statusMap = {
                0: '待支付',
                1: '已支付'
            };
            return statusMap[cellValue] || '未知状态';
        };

        const handlePageChange = (page) => {
            searchQuery.value.pageNum = page;
            fetchData();
        };

        const sortOrders = ({ prop, order }) => {
            // 处理排序逻辑，可以根据 prop 和 order 向后端发送请求重新获取数据
            fetchData();
        };

        const change2Success = async (row) => {
            const res = await api.get("/api/pay/changeOrder2Success?outTradeNo=" + row.outTradeNo);
            if (res.data.code !== 0) {
                ElMessage.error(res.data.msg);
                return;
            }
            ElMessage.success("更新成功");
            fetchData();
        };

        return {
            tableData,
            fetchData,
            searchQuery,
            formatDate,
            formatStatus,
            handlePageChange,
            sortOrders,
            Search,
            change2Success
        };
    },
};
</script>

<style scoped>
.order-container {
    padding: 20px;
}

.el-table {
    margin-bottom: 20px;
    text-align: center;
}
</style>
