<template>
    <div class="pagination-container" style="margin-bottom: 10px;">
        <!-- 工具栏部分 -->
        <el-row :gutter="20">
            <el-col :span="4">
                <el-input v-model="searchQuery.query" placeholder="违禁词"></el-input>
            </el-col>
            <el-col :span="2">
                <el-button type="primary" @click="fetchData" :icon="Search">查询</el-button>
            </el-col>
            <el-col :span="4">
                <el-button type="primary" @click="openAddgptDialog">添加违禁词</el-button>
            </el-col>
            <el-col :span="4">
                <el-button type="danger" @click="deleteBatch">
                    <template #default="scope">
                        <el-icon>
                            <Delete />
                        </el-icon>
                        批量删除
                    </template>
                </el-button>

            </el-col>
        </el-row>
        <el-table :data="tableData" border @sort-change="sortCustomer" style="margin-top: 10px;" max-height="500px"
            :header-cell-style="{ 'text-align': 'center' }" :cell-style="{ 'text-align': 'center' }"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />

            <el-table-column prop="id" label="id" width="50" />

            <el-table-column prop="word" label="违禁词" />


            <el-table-column prop="createdAt" label="创建时间" sortable="custom" width="150" :formatter="formatDate" />
            <el-table-column label="操作" width="180">
                <template #default="scope">
                    <el-button size="small" type="danger" @click="deleteWord(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination v-model:currentPage="searchQuery.pageNum" :page-size="searchQuery.pageSize"
            :total="searchQuery.totalItems" layout="total,prev, pager, next" :page-sizes="[10, 20, 50, 100]"
            @current-change="handlePageChange" background />
    </div>
    <!-- 添加账号弹窗 -->
    <el-dialog title="添加违禁词" v-model="addForbiddenWordVisible" max-width="600px" height="500px"
        :close-on-click-modal="false">
        <el-input v-model="form.word" placeholder="违禁词名单，每行一个" type="textarea" class="textarea-size" />
        <template #footer>
            <span>
                <el-button @click="addForbiddenWordVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script>
import { ref, reactive, toRefs } from 'vue';
import api from '@/axios'
import { getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
export default {
    setup() {
        const { proxy } = getCurrentInstance();
        const addForbiddenWordVisible = ref(false);
        const tableData = ref([]);
        const selected = ref([])
        const handleSelectionChange = (val) => {
            selected.value = val.map(item => item.id)
            console.log(val)
        }
        const deleteBatch = () => {
            if (selected.value.length === 0) {
                ElMessage.warning('请选择要删除的账号')
                return
            }

            ElMessageBox.confirm('是否批量删除选中的账号', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await api.post("/api/forbiddenWord/deleteByIds", selected.value)
                if (res.data.code === 0) {
                    ElMessage.success('删除成功')
                    fetchData()

                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        }

        const form = reactive({
            word: ''
        });



        const searchQuery = ref({
            query: '',
            pageNum: 1,
            pageSize: 10,
            sortField: '',
            sortOrder: '',
            totalItems: 0
        });
        const openAddgptDialog = async () => {
            addForbiddenWordVisible.value = true
        }

        const handlePageChange = (page) => {
            searchQuery.pageNum = page;
            fetchData();
        };

        const fetchData = async () => {

            // 模拟数据请求
            const res = await api.post("/api/forbiddenWord/page", searchQuery.value)
            tableData.value = res.data.data
            searchQuery.value.totalItems = res.data.total
            console.log(searchQuery.totalItems)
        };
        fetchData();
        const formatDate = (row, col, cellvalue) => {
            return proxy.$dateFormat(cellvalue)
        };
        const submitForm = async () => {
            if (!form.word) {
                ElMessage.warning('请输入违禁词')
                return
            }
            let config = {
                headers: {
                    'Content-Type': 'text/plain'
                }
            }
            const res = await api.post("/api/forbiddenWord/batchAdd", form.word, config)
            if (res.data.code === 0) {
                ElMessage.success('添加成功')
                addForbiddenWordVisible.value = false
                fetchData()
                Object.assign(form, defaultForm)
            } else {
                ElMessage.error(res.data.msg)
            }
        }

        const deleteWord = async (row) => {
            ElMessageBox.confirm('确定删除该违禁词?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(async () => {
                const res = await api.post("/api/forbiddenWord/deleteByIds", [row.id])
                if (res.data.code === 0) {
                    ElMessage.success('删除成功')
                    fetchData()
                } else {
                    ElMessage.error(res.data.msg)
                }

            })
        }
        const sortCustomer = ({ prop, order }) => {
            // 将排序参数传递给后端
            const sortOrder = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : '';
            console.log(prop, order)
            searchQuery.value.sortField = prop
            searchQuery.value.sortOrder = sortOrder
            fetchData()
        };
        const assignRow2Form = (row) => {
            Object.assign(form, row)
        }
        const editRow = async (row) => {
            console.log(row)
            updateGptDialogVisible.value = true
            assignRow2Form(row)
        }
        return {
            tableData,
            handlePageChange,
            searchQuery,
            formatDate,
            addForbiddenWordVisible,
            submitForm,
            deleteWord,
            fetchData,
            sortCustomer,
            Search,
            openAddgptDialog,
            handleSelectionChange,
            deleteBatch,
            form
        };
    },
};
</script>

<style scoped>
.pagination-container {
    padding: 20px;
}

.el-table {
    margin-bottom: 20px;
    text-align: center;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to

/* .fade-leave-active in <2.1.8 */
    {
    opacity: 0;
}
</style>