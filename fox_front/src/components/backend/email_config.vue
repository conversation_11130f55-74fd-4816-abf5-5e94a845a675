<template>
    <el-form :model="form.member" label-width="auto" style="max-width: 500px;">
        <el-alert title="发送邮件配置" type="info" :closable="false" style="margin-top: 20px;"></el-alert>
        <el-form-item label="SMTP服务器地址">
            <el-input v-model="form.email.smtpHost" placeholder="填写SMTP服务器地址"></el-input>
        </el-form-item>
        <el-form-item label="SMTP服务器端口">
            <el-radio-group title="邮件端口" v-model="form.email.smtpPort">
                <!-- works when >=2.6.0, recommended ✔️ not work when <2.6.0 ❌ -->
                <el-radio value="465">465（SSL）</el-radio>
                <!-- works when <2.6.0, deprecated act as value when >=3.0.0 -->
                <el-radio label="578">578（TLS）</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="发件人邮箱">
            <el-input v-model="form.email.senderEmail" placeholder="填写发件人邮箱"></el-input>
        </el-form-item>
        <el-form-item label="smtp授权码">
            <el-input v-model="form.email.emailPassword" placeholder="填写smtp授权码"></el-input>
        </el-form-item>
        <el-form-item label="邮箱白名单">
            <el-select v-model="form.email.emailWhiteList" multiple filterable allow-create default-first-option
                placeholder="请输入邮箱地址">
                <el-option v-for="email in form.email.emailWhiteList" :key="email" :label="email" :value="email" />
            </el-select>
        </el-form-item>
        <el-form-item style="justify-content: flex-end;">
            <el-button type="primary" @click="saveEmailConfig">保存</el-button>
            <el-button type="primary" @click="testEmailSend">测试邮件发送</el-button>
        </el-form-item>
    </el-form>
</template>
<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/axios'
const form = reactive({
    email: {
        smtpHost: '',
        smtpPort: '',
        senderEmail: '',
        emailPassword: '',
        emailWhiteList: [],
        // 其他会员管理相关项
    },
})
const getEmailConfig = () => {
    api.post('/api/config/get', ['smtpHost', 'smtpPort', 'senderEmail', 'emailPassword', 'emailWhiteList']).then(res => {
        const data = res.data.data
        form.email.smtpHost = data.smtpHost
        form.email.smtpPort = data.smtpPort
        form.email.senderEmail = data.senderEmail
        form.email.emailPassword = data.emailPassword
        form.email.emailWhiteList = data.emailWhiteList && data.emailWhiteList.split(',')
    })
}

getEmailConfig()
console.log(form)
const addEmail = (email) => {
    if (email && !form.emailWhiteList.includes(email)) {
        form.emailWhiteList.push(email);
    }
};

const removeEmail = (index) => {
    form.emailWhiteList.splice(index, 1);
};
const saveEmailConfig = () => {
    const configCopy = { ...form.email }
    configCopy.emailWhiteList = configCopy.emailWhiteList && configCopy.emailWhiteList.join(',')
    api.post('/api/config/addOrUpdate', configCopy).then(res => {
        ElMessage.success('保存成功')
    }).catch(err => {
        ElMessage.error('保存失败')
    })
}
const testEmailSend = () => {
    api.post('/api/chatGptUser/testEmailSend').then(res => {
        ElMessage.success('发送成功')
    }).catch(err => {
        ElMessage.error('发送失败')
    })
}
</script>