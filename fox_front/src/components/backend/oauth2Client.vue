<template>
    <div class="pagination-container">
        <!-- 工具栏部分 -->
        <el-row :gutter="10" class="toolbar">
            <el-col :xs="24" :sm="8" :md="6" :lg="4" class="toolbar-item">
                <el-input v-model="searchQuery.query" placeholder="请输入客户端ID/类型"></el-input>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="primary" @click="fetchData" :icon="Search">查询</el-button>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="primary" @click="showAddDialog">添加客户端</el-button>
            </el-col>
        </el-row>

        <!-- PC端表格 -->
        <div v-if="!isMobile()" class="table-container">
            <el-table :data="tableData" border @sort-change="sortCustomer" style="width: 100%;margin-top: 10px;"
                :max-height="tableMaxHeight" :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }" class="responsive-table">
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="clientId" label="客户端ID" width="200" />
                <el-table-column prop="type" label="类型" width="120" />
                <el-table-column prop="redirectUri" label="重定向URI" width="250" />
                <el-table-column prop="scope" label="权限范围" width="200" />
                <el-table-column prop="createTime" label="创建时间" sortable="custom" width="180" :formatter="formatDate" />
                <el-table-column prop="updateTime" label="更新时间" width="180" :formatter="formatDate" />
                <el-table-column fixed="right" label="操作" width="200">
                    <template #default="scope">
                        <el-button size="small" type="primary" @click="editRow(scope.row)">编辑</el-button>
                        <el-button size="small" type="danger" @click="deleteClient(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 移动端卡片列表 -->
        <div v-else class="mobile-card-list">
            <el-card v-for="item in tableData" :key="item.id" class="mobile-card" shadow="hover">
                <div class="mobile-card-header">
                    <span class="mobile-card-title">客户端: {{ item.clientId }}</span>
                </div>
                <div class="mobile-card-content">
                    <div class="mobile-card-item">
                        <span class="label">类型:</span>
                        <span class="value">{{ item.type }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">重定向URI:</span>
                        <span class="value">{{ item.redirectUri }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">权限范围:</span>
                        <span class="value">{{ item.scope }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">创建时间:</span>
                        <span class="value">{{ formatDate(item, null, item.createTime) }}</span>
                    </div>
                </div>
                <div class="mobile-card-footer">
                    <el-button size="small" type="primary" @click="editRow(item)">编辑</el-button>
                    <el-button size="small" type="danger" @click="deleteClient(item)">删除</el-button>
                </div>
            </el-card>
        </div>

        <!-- 分页器 -->
        <el-pagination v-model:currentPage="searchQuery.pageNum" :page-size="searchQuery.pageSize"
            :total="searchQuery.totalItems" :layout="isMobile() ? 'prev, pager, next' : 'total, prev, pager, next'"
            :page-sizes="[10, 20, 50, 100]" @current-change="handlePageChange" background />
    </div>

    <!-- 添加/编辑客户端弹窗 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" :width="isMobile() ? '95%' : '600px'" :close-on-click-modal="false">
        <el-form ref="formRef" :model="form" :rules="rules" :label-width="isMobile() ? 'auto' : '120px'"
            :label-position="isMobile() ? 'top' : 'right'" status-icon>
            <el-form-item label="客户端ID" prop="clientId">
                <el-input v-model="form.clientId" placeholder="请输入客户端ID"></el-input>
            </el-form-item>
            <el-form-item label="客户端密钥" prop="clientSecret">
                <el-input v-model="form.clientSecret"  placeholder="请输入客户端密钥" ></el-input>
            </el-form-item>
            <el-form-item label="类型" prop="type">
                <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%" @change="handleTypeChange">
                    <el-option label="GitHub" value="github" />
                    <el-option label="Google" value="google" />
                </el-select>
            </el-form-item>
            <el-form-item label="重定向URI" prop="redirectUri">
                <el-input v-model="form.redirectUri" placeholder="请输入重定向URI"></el-input>
            </el-form-item>
            <el-form-item label="权限范围" prop="scope">
                <el-input v-model="form.scope" placeholder="请输入权限范围，多个用空格分隔"></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { isMobile as isMobileUtil } from '@/utils';
import api from '@/axios';
import { getCurrentInstance } from 'vue';

const { proxy } = getCurrentInstance();

// 表格数据
const tableData = ref([]);
const dialogVisible = ref(false);
const dialogTitle = ref('添加客户端');
const isEdit = ref(false);

// 查询参数
const searchQuery = reactive({
    query: '',
    pageNum: 1,
    pageSize: 10,
    sortField: '',
    sortOrder: '',
    totalItems: 0
});

// 表单数据
const defaultForm = {
    clientId: '',
    clientSecret: '',
    type: '',
    redirectUri: '',
    scope: ''
};

const form = reactive({ ...defaultForm });

// 表单验证规则
const rules = {
    clientId: [
        { required: true, message: '请输入客户端ID', trigger: 'blur' },
        { min: 3, max: 100, message: '长度在 3 到 100 个字符', trigger: 'blur' }
    ],
    clientSecret: [
        { required: true, message: '请输入客户端密钥', trigger: 'blur' },
        { min: 6, max: 100, message: '长度在 6 到 100 个字符', trigger: 'blur' }
    ],
    type: [
        { required: true, message: '请选择类型', trigger: 'change' }
    ],
    redirectUri: [
        { required: true, message: '请输入重定向URI', trigger: 'blur' },
        { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
    ],
    scope: [
        { required: true, message: '请输入权限范围', trigger: 'blur' }
    ]
};

// 工具函数
const isMobile = () => isMobileUtil();
const formRef = ref();

// 处理类型变更
const handleTypeChange = (type) => {
    if (type === 'github') {
        // GitHub默认权限设置
        form.scope = 'read:user user:email';
    } else if (type === 'google') {
        // Google默认权限设置
        form.scope = 'openid email profile';
    } else {
        form.scope = '';
    }
};

// 格式化日期
const formatDate = (row, col, cellvalue) => {
    if (!cellvalue) return '-';
    return proxy.$dateFormat(cellvalue);
};

// 获取数据
const fetchData = async () => {
    try {
        const res = await api.post('/api/oauth2Client/page', searchQuery);
        if (res.data.code === 0) {
            tableData.value = res.data.data;
            searchQuery.totalItems = res.data.total;
        } else {
            ElMessage.error(res.data.msg || '获取数据失败');
        }
    } catch (error) {
        console.error('获取数据失败:', error);
        ElMessage.error('获取数据失败');
    }
};

// 分页处理
const handlePageChange = (page) => {
    searchQuery.pageNum = page;
    fetchData();
};

// 排序处理
const sortCustomer = ({ prop, order }) => {
    searchQuery.sortField = prop;
    searchQuery.sortOrder = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : '';
    fetchData();
};

// 显示添加对话框
const showAddDialog = () => {
    dialogTitle.value = '添加客户端';
    isEdit.value = false;
    Object.assign(form, defaultForm);
    dialogVisible.value = true;
};

// 编辑行
const editRow = (row) => {
    dialogTitle.value = '编辑客户端';
    isEdit.value = true;
    Object.assign(form, row);
    dialogVisible.value = true;
};

// 删除客户端
const deleteClient = async (row) => {
    try {
        await ElMessageBox.confirm('确定要删除该客户端吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        const res = await api.post('/api/oauth2Client/deleteByIds', [row.id]);
        if (res.data.code === 0) {
            ElMessage.success('删除成功');
            fetchData();
        } else {
            ElMessage.error(res.data.msg || '删除失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除失败:', error);
            ElMessage.error('删除失败');
        }
    }
};

// 提交表单
const submitForm = async () => {
    try {
        await formRef.value.validate();
        const res = await api.post('/api/oauth2Client/addOrUpdate', form);
        if (res.data.code === 0) {
            ElMessage.success(isEdit.value ? '更新成功' : '添加成功');
            dialogVisible.value = false;
            fetchData();
            Object.assign(form, defaultForm);
        } else {
            ElMessage.error(res.data.msg || (isEdit.value ? '更新失败' : '添加失败'));
        }
    } catch (error) {
        console.error(isEdit.value ? '更新失败:' : '添加失败:', error);
        ElMessage.error(isEdit.value ? '更新失败' : '添加失败');
    }
};

// 初始化
onMounted(() => {
    fetchData();
});
</script>

<style scoped>
.pagination-container {
    padding: 10px;
}

.toolbar {
    margin-bottom: 15px;
}

.toolbar-item {
    margin-bottom: 10px;
}

.mobile-card-list {
    margin-top: 15px;
}

.mobile-card {
    margin-bottom: 15px;
}

.mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.mobile-card-title {
    font-size: 16px;
    font-weight: bold;
}

.mobile-card-content {
    margin-bottom: 15px;
}

.mobile-card-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;
}

.mobile-card-item .label {
    color: #909399;
    min-width: 90px;
}

.mobile-card-item .value {
    color: #303133;
    flex: 1;
}

.mobile-card-footer {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.mobile-card-footer .el-button {
    flex: 1;
    min-width: 80px;
}

@media screen and (max-width: 768px) {
    .pagination-container {
        padding: 5px;
    }

    .el-dialog {
        width: 95% !important;
        margin: 10px auto !important;
    }

    .el-form-item {
        margin-bottom: 15px;
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
    }

    .dialog-footer .el-button {
        flex: 1;
    }

    .el-table {
        display: none;
    }
}

.responsive-table {
    width: 100%;
    overflow-x: auto;
}
</style>
