<template>
    <div class="pagination-container">
        <!-- 移动端表格容器 -->
        <div class="works-grid" v-if="isMobile">
            <div v-for="item in tableData" :key="item.id" class="work-card">
                <div class="image-wrapper">
                    <template v-if="item.isSuccess">
                        <div class="image-container">
                            <div class="source-image" v-if="item.sourceImageUrl">
                                <el-image 
                                    :src="item.sourceImageUrl"
                                    class="work-image"
                                    fit="cover"
                                    :preview-src-list="[]"
                                    @click="showPreview(item.sourceImageUrl)"
                                >
                                    <template #error>
                                        <div class="image-error">
                                            <el-icon><picture-filled /></el-icon>
                                            <span>原图加载失败</span>
                                        </div>
                                    </template>
                                </el-image>
                                <div class="image-label">原图</div>
                            </div>
                            <div class="generated-image">
                                <el-image 
                                    v-if="item.imageUrl"
                                    :src="item.imageUrl" 
                                    class="work-image"
                                    fit="cover"
                                    :preview-src-list="[]"
                                    @click="showPreview(item.imageUrl)"
                                >
                                    <template #error>
                                        <div class="image-error">
                                            <el-icon><picture-filled /></el-icon>
                                            <span>结果加载失败</span>
                                        </div>
                                    </template>
                                </el-image>
                                <div class="image-label">生成结果</div>
                            </div>
                            <!-- <div class="user-token" v-if="item.userToken">用户: {{ item.userToken }}</div> -->
                        </div>
                    </template>
                    <template v-else>
                        <div class="error-container">
                            <div class="error-content">
                                <el-icon class="error-icon"><warning-filled /></el-icon>
                                <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    :content="item.errorMessage || '生成失败'"
                                    placement="top"
                                    :show-after="200"
                                >
                                    <div class="error-message-trigger">
                                        {{ item.errorMessage ? (item.errorMessage.length > 30 ? item.errorMessage.slice(0, 30) + '...' : item.errorMessage) : '生成失败' }}
                                    </div>
                                </el-tooltip>
                            </div>
                        </div>
                    </template>
                    <div class="card-content">
                        <div class="card-header">
                            <el-tag size="small" :type="item.isSuccess ? 'success' : 'danger'">
                                {{ item.isSuccess ? '成功' : '失败' }}
                            </el-tag>
                            <span class="user-name" v-if="item.userToken">{{ item.userToken }}</span>
                        </div>
                        <el-tooltip
                            class="box-item"
                            effect="dark"
                            :content="item.prompt"
                            placement="top"
                            :show-after="200"
                        >
                            <div class="card-prompt">{{ item.prompt.length > 50 ? item.prompt.slice(0, 50) + '...' : item.prompt }}</div>
                        </el-tooltip>
                        <div class="card-footer">
                            <span class="card-time">{{ formatDate(null, null, item.createdAt) }}</span>
                            <el-button size="small" type="primary" @click="downloadImage(item)" v-if="item.imageUrl">下载</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- PC端表格容器 -->
        <div class="works-grid" v-if="!isMobile">
            <div v-for="item in tableData" :key="item.id" class="work-card">
                <div class="image-wrapper">
                    <template v-if="item.isSuccess">
                        <div class="image-container">
                            <div class="source-image" v-if="item.sourceImageUrl">
                                <el-image 
                                    :src="item.sourceImageUrl"
                                    class="work-image"
                                    fit="cover"
                                    :preview-src-list="[]"
                                    @click="showPreview(item.sourceImageUrl)"
                                >
                                    <template #error>
                                        <div class="image-error">
                                            <el-icon><picture-filled /></el-icon>
                                            <span>原图加载失败</span>
                                        </div>
                                    </template>
                                </el-image>
                                <div class="image-label">原图</div>
                            </div>
                            <div class="generated-image">
                                <el-image 
                                    v-if="item.imageUrl"
                                    :src="item.imageUrl" 
                                    class="work-image"
                                    fit="cover"
                                    :preview-src-list="[]"
                                    @click="showPreview(item.imageUrl)"
                                >
                                    <template #error>
                                        <div class="image-error">
                                            <el-icon><picture-filled /></el-icon>
                                            <span>结果加载失败</span>
                                        </div>
                                    </template>
                                </el-image>
                                <div class="image-label">生成结果</div>
                            </div>
                            <!-- <div class="user-token" v-if="item.userToken">用户: {{ item.userToken }}</div> -->
                        </div>
                    </template>
                    <template v-else>
                        <div class="error-container">
                            <div class="error-content">
                                <el-icon class="error-icon"><warning-filled /></el-icon>
                                <el-tooltip
                                    class="box-item"
                                    effect="dark"
                                    :content="item.errorMessage || '生成失败'"
                                    placement="top"
                                    :show-after="200"
                                >
                                    <div class="error-message-trigger">
                                        {{ item.errorMessage ? (item.errorMessage.length > 30 ? item.errorMessage.slice(0, 30) + '...' : item.errorMessage) : '生成失败' }}
                                    </div>
                                </el-tooltip>
                            </div>
                        </div>
                    </template>
                    <div class="card-content">
                        <div class="card-header">
                            <el-tag size="small" :type="item.isSuccess ? 'success' : 'danger'">
                                {{ item.isSuccess ? '成功' : '失败' }}
                            </el-tag>
                            <span class="user-name" v-if="item.userToken">{{ item.userToken }}</span>
                        </div>
                        <el-tooltip
                            class="box-item"
                            effect="dark"
                            :content="item.prompt"
                            placement="top"
                            :show-after="200"
                        >
                            <div class="card-prompt">{{ item.prompt.length > 50 ? item.prompt.slice(0, 50) + '...' : item.prompt }}</div>
                        </el-tooltip>
                        <div class="card-footer">
                            <span class="card-time">{{ formatDate(null, null, item.createdAt) }}</span>
                            <el-button size="small" type="primary" @click="downloadImage(item)" v-if="item.imageUrl">下载</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图片预览组件 -->
        <el-image-viewer
            v-if="previewVisible"
            :url-list="previewImages"
            :initial-index="0"
            @close="previewVisible = false"
            :hide-on-click-modal="true"
            :zoom-rate="1.2"
            :min-scale="0.2"
            :max-scale="7"
        />

        <el-pagination 
            v-model:currentPage="searchQuery.pageNum" 
            :page-size="searchQuery.pageSize"
            :total="searchQuery.totalItems" 
            layout="total, prev, pager, next" 
            :page-sizes="[10, 20, 50, 100]"
            @current-change="handlePageChange" 
            background 
            class="pagination"
        />
    </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { PictureFilled, WarningFilled } from '@element-plus/icons-vue'
import { ElImageViewer } from 'element-plus'
import api from '@/axios'
import { getCurrentInstance } from 'vue';
import { ElMessage } from 'element-plus';

export default {
    components: {
        PictureFilled,
        WarningFilled,
        ElImageViewer,
    },
    setup() {
        const { proxy } = getCurrentInstance();
        const tableData = ref([]);
        const searchQuery = ref({
            query: '',
            pageNum: 1,
            pageSize: 10,
            sortField: '',
            sortOrder: '',
            totalItems: 0
        });

        // 预览图片列表
        const previewImages = ref([]);
        const previewVisible = ref(false);

        // 显示预览
        const showPreview = (imageUrl) => {
            if (!imageUrl) return;
            previewImages.value = [imageUrl];
            previewVisible.value = true;
        };

        // 获取图片URL
        const getImageUrl = (imageId) => {
            if (!imageId) return '';
            const baseUrl = import.meta.env.MODE === 'production' ? window.location.origin : import.meta.env.VITE_API_HOST;
            return `${baseUrl}/api/download-image/${imageId}`;
        };

        const handlePageChange = (page) => {
            searchQuery.value.pageNum = page;
            fetchData();
        };

        const fetchData = async () => {
            try {
                const res = await api.post("/api/image-generation-records/pageAll", searchQuery.value);
                if (res.data.code === 0) {
                    // 处理图片数据
                    tableData.value = res.data.data.map(item => {
                        // 解析生成的图片
                        let generatedImageId = null;
                        try {
                            const generatedImages = JSON.parse(item.generatedImages || '[]');
                            generatedImageId = generatedImages[0];
                        } catch (e) {
                            console.error('Failed to parse generatedImages:', e);
                        }

                        // 解析源图片
                        let sourceImageId = null;
                        try {
                            const sourceImages = JSON.parse(item.sourceImageFiles || '[]');
                            sourceImageId = sourceImages[0];
                        } catch (e) {
                            console.error('Failed to parse sourceImageFiles:', e);
                        }

                        return {
                            ...item,
                            imageUrl: generatedImageId ? getImageUrl(generatedImageId) : null,
                            sourceImageUrl: sourceImageId ? getImageUrl(sourceImageId) : null,
                            // 使用 successful 字段作为最终状态判断
                            isSuccess: item.successful
                        };
                    });
                    searchQuery.value.totalItems = res.data.total;
                } else {
                    ElMessage.error(res.data.msg || '获取数据失败');
                }
            } catch (error) {
                console.error('Failed to fetch data:', error);
                ElMessage.error('获取数据失败');
            }
        };

        const formatDate = (row, col, cellvalue) => {
            return proxy.$dateFormat(cellvalue);
        };

        const downloadImage = async (row) => {
            if (!row.imageUrl) {
                ElMessage.warning('没有可下载的图片');
                return;
            }
            
            try {
                // 从 imageUrl 中提取 ID
                const imageId = row.imageUrl.split('/').pop();
                if (!imageId) {
                    ElMessage.error('无效的图片ID');
                    return;
                }

                const response = await api.get(`/api/download-image/${imageId}`, {
                    responseType: 'blob'
                });
                
                // 检查响应类型
                if (!response.data || !(response.data instanceof Blob)) {
                    ElMessage.error('下载失败：无效的响应数据');
                    return;
                }

                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                
                // 使用更有意义的文件名
                const timestamp = new Date().getTime();
                const fileExtension = getImageExtension(response.headers['content-type']);
                link.download = `generated-image-${timestamp}${fileExtension}`;
                
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
                
                ElMessage.success('下载成功');
            } catch (error) {
                console.error('Download failed:', error);
                ElMessage.error('下载失败：' + (error.message || '未知错误'));
            }
        };

        // 根据 content-type 获取文件扩展名
        const getImageExtension = (contentType) => {
            const extensionMap = {
                'image/jpeg': '.jpg',
                'image/png': '.png',
                'image/gif': '.gif',
                'image/webp': '.webp'
            };
            return extensionMap[contentType] || '.png';
        };

        const isMobile = ref(window.innerWidth <= 768);
        
        onMounted(() => {
            fetchData();
            window.addEventListener('resize', () => {
                isMobile.value = window.innerWidth <= 768;
            });
        });

        onUnmounted(() => {
            window.removeEventListener('resize', () => {
                isMobile.value = window.innerWidth <= 768;
            });
        });

        return {
            tableData,
            handlePageChange,
            searchQuery,
            formatDate,
            downloadImage,
            isMobile,
            getImageUrl,
            previewImages,
            previewVisible,
            showPreview,
        };
    },
};
</script>

<style scoped>
.pagination-container {
    padding: 20px;
    min-height: calc(100vh - 200px);
}

.works-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.work-card {
    background: rgba(255, 255, 255, 0.85);
    border-radius: 12px;
    box-shadow: 0 6px 24px rgba(147, 112, 219, 0.1);
    overflow: hidden;
    transition: box-shadow 0.3s, transform 0.3s;
}

.work-card:hover {
    box-shadow: 0 12px 32px rgba(147, 112, 219, 0.18);
    transform: translateY(-4px) scale(1.02);
}

.image-wrapper {
    position: relative;
    width: 100%;
}

.image-container {
    display: flex;
    gap: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
    position: relative;
}

.source-image,
.generated-image {
    flex: 1;
    position: relative;
}

.work-image {
    width: 100%;
    height: 160px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s;
}

.work-image:hover {
    transform: scale(1.02);
}

.image-label {
    position: absolute;
    top: 8px;
    left: 8px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.user-token {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1;
}

.card-content {
    padding: 1rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.user-name {
    font-size: 14px;
    color: #606266;
    margin-left: 8px;
}

.error-container {
    width: 100%;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

.error-content {
    background: #fdf6ec;
    width: 100%;
    height: 160px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    border-radius: 4px;
    gap: 16px;
    box-sizing: border-box;
}

.error-icon {
    font-size: 32px;
    color: #e6a23c;
    flex-shrink: 0;
}

.error-message-trigger {
    color: #e6a23c;
    font-size: 14px;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 4px;
    background: rgba(230, 162, 60, 0.1);
    transition: all 0.3s;
    text-align: center;
    max-width: 90%;
    word-break: break-word;
    line-height: 1.4;
}

.error-message-trigger:hover {
    background: rgba(230, 162, 60, 0.2);
    transform: translateY(-1px);
}

.error-message {
    color: #b88230;
    font-size: 14px;
    text-align: center;
    max-width: 100%;
    word-break: break-word;
    line-height: 1.6;
    overflow-y: auto;
    max-height: 80px;
    padding: 0 10px;
    margin-bottom: 8px;
}

.error-user {
    color: #909399;
    font-size: 12px;
    margin-top: 8px;
    background: rgba(0, 0, 0, 0.05);
    padding: 2px 8px;
    border-radius: 4px;
}

.card-prompt {
    font-size: 0.95rem;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.4;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    background: rgba(64, 158, 255, 0.1);
    transition: background-color 0.3s;
}

.card-prompt:hover {
    background: rgba(64, 158, 255, 0.2);
}

:deep(.el-tooltip__trigger) {
    cursor: pointer;
    width: 100%;
}

:deep(.el-tooltip__popper) {
    max-width: 400px;
    line-height: 1.6;
    padding: 8px 12px;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-time {
    font-size: 0.85rem;
    color: #999;
}

.pagination {
    text-align: center;
    margin-top: 2rem;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .works-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1rem;
    }

    .pagination-container {
        padding: 10px;
    }

    .work-image {
        height: 140px;
    }

    .error-container {
        min-height: 140px;
        margin: 5px;
    }

    .error-message {
        max-height: 80px;
    }

    .user-name {
        font-size: 12px;
    }
    
    .error-message-trigger {
        font-size: 13px;
        padding: 4px 8px;
    }

    .error-content {
        height: 140px;
        padding: 1rem;
        gap: 12px;
    }

    .card-prompt {
        font-size: 0.85rem;
        margin-bottom: 0.8rem;
    }
    
    :deep(.el-tooltip__popper) {
        max-width: 300px;
        font-size: 13px;
    }
}

.image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #909399;
    font-size: 14px;
}

.image-error .el-icon {
    font-size: 32px;
    margin-bottom: 8px;
}

/* 确保预览组件在最上层 */
:deep(.el-image-viewer__wrapper) {
    z-index: 2100;
}

:deep(.el-image-viewer__close) {
    color: #fff;
}

:deep(.el-image-viewer__actions) {
    opacity: 1;
    background: rgba(0, 0, 0, 0.7);
}

:deep(.el-image-viewer__canvas) {
    user-select: none;
}

:deep(.el-image-viewer__btn) {
    color: #fff;
}
</style> 