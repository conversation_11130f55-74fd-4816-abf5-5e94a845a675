<template>
    <el-tooltip content="在这里配置推广返现相关设置" placement="top">
        <el-alert title="推广返现配置" type="info" :closable="false"></el-alert>
    </el-tooltip>
    <el-form :model="form.promotion" label-width="auto" style="max-width: 500px;margin-top: 20px;">
        <el-form-item label="开启推广返现">
            <el-switch 
                v-model="form.promotion.enablePromotion" 
                active-text="开启后，用户可以通过推广获得返现">
            </el-switch>
        </el-form-item>

        <el-form-item label="返现比例">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-input
                        v-model="form.promotion.cashbackRate" 
                        type="number"
                        placeholder="请输入返现比例">
                    </el-input>
                </el-col>
            </el-row>
            <el-text type="info" size="small">推广用户购买金额的返现比例</el-text>
        </el-form-item>

        <el-form-item label="提现门槛">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-input 
                        v-model="form.promotion.withdrawalThreshold" 
                        placeholder="请输入提现门槛金额"
                        type="number"
                        >
                    </el-input>
                </el-col>
                <el-col :span="8">
                    <el-text size="large" tag="mark">元</el-text>
                </el-col>
            </el-row>
            <el-text type="info" size="small">用户可提现的最低金额</el-text>
        </el-form-item>

        <el-form-item style="justify-content: flex-end;">
            <div style="justify-content: flex-end;">
                <el-button type="primary" @click="submitPromotionConfig">保存</el-button>
            </div>
        </el-form-item>
    </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import api from '@/axios'
import { ElMessage } from 'element-plus'

const form = reactive({
    promotion: {
        enablePromotion: false,
        cashbackRate: 0.2,
        withdrawalThreshold: 20
    }
})

const submitPromotionConfig = () => {
    const submitData = { ...form.promotion }
    api.post('/api/config/addOrUpdate', submitData).then(res => {
        ElMessage.success('保存成功')
    }).catch(err => {
        ElMessage.error('保存失败')
    })
}

const getPromotionConfig = () => {
    api.post('/api/config/get', Object.keys(form.promotion)).then(res => {
        if (res.data.data) {
            form.promotion = {
                ...form.promotion,
                ...res.data.data
            }
            // Convert string to boolean if needed
            form.promotion.enablePromotion = form.promotion.enablePromotion === 'true' || form.promotion.enablePromotion === true
            // Convert string to number if needed
            form.promotion.cashbackRate = Number(form.promotion.cashbackRate) || 0.2
            form.promotion.withdrawalThreshold = Number(form.promotion.withdrawalThreshold) || 20
        }
    }).catch(err => {
        ElMessage.error('获取推广配置失败')
    })
}

// 初始化获取配置
getPromotionConfig()
</script>

<style scoped>
.el-input-number {
    width: 100%;
}
</style>
