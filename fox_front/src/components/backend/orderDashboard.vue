<template>
    <div class="order-dashboard">
      <!-- 订单趋势卡片 -->
      <el-card class="box-card order-trend">
        <template #header>
          <div class="card-header">
            <span>订单趋势</span>
            <div class="period-selector">
              <el-radio-group v-model="trendPeriod" size="small" @change="fetchOrderTrend">
                <el-radio-button :label="7">7天</el-radio-button>
                <el-radio-button :label="30">30天</el-radio-button>
                <el-radio-button :label="90">90天</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>
        <div class="trend-summary">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="summary-item">
                <div class="label">总订单数</div>
                <div class="value">{{ orderTrendData.totalOrders || 0 }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="label">已支付订单</div>
                <div class="value">{{ orderTrendData.totalPaidOrders || 0 }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="label">支付率</div>
                <div class="value">{{ orderTrendData.paymentRate || '0.00%' }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item">
                <div class="label">总金额</div>
                <div class="value">¥{{ formatAmount(orderTrendData.totalAmount) }}</div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div ref="trendChartContainer" style="height: 300px;"></div>
      </el-card>
      
      <!-- 订单分布卡片 -->
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>套餐订单分布</span>
            <div class="period-selector">
              <el-radio-group v-model="distributionPeriod" size="small" @change="fetchOrderDistribution">
                <el-radio-button :label="7">7天</el-radio-button>
                <el-radio-button :label="30">30天</el-radio-button>
                <el-radio-button :label="90">90天</el-radio-button>
                <el-radio-button :label="365">全年</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>
        <div class="distribution-summary">
          <div class="total-orders">
            <span class="label">总订单数：</span>
            <span class="value">{{ orderDistributionData.totalOrderCount || 0 }}</span>
            <span class="label" style="margin-left: 20px;">总金额：</span>
            <span class="value">¥{{ formatAmount(orderDistributionData.totalAmount) }}</span>
            <span class="label" style="margin-left: 20px;">时间范围：</span>
            <span class="value">{{ getTimeRangeText(orderDistributionData.days) }}</span>
          </div>
        </div>
        <div ref="pieChartContainer" style="height: 300px;"></div>
      </el-card>
      
      <!-- 套餐详细信息卡片 -->
      <el-card class="box-card plan-details">
        <template #header>
          <div class="card-header">
            <span>套餐详细信息</span>
          </div>
        </template>
        <el-table :data="salesPlanData" style="width: 100%">
          <el-table-column prop="salesPlan" label="套餐名称" />
          <el-table-column prop="orderCount" label="订单数量" />
          <el-table-column label="订单金额">
            <template #default="scope">
              ¥{{ formatAmount(scope.row.totalAmount) }}
            </template>
          </el-table-column>
          <el-table-column label="占比">
            <template #default="scope">
              {{ calculatePercentage(scope.row.orderCount, orderDistributionData.totalOrderCount) }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, computed } from 'vue';
  import * as echarts from 'echarts';
  import api from '@/axios';
  
  // 订单趋势数据
  const trendPeriod = ref(7);
  const orderTrendData = ref({});
  const trendChartContainer = ref(null);
  let trendChart = null;
  
  // 订单分布数据
  const distributionPeriod = ref(7);
  const orderDistributionData = ref({});
  const salesPlanData = ref([]);
  const pieChartContainer = ref(null);
  let pieChart = null;
  
  // 获取订单趋势数据
  const fetchOrderTrend = async () => {
    try {
      const response = await api.get(`/api/dashboard/orders/trend?days=${trendPeriod.value}`);
      if (response.data.code === 0) {
        orderTrendData.value = response.data.data;
        renderTrendChart();
      }
    } catch (error) {
      console.error('获取订单趋势数据失败:', error);
    }
  };
  
  // 获取订单分布数据
  const fetchOrderDistribution = async () => {
    try {
      const response = await api.get(`/api/dashboard/orders/distribution?days=${distributionPeriod.value}`);
      if (response.data.code === 0) {
        orderDistributionData.value = response.data.data;
        salesPlanData.value = response.data.data.salesPlanData || [];
        renderPieChart();
      }
    } catch (error) {
      console.error('获取订单分布数据失败:', error);
    }
  };
  
  // 获取时间范围文本
  const getTimeRangeText = (days) => {
    if (!days) return '最近7天';
    
    switch (days) {
      case 7: return '最近7天';
      case 30: return '最近30天';
      case 90: return '最近90天';
      case 365: return '全年';
      default: return `最近${days}天`;
    }
  };
  
  // 渲染订单趋势图
  const renderTrendChart = () => {
    if (!trendChartContainer.value) return;
    
    if (!trendChart) {
      trendChart = echarts.init(trendChartContainer.value);
    }
    
    const dailyData = orderTrendData.value.dailyData || [];
    const dates = dailyData.map(item => item.date);
    const totalOrders = dailyData.map(item => item.totalOrders);
    const paidOrders = dailyData.map(item => item.paidOrders);
    const amounts = dailyData.map(item => parseFloat(item.totalAmount));
    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['总订单数', '已支付订单', '订单金额'],
        bottom: 'bottom'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: dates,
          axisLabel: {
            rotate: 45,
            formatter: (value) => {
              // 格式化日期，只显示月和日
              const date = new Date(value);
              return `${date.getMonth() + 1}/${date.getDate()}`;
            }
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '订单数',
          position: 'left',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#5470C6'
            }
          },
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '金额',
          position: 'right',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#91CC75'
            }
          },
          axisLabel: {
            formatter: '¥{value}'
          }
        }
      ],
      series: [
        {
          name: '总订单数',
          type: 'bar',
          data: totalOrders
        },
        {
          name: '已支付订单',
          type: 'bar',
          data: paidOrders
        },
        {
          name: '订单金额',
          type: 'line',
          yAxisIndex: 1,
          data: amounts,
          smooth: true
        }
      ]
    };
    
    trendChart.setOption(option);
  };
  
  // 渲染订单分布饼图
  const renderPieChart = () => {
    if (!pieChartContainer.value) return;
    
    if (!pieChart) {
      pieChart = echarts.init(pieChartContainer.value);
    }
    
    const planData = salesPlanData.value || [];
    
    // 如果没有数据，显示无数据提示
    if (planData.length === 0) {
      pieChart.setOption({
        title: {
          text: '暂无数据',
          left: 'center',
          top: 'center'
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            data: []
          }
        ]
      });
      return;
    }
    
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        bottom: 'bottom',
        data: planData.map(item => item.salesPlan)
      },
      series: [
        {
          name: '套餐订单',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: planData.map(item => ({
            value: item.orderCount,
            name: item.salesPlan
          }))
        }
      ]
    };
    
    pieChart.setOption(option);
  };
  
  // 格式化金额
  const formatAmount = (amount) => {
    if (!amount) return '0.00';
    return parseFloat(amount).toFixed(2);
  };
  
  // 计算百分比
  const calculatePercentage = (value, total) => {
    if (!total || total === 0) return '0.00%';
    return ((value / total) * 100).toFixed(2) + '%';
  };
  
  onMounted(() => {
    fetchOrderTrend();
    fetchOrderDistribution();
    
    window.addEventListener('resize', () => {
      trendChart?.resize();
      pieChart?.resize();
    });
  });
  </script>
  
  <style scoped>
  .order-dashboard {
    margin-bottom: 20px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .trend-summary {
    margin-bottom: 20px;
  }
  
  .summary-item {
    text-align: center;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .summary-item .label {
    font-size: 14px;
    color: #606266;
    margin-bottom: 5px;
  }
  
  .summary-item .value {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
  }
  
  .distribution-summary {
    margin-bottom: 15px;
  }
  
  .total-orders {
    font-size: 16px;
  }
  
  .total-orders .value {
    font-weight: bold;
  }
  
  .period-selector {
    display: flex;
    align-items: center;
  }
  
  .order-trend, .plan-details {
    margin-bottom: 20px;
  }
  </style>