<template>
    <div class="pagination-container" style="margin-bottom: 10px;">
        <!-- 工具栏部分 -->
        <el-row :gutter="20" class="toolbar">
            <el-col :xs="24" :sm="8" :md="6" :lg="4" class="toolbar-item">
                <el-input v-model="searchQuery.query" placeholder="邮箱/备注"></el-input>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="primary" @click="fetchData" :icon="Search">查询</el-button>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="primary" @click="openAddDialog">添加账号</el-button>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="danger" @click="deleteBatch">
                    <template #default="scope">
                        <el-icon>
                            <Delete />
                        </el-icon>
                        批量删除
                    </template>
                </el-button>
            </el-col>
        </el-row>
        <Transition name="fade">
            <el-table :data="tableData" border @sort-change="sortCustomer" style="width: 100%;margin-top: 10px;"
                :max-height="tableMaxHeight" :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }" class="responsive-table"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="email" label="邮箱" width="250" show-overflow-tooltip />
                <el-table-column prop="password" label="密码" width="150" />

                <el-table-column prop="isPro" label="账号类型" width="100">
                    <template #default="scope">
                        <el-tag v-if="scope.row.isPro === 0" type="success">普号</el-tag>
                        <el-tag v-else type="danger">Pro</el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="status" label="状态" width="100">
                    <template #default="scope">
                        <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
                            @change="(newValue) => updateUser(scope.row, newValue)" />
                    </template>
                </el-table-column>

                <el-table-column prop="count" label="请求量" width="100" />
                <el-table-column prop="officialSession" label="官方Session" width="200" show-overflow-tooltip />
                <el-table-column prop="remark" label="备注" width="150" show-overflow-tooltip />
                <el-table-column prop="createTime" label="创建时间" sortable="custom" width="150" :formatter="formatDate" />
                <el-table-column prop="updateTime" label="修改时间" width="150" :formatter="formatDate" />
                <el-table-column fixed="right" label="操作" width="180" :fixed="!isMobile() ? 'right' : false">
                    <template #default="scope">
                        <el-button size="small" type="primary" @click="editRow(scope.row)">编辑</el-button>
                        <el-button size="small" type="danger" @click="deleteAccount(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </Transition>

        <el-pagination v-model:currentPage="searchQuery.pageNum" :page-size="searchQuery.pageSize"
            :total="searchQuery.totalItems" layout="total,prev, pager, next" :page-sizes="[10, 20, 50, 100]"
            @current-change="handlePageChange" background />
    </div>

    <!-- 添加账号弹窗 -->
    <el-dialog title="添加Grok账号" v-model="addDialogVisible" :width="dialogWidth" height="600px"
        :close-on-click-modal="false">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="right" status-icon>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="邮箱" prop="email">
                        <el-input v-model="form.email" placeholder="请输入邮箱">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="密码" prop="password">
                        <el-input v-model="form.password" type="password" placeholder="请输入密码" show-password>
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="是否Pro">
                        <el-switch v-model="form.isPro" :active-value="1" :inactive-value="0" />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="开启">
                        <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="24">
                    <el-form-item label="官方Session">
                        <el-input v-model="form.officialSession" type="textarea" placeholder="请输入官方Session" :rows="3" />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="24">
                    <el-form-item label="备注">
                        <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="2" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="addDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 编辑账号弹窗 -->
    <el-dialog title="编辑Grok账号" v-model="updateDialogVisible" :width="dialogWidth" height="600px"
        :close-on-click-modal="false">
        <el-form ref="updateFormRef" :model="form" :rules="rules" label-width="120px" label-position="right"
            status-icon>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="邮箱" prop="email">
                        <el-input v-model="form.email" placeholder="请输入邮箱">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="密码" prop="password">
                        <el-input v-model="form.password" type="password" placeholder="请输入密码" show-password>
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="是否Pro">
                        <el-switch v-model="form.isPro" :active-value="1" :inactive-value="0" />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="开启">
                        <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="24">
                    <el-form-item label="官方Session">
                        <el-input v-model="form.officialSession" type="textarea" placeholder="请输入官方Session" :rows="3" />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="24">
                    <el-form-item label="备注">
                        <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="2" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="updateDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitUpdateForm">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive } from 'vue';
import api from '@/axios'
import { getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { isMobile } from '@/utils';

const { proxy } = getCurrentInstance();
const addDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const tableData = ref([]);
const selected = ref([]);

const handleSelectionChange = (val) => {
    selected.value = val.map(item => item.id)
    console.log(val)
}

const deleteBatch = () => {
    if (selected.value.length === 0) {
        ElMessage.warning('请选择要删除的账号')
        return
    }

    ElMessageBox.confirm('是否批量删除选中的账号', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        const res = await api.post("/api/lyy/grokSession/delete", selected.value, {
            timeout: 20000
        })
        if (res.data.code === 0) {
            ElMessage.success('删除成功')
            fetchData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const defaultForm = {
    email: '',
    password: '',
    status: 1,
    isPro: 0,
    officialSession: '',
    count: 0,
    remark: ''
}

const form = reactive({
    email: '',
    password: '',
    status: 1,
    isPro: 0,
    officialSession: '',
    count: 0,
    remark: ''
});

const searchQuery = ref({
    query: '',
    pageNum: 1,
    pageSize: 10,
    sortField: '',
    sortOrder: '',
    totalItems: 0
});

const openAddDialog = async () => {
    form.id = undefined
    Object.assign(form, defaultForm)
    addDialogVisible.value = true
}

const handlePageChange = (page) => {
    searchQuery.value.pageNum = page;
    fetchData();
};

const rules = ref({
    email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' }
    ],
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' }
    ],
})

const fetchData = async () => {
    try {
        const res = await api.post("/api/lyy/grokSession/page", searchQuery.value, {
            timeout: 20000
        })
        tableData.value = res.data.data
        searchQuery.value.totalItems = res.data.total
    } catch (error) {
        ElMessage.error('获取数据失败')
    }
};

fetchData();

const formatDate = (row, col, cellvalue) => {
    return proxy.$dateFormat(cellvalue)
};

const submitForm = async () => {
    console.log(form)
    await proxy.$refs.formRef.validate()
    const loading = ElLoading.service({
        lock: true,
        text: '添加中...',
        background: 'rgba(0, 0, 0, 0.7)',
    })
    try {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 20000)

        const res = await api.post("/api/lyy/grokSession/addOrUpdate", form, {
            signal: controller.signal,
            timeout: 20000
        })
        clearTimeout(timeoutId)

        if (res.data.code === 0) {
            ElMessage.success('添加成功')
            addDialogVisible.value = false
            fetchData()
            Object.assign(form, defaultForm)
        } else {
            ElMessage.error(res.data.msg)
        }
    } catch (error) {
        if (error.name === 'AbortError') {
            ElMessage.error('添加超时，请重试')
        } else {
            ElMessage.error(error.message || '添加失败')
        }
    } finally {
        loading.close()
    }
}

const submitUpdateForm = async () => {
    console.log(form)
    await proxy.$refs.updateFormRef.validate()
    const loading = ElLoading.service({
        lock: true,
        text: '修改中...',
        background: 'rgba(0, 0, 0, 0.7)',
    })
    try {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 20000)

        const res = await api.post("/api/lyy/grokSession/addOrUpdate", form, {
            signal: controller.signal,
            timeout: 20000
        })
        clearTimeout(timeoutId)

        if (res.data.code === 0) {
            ElMessage.success('修改成功')
            updateDialogVisible.value = false
            fetchData()
            Object.assign(form, defaultForm)
        } else {
            ElMessage.error(res.data.msg)
        }
    } catch (error) {
        if (error.name === 'AbortError') {
            ElMessage.error('修改超时，请重试')
        } else {
            ElMessage.error(error.message || '修改失败')
        }
    } finally {
        loading.close()
    }
}

const updateUser = async (row, newVal) => {
    console.log(row, newVal)
    try {
        const res = await api.post("/api/lyy/grokSession/addOrUpdate", row, {
            timeout: 20000
        })
        if (res.data.code === 0) {
            ElMessage.success('修改成功')
            fetchData()
        } else {
            ElMessage.error(res.data.msg)
            // 恢复原值
            row.status = row.status === 1 ? 0 : 1
        }
    } catch (error) {
        ElMessage.error('修改失败')
        // 恢复原值
        row.status = row.status === 1 ? 0 : 1
    }
}

const deleteAccount = async (row) => {
    ElMessageBox.confirm('确定删除该账号?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        const res = await api.post("/api/lyy/grokSession/delete", [row.id], {
            timeout: 20000
        })
        if (res.data.code === 0) {
            ElMessage.success('删除成功')
            fetchData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const sortCustomer = ({ prop, order }) => {
    const sortOrder = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : '';
    console.log(prop, order)
    searchQuery.value.sortField = prop
    searchQuery.value.sortOrder = sortOrder
    fetchData()
};

const assignRow2Form = (row) => {
    Object.assign(form, row)
}

const editRow = async (row) => {
    console.log(row)
    updateDialogVisible.value = true
    assignRow2Form(row)
}

const dialogWidth = ref(isMobile() ? '90%' : '600px');
const formLabelWidth = ref(isMobile() ? '80px' : '120px');
const tableMaxHeight = ref(window.innerHeight - 300);
</script>

<style scoped>
.pagination-container {
    padding: 20px;
}

.el-table {
    margin-bottom: 20px;
    text-align: center;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}

.toolbar {
    margin-bottom: 20px;
}

.toolbar-item {
    margin-bottom: 10px;
}

@media screen and (max-width: 768px) {
    .pagination-container {
        padding: 10px;
    }

    .el-table {
        margin-bottom: 10px;
    }
}

/* 响应式表格样式 */
@media screen and (max-width: 768px) {
    .responsive-table {
        width: 100%;
        overflow-x: auto;
    }

    .toolbar-item {
        margin-bottom: 10px;
    }
}
</style> 