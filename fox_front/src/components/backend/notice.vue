<template>
    <div class="pagination-container" style="margin-bottom: 10px;">
        <el-row :gutter="20">
            <el-col :xs="24" :sm="4" style="margin-bottom: 10px;">
                <el-button type="primary" @click="addNoticeDialogVisible = true">添加公告</el-button>
            </el-col>
        </el-row>

        <!-- 移动端表格容器 -->
        <div class="mobile-table-container" v-if="isMobile">
            <div v-for="item in tableData" :key="item.id" class="mobile-table-item">
                <div class="mobile-table-header">
                    <div>
                        <el-tag size="small" :type="type[item.type][0]">{{ type[item.type][1] }}</el-tag>
                        <el-tag size="small" :type="item.contentType === 1 ? 'success' : 'info'" style="margin-left: 5px;">
                            {{ item.contentType === 1 ? '富文本' : 'HTML' }}
                        </el-tag>
                    </div>
                    <el-switch v-model="item.status" :active-value="1" :inactive-value="0"
                        @change="(newValue) => updateNotification(item, newValue)" />
                </div>
                <div class="mobile-table-content">{{ item.content.substring(0, 100) + '...' }}</div>
                <div class="mobile-table-footer">
                    <span>创建时间: {{ formatDate(null, null, item.createdAt) }}</span>
                    <div class="mobile-table-actions">
                        <el-button size="small" type="primary" @click="editRow(item)">编辑</el-button>
                        <el-button size="small" type="danger" @click="deleteUser(item)">删除</el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- PC端表格 -->
        <Transition name="fade">
            <el-table v-if="!isMobile" :data="tableData" class="table-container" border @sort-change="sortCustomer" max-height="250"
                :header-cell-style="{ 'text-align': 'center' }" :cell-style="{ 'text-align': 'center' }">
                <el-table-column type="selection" width="55" />

                <el-table-column fixed prop="id" label="id" width="50" />

                <el-table-column prop="content" label="公告内容" show-overflow-tooltip />

                <el-table-column prop="type" label="提示类型" width="150">
                    <template #default="scope">

                        <el-tag size="large" :type="type[scope.row.type][0]">{{
                            type[scope.row.type][1]
                            }}</el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="contentType" label="内容类型" width="120">
                    <template #default="scope">
                        <el-tag size="small" :type="scope.row.contentType === 1 ? 'success' : 'info'">
                            {{ scope.row.contentType === 1 ? '富文本' : 'HTML' }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="type" label="启用" width="150">
                    <template #default="scope">
                        <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
                            @change="(newValue) => updateNotification(scope.row, newValue)" />
                    </template>
                </el-table-column>

                <el-table-column prop="createdAt" label="创建时间" sortable="custom" width="150" :formatter="formatDate" />
                <el-table-column prop="updatedAt" label="修改时间" width="150" :formatter="formatDate" />
                <el-table-column fixed="right" label="操作" width="180">
                    <template #default="scope">
                        <el-button size="small" type="primary" @click="editRow(scope.row)">编辑</el-button>
                        <el-button size="small" type="danger" @click="deleteUser(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </Transition>

        <el-pagination v-model:currentPage="searchQuery.pageNum" :page-size="searchQuery.pageSize"
            :total="searchQuery.totalItems" layout="total,prev, pager, next" :page-sizes="[10, 20, 50, 100]"
            @current-change="handlePageChange" background />
    </div>
    <!-- 修改后的添加公告对话框 -->
    <el-dialog title="添加公告" v-model="addNoticeDialogVisible" :close-on-click-modal="false"
        :fullscreen="isMobile" :width="isMobile ? '100%' : '80%'">

        <div class="editor-container">
            <!-- 编辑模式选择 -->
            <div class="editor-mode-selector">
                <el-radio-group v-model="editorMode" @change="onEditorModeChange">
                    <el-radio-button label="rich">富文本编辑器</el-radio-button>
                    <el-radio-button label="html">HTML编辑器</el-radio-button>
                </el-radio-group>
            </div>

            <el-row :gutter="20">
                <el-col :xs="24" :sm="12">
                    <!-- 编辑区域 -->
                    <div class="preview-container">
                        <div class="preview-header">公告内容</div>

                        <!-- 富文本编辑器 -->
                        <div v-if="editorMode === 'rich'" class="rich-editor-container">
                            <Toolbar
                                style="border-bottom: 1px solid #ccc"
                                :editor="editorRef"
                                :defaultConfig="toolbarConfig"
                                :mode="mode"
                            />
                            <Editor
                                style="height: 300px; overflow-y: hidden;"
                                v-model="form.content"
                                :defaultConfig="editorConfig"
                                :mode="mode"
                                @onCreated="handleCreated"
                                @onChange="handleEditorChange"
                            />
                        </div>

                        <!-- HTML编辑器 -->
                        <el-input
                            v-else
                            v-model="form.content"
                            type="textarea"
                            placeholder="请输入HTML内容"
                            :rows="15"
                            @input="updatePreview"
                        />
                    </div>
                </el-col>
                <el-col :xs="24" :sm="12">
                    <!-- 预览区域 -->
                    <div class="preview-container">
                        <div class="preview-header">预览效果</div>
                        <div class="preview-content" v-richText="form.content"></div>
                    </div>
                </el-col>
            </el-row>
        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-radio-group v-model="form.type">
                    <el-radio-button label="1">首页公告</el-radio-button>
                    <el-radio-button label="2">站内通知</el-radio-button>
                    <el-radio-button label="3">使用说明</el-radio-button>
                    <el-radio-button label="4">GPT选车说明</el-radio-button>
                    <el-radio-button label="5">Claude选车说明</el-radio-button>
                    <el-radio-button label="6">Grok选车说明</el-radio-button>
                </el-radio-group>
                <div>
                    <el-button @click="addNoticeDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitForm">确定</el-button>
                </div>
            </span>
        </template>
    </el-dialog>
    <el-dialog title="编辑公告" v-model="updateNoticeDialogVisible" :close-on-click-modal="false"
        :fullscreen="isMobile" :width="isMobile ? '100%' : '80%'">

        <div class="editor-container">
            <!-- 编辑模式选择 -->
            <div class="editor-mode-selector">
                <el-radio-group v-model="editEditorMode" @change="onEditEditorModeChange">
                    <el-radio-button label="rich">富文本编辑器</el-radio-button>
                    <el-radio-button label="html">HTML编辑器</el-radio-button>
                </el-radio-group>
            </div>

            <el-row :gutter="20">
                <el-col :xs="24" :sm="12">
                    <!-- 编辑区域 -->
                    <div class="preview-container">
                        <div class="preview-header">公告内容</div>

                        <!-- 富文本编辑器 -->
                        <div v-if="editEditorMode === 'rich'" class="rich-editor-container">
                            <Toolbar
                                style="border-bottom: 1px solid #ccc"
                                :editor="editEditorRef"
                                :defaultConfig="editToolbarConfig"
                                :mode="mode"
                            />
                            <Editor
                                style="height: 300px; overflow-y: hidden;"
                                v-model="form.content"
                                :defaultConfig="editEditorConfig"
                                :mode="mode"
                                @onCreated="handleEditCreated"
                                @onChange="handleEditEditorChange"
                            />
                        </div>

                        <!-- HTML编辑器 -->
                        <el-input
                            v-else
                            v-model="form.content"
                            type="textarea"
                            placeholder="请输入HTML内容"
                            :rows="15"
                            @input="updatePreview"
                        />
                    </div>
                </el-col>
                <el-col :xs="24" :sm="12">
                    <!-- 预览区域 -->
                    <div class="preview-container">
                        <div class="preview-header">预览效果</div>
                        <div class="preview-content" v-richText="form.content"></div>
                    </div>
                </el-col>
            </el-row>
        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-radio-group v-model="form.type">
                    <el-radio-button label="1">首页公告</el-radio-button>
                    <el-radio-button label="2">站内通知</el-radio-button>
                    <el-radio-button label="3">使用说明</el-radio-button>
                    <el-radio-button label="4">GPT选车说明</el-radio-button>
                    <el-radio-button label="5">Claude选车说明</el-radio-button>
                    <el-radio-button label="6">Grok选车说明</el-radio-button>
                </el-radio-group>
                <div>
                    <el-button @click="updateNoticeDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitUpdateForm">确定</el-button>
                </div>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, onBeforeUnmount, shallowRef } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import '@wangeditor/editor/dist/css/style.css';
import api from '@/axios'
import { getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const addNoticeDialogVisible = ref(false);
const updateNoticeDialogVisible = ref(false);
const tableData = ref([]);

// 编辑器相关
const editorRef = shallowRef();
const editEditorRef = shallowRef();
const mode = 'default';
const editorMode = ref('rich'); // 'rich' 或 'html'
const editEditorMode = ref('rich'); // 编辑时的编辑器模式

const type = {
    "1": ['primary', '首页公告'],
    "2": ['success', '站内通知'],
    "3": ['warning', '使用说明'],
    "4": ['info', 'GPT选车说明'],
    "5": ['info', 'Claude选车说明'],
    "6": ['info', 'Grok选车说明'],
}

const defaultForm = {
    id: null,
    content: '',
    type: "1",
    status: 0,
    contentType: 2, // 默认为HTML类型
}

const form = reactive({
    content: '',
    type: "1",
    status: 0,
    contentType: 2, // 默认为HTML类型
});

// 编辑器配置
const toolbarConfig = {
    toolbarKeys: [
        'headerSelect',
        'blockquote',
        '|',
        'bold',
        'underline',
        'italic',
        'color',
        'bgColor',
        '|',
        'fontSize',
        'fontFamily',
        'lineHeight',
        '|',
        'bulletedList',
        'numberedList',
        'todo',
        'justifyLeft',
        'justifyRight',
        'justifyCenter',
        'justifyJustify',
        '|',
        'insertLink',
        'insertImage',
        'insertTable',
        'codeBlock',
        'divider',
        '|',
        'undo',
        'redo',
        '|',
        'fullScreen'
    ]
};

const editorConfig = {
    placeholder: '请输入内容...',
    MENU_CONF: {
        uploadImage: {
            async customUpload(file, insertFn) {
                try {
                    const formData = new FormData()
                    formData.append('file', file)

                    const response = await api.post('/api/files/upload', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data'
                        }
                    })

                    if (response.data.code === 0) {
                        const imageUrl = process.env.NODE_ENV === 'development'
                            ? import.meta.env.VITE_API_HOST + response.data.data.downloadUrl
                            : window.location.origin + response.data.data.downloadUrl

                        insertFn(imageUrl, file.name, imageUrl)
                    } else {
                        throw new Error(response.data.msg || '上传失败')
                    }
                } catch (error) {
                    console.error('图片上传失败:', error)
                    ElMessage.error('图片上传失败: ' + error.message)
                }
            }
        }
    }
};

const editToolbarConfig = { ...toolbarConfig };
const editEditorConfig = { ...editorConfig };

const searchQuery = ref({
    query: '',
    pageNum: 1,
    pageSize: 10,
    sortField: '',
    sortOrder: '',
    totalItems: 0
});

// 编辑器事件处理
const handleCreated = (editor) => {
    editorRef.value = editor;
};

const handleEditCreated = (editor) => {
    editEditorRef.value = editor;
};

const handleEditorChange = (editor) => {
    // 富文本编辑器内容变化时更新预览
};

const handleEditEditorChange = (editor) => {
    // 编辑时富文本编辑器内容变化
};

const onEditorModeChange = (mode) => {
    // 切换编辑器类型时清空内容
    form.content = '';

    // 清空富文本编辑器
    if (editorRef.value) {
        editorRef.value.clear();
    }
};

const onEditEditorModeChange = (mode) => {
    // 切换编辑器类型时清空内容
    form.content = '';

    // 清空富文本编辑器
    if (editEditorRef.value) {
        editEditorRef.value.clear();
    }
};

const updatePreview = () => {
    // HTML模式下的预览更新
};

const handlePageChange = (page) => {
    searchQuery.value.pageNum = page;
    fetchData();
};

const fetchData = async () => {
    const res = await api.post("/api/notification/list", searchQuery.value)
    tableData.value = res.data.data
    searchQuery.value.totalItems = res.data.total
    console.log(searchQuery.value.totalItems)
};

fetchData();

const formatDate = (row, col, cellvalue) => {
    return proxy.$dateFormat(cellvalue)
};

const submitForm = async () => {
    if (editorMode.value === 'rich' && editorRef.value) {
        form.content = editorRef.value.getHtml();
    }

    // 根据编辑器模式设置 contentType
    form.contentType = editorMode.value === 'rich' ? 1 : 2;

    const res = await api.post("/api/notification/addOrUpdate", form)
    if (res.data.code === 0) {
        ElMessage.success('添加成功')
        addNoticeDialogVisible.value = false
        fetchData()
        Object.assign(form, defaultForm)
        if (editorRef.value) {
            editorRef.value.clear();
        }
    } else {
        ElMessage.error(res.data.msg)
    }
}

const submitUpdateForm = async () => {
    if (editEditorMode.value === 'rich' && editEditorRef.value) {
        form.content = editEditorRef.value.getHtml();
    }

    // 根据编辑器模式设置 contentType
    form.contentType = editEditorMode.value === 'rich' ? 1 : 2;

    const res = await api.post("/api/notification/addOrUpdate", form)
    if (res.data.code === 0) {
        ElMessage.success('修改成功')
        updateNoticeDialogVisible.value = false
        fetchData()
        Object.assign(form, defaultForm)
        if (editEditorRef.value) {
            editEditorRef.value.clear();
        }
    } else {
        ElMessage.error(res.data.msg)
    }
}

const updateNotification = async (row, newVal) => {
    console.log(row, newVal)
    const res = await api.post("/api/notification/addOrUpdate", row)
    if (res.data.code === 0) {
        ElMessage.success('修改成功')
        updateNoticeDialogVisible.value = false
        fetchData()
    } else {
        ElMessage.error(res.data.msg)
    }
    Object.assign(form, defaultForm)
}

const deleteUser = async (row) => {
    ElMessageBox.confirm('确定删除该记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        const res = await api.post("/api/notification/delete?id=" + row.id, form)
        if (res.data.code === 0) {
            ElMessage.success('删除成功')
            fetchData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const assignRow2Form = (row) => {
    Object.assign(form, row)
    form.type = row.type + ''

    // 根据 contentType 设置编辑器模式
    editEditorMode.value = (row.contentType === 1) ? 'rich' : 'html';

    setTimeout(() => {
        if (editEditorMode.value === 'rich' && editEditorRef.value && form.content) {
            editEditorRef.value.setHtml(form.content);
        }
    }, 100);
}

const editRow = async (row) => {
    console.log(row)
    updateNoticeDialogVisible.value = true
    assignRow2Form(row)
}

const isMobile = ref(window.innerWidth <= 768);

onMounted(() => {
    window.addEventListener('resize', () => {
        isMobile.value = window.innerWidth <= 768;
    });
});

onUnmounted(() => {
    window.removeEventListener('resize', () => {
        isMobile.value = window.innerWidth <= 768;
    });
});

onBeforeUnmount(() => {
    const editor = editorRef.value;
    const editEditor = editEditorRef.value;
    if (editor) editor.destroy();
    if (editEditor) editEditor.destroy();
});
</script>

<style scoped>
:deep(.el-dialog) {
    width: 100%;
    height: 100%;
    padding: none;
    margin: none;
}

.pagination-container {
    padding: 20px;
    flex: 1;
    height: calc(100vh - 200px);
}

.table-container {
    margin-bottom: 20px;
    text-align: center;
    flex: 1;
    height: calc(100vh - 200px);
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}

.editor-container {
    width: 100%;
    border-radius: 4px;
}

.editor-mode-selector {
    margin-bottom: 15px;
    text-align: center;
}

.rich-editor-container {
    border: 1px solid #ccc;
    z-index: 100;
}

.preview-container {
    height: 100%;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
}

.preview-header {
    padding: 8px 15px;
    border-bottom: 1px solid #dcdfe6;
    background-color: #f5f7fa;
    font-weight: bold;
}

.preview-content {
    padding: 15px;
    min-height: 300px;
    overflow-y: auto;
    background-color: white;
}

/* 确保预览区域显示完整的HTML样式 */
.preview-content :deep(*) {
    max-width: 100%;
}

.dialog-footer {
    display: flex;
    justify-content: space-between;
}

/* 添加移动端样式 */
.mobile-table-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.mobile-table-item {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
    background: white;
}

.mobile-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.mobile-table-content {
    margin: 10px 0;
    word-break: break-all;
}

.mobile-table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    font-size: 0.9em;
    color: #666;
}

.mobile-table-actions {
    display: flex;
    gap: 10px;
}

/* 响应式样式调整 */
@media screen and (max-width: 768px) {
    .pagination-container {
        padding: 10px;
        height: auto;
    }

    .preview-container {
        margin-bottom: 15px;
    }

    .dialog-footer {
        flex-direction: column;
        gap: 10px;
    }

    :deep(.el-dialog__body) {
        padding: 10px;
    }

    .editor-mode-selector {
        margin-bottom: 10px;
    }
}

/* wangEditor 样式覆盖 */
:deep(.w-e-text-container) {
    z-index: 100 !important;
}

:deep(.w-e-modal) {
    z-index: 9999 !important;
}
</style>