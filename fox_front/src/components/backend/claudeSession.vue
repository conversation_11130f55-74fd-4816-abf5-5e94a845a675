<template>
    <div class="pagination-container" style="margin-bottom: 10px;">
        <!-- 工具栏部分 -->
        <el-row :gutter="20" class="toolbar">
            <el-col :xs="24" :sm="8" :md="6" :lg="4" class="toolbar-item">
                <el-input v-model="searchQuery.query" placeholder="邮箱/备注"></el-input>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="primary" @click="fetchData" :icon="Search">查询</el-button>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="primary" @click="openAddClaudeDialog">添加账号</el-button>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="danger" @click="deleteBatch">
                    <template #default="scope">
                        <el-icon><Delete /></el-icon>
                        批量删除
                    </template>
                </el-button>
            </el-col>
        </el-row>
        <Transition name="fade">
            <el-table :data="tableData" border @sort-change="sortCustomer" style="width: 100%;margin-top: 10px;"
                :max-height="tableMaxHeight" :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }" class="responsive-table"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />

                <el-table-column fixed prop="id" label="id" width="50" />

                <el-table-column prop="carID" label="车号" width="100" />
                <el-table-column prop="email" label="邮箱" width="300" />

                <el-table-column prop="accountType" label="账号类型" width="100">
                    <template #default="scope">
                        <el-tag v-if="scope.row.accountType == 0" type="success">普号</el-tag>
                        <el-tag v-else type="danger">Pro</el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="enabled" label="状态" width="150">
                    <template #default="scope">
                        <el-switch v-model="scope.row.enabled" :active-value="true" :inactive-value="false"
                            @change="(newValue) => updateUser(scope.row, newValue)" />
                    </template>
                </el-table-column>

                <el-table-column prop="session" show-overflow-tooltip label="官方session" width="200" />

                <el-table-column prop="remarks" label="备注" width="200" />
                <el-table-column prop="createdAt" label="创建时间" sortable="custom" width="150" :formatter="formatDate" />
                <el-table-column prop="updatedAt" label="修改时间" width="150" :formatter="formatDate" />
                <el-table-column :fixed="!isMobile() ? 'right' : false" label="操作" width="180">
                    <template #default="scope">
                        <el-button size="small" type="primary" @click="editRow(scope.row)">编辑</el-button>
                        <el-button size="small" type="danger" @click="deleteAccount(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </Transition>

        <el-pagination v-model:currentPage="searchQuery.pageNum" :page-size="searchQuery.pageSize"
            :total="searchQuery.totalItems" layout="total,prev, pager, next" :page-sizes="[10, 20, 50, 100]"
            @current-change="handlePageChange" background />
    </div>
    <!-- 添加账号弹窗 -->
    <el-dialog title="添加账号" v-model="addGptDialogVisible" max-width="600px" height="500px"
        :close-on-click-modal="false">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="right" status-icon>
            <el-row :gutter="20">

                <el-col :span="24">
                    <el-form-item label="车号" prop="carID">
                        <el-input v-model="form.carID" required placeholder="">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="邮箱" prop="email">
                        <el-input v-model="form.email" placeholder="请输入邮箱">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>


            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="是否Pro">
                        <el-switch v-model="form.accountType" :active-value="1" :inactive-value="0" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="开启">
                        <el-switch v-model="form.enabled" :active-value="true" :inactive-value="false" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">

                    <el-form-item label="官方session">
                        <el-input v-model="form.session" type="textarea" placeholder="不填则自动获取session"
                            :rows="3" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="addGptDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog title="编辑账号" v-model="updateGptDialogVisible" max-width="600px" height="500px"
        :close-on-click-modal="false">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="right" status-icon>
            <el-row :gutter="20">

                <el-col :span="24">
                    <el-form-item label="车号" prop="carID">
                        <el-input v-model="form.carID" required placeholder="">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="邮箱" prop="email">
                        <el-input v-model="form.email" placeholder="请输入邮箱">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>


            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="是否Pro">
                        <el-switch v-model="form.accountType" :active-value="1" :inactive-value="0" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="开启">
                        <el-switch v-model="form.enabled" :active-value="true" :inactive-value="false" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">

                    <el-form-item label="官方session">
                        <el-input v-model="form.session" type="textarea" placeholder=""
                            :rows="3" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="updateGptDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitUpdateForm">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive } from 'vue';
import api from '@/axios'
import { getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { isMobile } from '@/utils';

const { proxy } = getCurrentInstance();
const addGptDialogVisible = ref(false);
const updateGptDialogVisible = ref(false);
const tableData = ref([]);
const selected = ref([]);

const handleSelectionChange = (val) => {
    selected.value = val.map(item => item.id)
    console.log(val)
}

const deleteBatch = () => {
    if (selected.value.length === 0) {
        ElMessage.warning('请选择要删除的账号')
        return
    }

    ElMessageBox.confirm('是否批量删除选中的账号', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        const res = await api.post("/api/claudeSession/delete", selected.value)
        if (res.data.code === 0) {
            ElMessage.success('删除成功')
            fetchData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const defaultForm = {
    carID: '',
    email: '',
    enabled: 0,
    accountType: 0,
    session: '',
    sort: 1
}

const form = reactive({
    carID: '',
    email: '',
    enabled: 0,
    accountType: 0,
    session: '',
    sort: 1
});

const searchQuery = ref({
    query: '',
    pageNum: 1,
    pageSize: 10,
    sortField: '',
    sortOrder: '',
    totalItems: 0
});

const openAddClaudeDialog = async () => {
    form.id = undefined
    Object.assign(form, defaultForm)
    addGptDialogVisible.value = true

    const res = await api.get("/api/chatGpt/session/generateCarId")
    form.carID = res.data.data
}

const handlePageChange = (page) => {
    searchQuery.value.pageNum = page;
    fetchData();
};

const rules = ref({
    carID: [
        { required: true, message: '请输入账号名', trigger: 'blur' },
    ],
    email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    ],
});

const editRules = reactive({});
Object.assign(editRules, rules.value);

const fetchData = async () => {
    const res = await api.post("/api/claudeSession/page", searchQuery.value)
    tableData.value = res.data.data
    searchQuery.value.totalItems = res.data.total
};

// Initial data fetch
fetchData();

const formatDate = (row, col, cellvalue) => {
    return proxy.$dateFormat(cellvalue)
};

const submitForm = async () => {
    console.log(form)
    await proxy.$refs.formRef.validate()
    const res = await api.post("/api/claudeSession/addOrUpdate", form)
    if (res.data.code === 0) {
        ElMessage.success('添加成功')
        addGptDialogVisible.value = false
        fetchData()
        Object.assign(form, defaultForm)
    } else {
        ElMessage.error(res.data.msg)
    }
}

const submitUpdateForm = async () => {
    console.log(form)
    await proxy.$refs.formRef.validate()
    const res = await api.post("/api/claudeSession/addOrUpdate", form)
    if (res.data.code === 0) {
        ElMessage.success('修改成功')
        updateGptDialogVisible.value = false
        fetchData()
        Object.assign(form, defaultForm)
    } else {
        ElMessage.error(res.data.msg)
    }
}

const updateUser = async (row, newVal) => {
    console.log(row, newVal)
    const res = await api.post("/api/claudeSession/addOrUpdate", row)
    if (res.data.code === 0) {
        ElMessage.success('修改成功')
        updateGptDialogVisible.value = false
        fetchData()
        Object.assign(form, defaultForm)
    } else {
        ElMessage.error(res.data.msg)
    }
}

const deleteAccount = async (row) => {
    ElMessageBox.confirm('确定删除该账号?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        const res = await api.post("/api/claudeSession/delete", [row.id])
        if (res.data.code === 0) {
            ElMessage.success('删除成功')
            fetchData()
        } else {
            ElMessage.error(res.data.msg)
        }
    })
}

const sortCustomer = ({ prop, order }) => {
    const sortOrder = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : '';
    console.log(prop, order)
    searchQuery.value.sortField = prop
    searchQuery.value.sortOrder = sortOrder
    fetchData()
};

const assignRow2Form = (row) => {
    Object.assign(form, row)
}

const editRow = async (row) => {
    console.log(row)
    updateGptDialogVisible.value = true
    assignRow2Form(row)
}
</script>

<style scoped>
.pagination-container {
    padding: 20px;
}

.toolbar {
    margin-bottom: 20px;
}

.toolbar-item {
    margin-bottom: 10px;
}

.el-table {
    margin-bottom: 20px;
    text-align: center;
}

/* 响应式表格样式 */
@media screen and (max-width: 768px) {
    .responsive-table {
        width: 100%;
        overflow-x: auto;
    }
    
    .toolbar-item {
        margin-bottom: 10px;
    }
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}
</style>