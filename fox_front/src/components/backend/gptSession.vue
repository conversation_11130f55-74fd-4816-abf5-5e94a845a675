<template>
    <div class="pagination-container" style="margin-bottom: 10px;">
        <!-- 工具栏部分 -->
        <el-row :gutter="20" class="toolbar">
            <el-col :xs="24" :sm="8" :md="6" :lg="4" class="toolbar-item">
                <el-input v-model="searchQuery.query" placeholder="邮箱/备注"></el-input>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="primary" @click="fetchData" :icon="Search">查询</el-button>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="primary" @click="openAddgptDialog">添加账号</el-button>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="success" @click="openBatchAddDialog">批量添加</el-button>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="danger" @click="deleteBatch">
                    <template #default="scope">
                        <el-icon>
                            <Delete />
                        </el-icon>
                        批量删除
                    </template>
                </el-button>
            </el-col>
        </el-row>
        <Transition name="fade">
            <el-table :data="tableData" border @sort-change="sortCustomer" style="width: 100%;margin-top: 10px;"
                :max-height="tableMaxHeight" :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }" class="responsive-table"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />

                <el-table-column fixed prop="id" label="id" width="50" />

                <el-table-column prop="carID" label="车号" width="100" />
                <el-table-column prop="email" label="邮箱" width="300" />

                <el-table-column prop="password" label="密码" width="200">
                </el-table-column>


                <el-table-column prop="isPlus" label="账号类型" width="100">
                    <template #default="scope">
                        <el-tag v-if="scope.row.isPlus == 0" type="success">普号</el-tag>
                        <el-tag v-else type="danger">PLUS</el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="iqStatus" label="智力状态" width="100">
                    <template #default="scope">
                        <el-tag v-if="scope.row.iqStatus == 1" type="success">正常</el-tag>
                        <el-tag v-else type="danger">降智</el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="status" label="状态" width="150">
                    <template #default="scope">
                        <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
                            @change="(newValue) => updateUser(scope.row, newValue)" />
                    </template>
                </el-table-column>

                <el-table-column prop="count" label="日请求量" width="150" />
                <el-table-column prop="sort" label="排序" width="100" sortable="custom" />
                <el-table-column prop="officialSession" show-overflow-tooltip label="官方session" width="200" />

                <el-table-column prop="remark" label="备注" width="200" />
                <el-table-column prop="createTime" label="创建时间" sortable="custom" width="150" :formatter="formatDate" />
                <el-table-column prop="updateTime" label="修改时间" width="150" :formatter="formatDate" />
                <el-table-column fixed="right" label="操作" width="180" :fixed="!isMobile() ? 'right' : false">
                    <template #default="scope">
                        <el-button size="small" type="primary" @click="editRow(scope.row)">编辑</el-button>
                        <el-button size="small" type="danger" @click="deleteAccount(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </Transition>

        <el-pagination v-model:currentPage="searchQuery.pageNum" :page-size="searchQuery.pageSize"
            :total="searchQuery.totalItems" layout="total,prev, pager, next" :page-sizes="[10, 20, 50, 100]"
            @current-change="handlePageChange" background />
    </div>
    <!-- 添加账号弹窗 -->
    <el-dialog title="添加账号" v-model="addGptDialogVisible" :width="dialogWidth" height="500px"
        :close-on-click-modal="false">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="right" status-icon>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="车号" prop="carID">
                        <el-input v-model="form.carID" required placeholder="">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="邮箱" prop="email">
                        <el-input v-model="form.email" placeholder="请输入邮箱">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="密码" prop="password">
                        <el-input v-model="form.password"  placeholder="请输入密码">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="是否PLUS">
                        <el-switch v-model="form.isPlus" :active-value="1" :inactive-value="0" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="开启">
                        <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-form-item label="官方session">
                        <el-input v-model="form.officialSession" type="textarea" placeholder="不填则自动获取session"
                            :rows="3" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-form-item label="排序" prop="sort">
                        <el-input-number v-model="form.sort" :min="1" :max="999" placeholder="请输入排序值" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="addGptDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog title="编辑账号" v-model="updateGptDialogVisible" :width="dialogWidth" height="500px"
        :close-on-click-modal="false">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="right" status-icon>
            <el-row :gutter="20">

                <el-col :span="24">
                    <el-form-item label="车号" prop="carID">
                        <el-input v-model="form.carID" required placeholder="">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="邮箱" prop="email">
                        <el-input v-model="form.email" placeholder="请输入邮箱">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">

                <el-col :span="24">
                    <el-form-item label="密码" prop="password">
                        <el-input v-model="form.password"  placeholder="请输入密码">
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-row>


            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="是否PLUS">
                        <el-switch v-model="form.isPlus" :active-value="1" :inactive-value="0" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item label="开启">
                        <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-form-item label="官方session">
                        <el-input v-model="form.officialSession" type="textarea" placeholder="不填则自动获取session"
                            :rows="3" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="24">
                    <el-form-item label="排序" prop="sort">
                        <el-input-number v-model="form.sort" :min="1" :max="999" placeholder="请输入排序值" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="updateGptDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitUpdateForm">确定</el-button>
            </span>
        </template>
    </el-dialog>
    <!-- 批量添加账号弹窗 -->
    <el-dialog title="批量添加账号" v-model="batchAddDialogVisible" :width="dialogWidth" height="500px"
        :close-on-click-modal="false">
        <el-form ref="batchFormRef" :model="batchForm" :rules="batchRules" label-width="120px" label-position="right" status-icon>
            <el-row>
                <el-col :span="24">
                    <el-form-item label="官方session" prop="officialSession">
                        <el-input v-model="batchForm.officialSession" type="textarea" 
                            placeholder="请输入rt，每行一个"
                            :rows="10" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="batchAddDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitBatchForm">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script>
import { ref, reactive, toRefs } from 'vue';
import api from '@/axios'
import { getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { isMobile } from '@/utils';

export default {
    setup() {
        const { proxy } = getCurrentInstance();
        const addGptDialogVisible = ref(false);
        const updateGptDialogVisible = ref(false);
        const batchAddDialogVisible = ref(false);
        const tableData = ref([]);
        const selected = ref([])
        const handleSelectionChange = (val) => {
            selected.value = val.map(item => item.id)
            console.log(val)
        }
        const deleteBatch = () => {
            if (selected.value.length === 0) {
                ElMessage.warning('请选择要删除的账号')
                return
            }

            ElMessageBox.confirm('是否批量删除选中的账号', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                const res = await api.post("/api/chatGpt/session/deleteSession", selected.value)
                if (res.data.code === 0) {
                    ElMessage.success('删除成功')
                    fetchData()

                } else {
                    ElMessage.error(res.data.msg)
                }
            })
        }





        const defaultForm = {
            carID: '',
            email: '',
            status: 0,
            isPlus: 0,
            officialSession: '',
            sort: 1
        }
        const form = reactive({
            carID: '',
            email: '',
            status: 0,
            isPlus: 0,
            officialSession: '',
            sort: 1
        });
        const batchForm = reactive({
            officialSession: ''
        });
        const searchQuery = ref({
            query: '',
            pageNum: 1,
            pageSize: 10,
            sortField: '',
            sortOrder: '',
            totalItems: 0
        });
        const openAddgptDialog = async () => {
            form.carID = ''
            form.email = ''
            form.status = 0
            form.isPlus = 0
            form.officialSession = ''
            form.sort = 1
            form.id = undefined
            addGptDialogVisible.value = true

            const res = await api.get("/api/chatGpt/session/generateCarId")
            form.carID = res.data.data
        }

        const handlePageChange = (page) => {
            searchQuery.pageNum = page;
            fetchData();
        };
        const rules = ref({
            carID: [
                { required: true, message: '请输入账号名', trigger: 'blur' },
            ]
        })
        const editRules = reactive({})
        Object.assign(editRules, rules.value)
        delete editRules.password
        const fetchData = async () => {
            // 模拟数据请求
            const res = await api.post("/api/chatGpt/session/list", searchQuery.value)
            tableData.value = res.data.data
            searchQuery.value.totalItems = res.data.total
            console.log(searchQuery.totalItems)
        };
        fetchData();
        const formatDate = (row, col, cellvalue) => {
            return proxy.$dateFormat(cellvalue)
        };
        const submitForm = async () => {
            console.log(form)
            await proxy.$refs.formRef.validate()
            const loading = ElLoading.service({
                lock: true,
                text: '添加中...',
                background: 'rgba(0, 0, 0, 0.7)',
            })
            try {
                const controller = new AbortController()
                const timeoutId = setTimeout(() => controller.abort(), 10000)
                
                const res = await api.post("/api/chatGpt/session/addSession", form, {
                    signal: controller.signal
                })
                clearTimeout(timeoutId)
                
                if (res.data.code === 0) {
                    ElMessage.success('添加成功')
                    addGptDialogVisible.value = false
                    fetchData()
                    Object.assign(form, defaultForm)
                } else {
                    ElMessage.error(res.data.msg)
                }
            } catch (error) {
                if (error.name === 'AbortError') {
                    ElMessage.error('添加超时，请重试')
                } else {
                    ElMessage.error(error.message || '添加失败')
                }
            } finally {
                loading.close()
            }
        }
        const submitUpdateForm = async () => {
            console.log(form)
            await proxy.$refs.formRef.validate()
            const loading = ElLoading.service({
                lock: true,
                text: '修改中...',
                background: 'rgba(0, 0, 0, 0.7)',
            })
            try {
                const controller = new AbortController()
                const timeoutId = setTimeout(() => controller.abort(), 10000)
                
                const res = await api.post("/api/chatGpt/session/updateSession", form, {
                    signal: controller.signal
                })
                clearTimeout(timeoutId)
                
                if (res.data.code === 0) {
                    ElMessage.success('修改成功')
                    updateGptDialogVisible.value = false
                    fetchData()
                    Object.assign(form, defaultForm)
                } else {
                    ElMessage.error(res.data.msg)
                }
            } catch (error) {
                if (error.name === 'AbortError') {
                    ElMessage.error('修改超时，请重试')
                } else {
                    ElMessage.error(error.message || '修改失败')
                }
            } finally {
                loading.close()
            }
        }
        const updateUser = async (row, newVal) => {
            console.log(row, newVal)
            const res = await api.post("/api/chatGpt/session/updateSession", row)
            if (res.data.code === 0) {
                ElMessage.success('修改成功')
                updateGptDialogVisible.value = false
                fetchData()
                Object.assign(form, defaultForm)
            } else {
                ElMessage.error(res.data.msg)
            }

        }
        const deleteAccount = async (row) => {
            ElMessageBox.confirm('确定删除该账号?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(async () => {
                const res = await api.post("/api/chatGpt/session/deleteSession", [row.id])
                if (res.data.code === 0) {
                    ElMessage.success('删除成功')
                    fetchData()
                } else {
                    ElMessage.error(res.data.msg)
                }

            })
        }
        const sortCustomer = ({ prop, order }) => {
            // 将排序参数传递给后端
            const sortOrder = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : '';
            console.log(prop, order)
            searchQuery.value.sortField = prop
            searchQuery.value.sortOrder = sortOrder
            fetchData()
        };
        const assignRow2Form = (row) => {
            Object.assign(form, row)
        }
        const editRow = async (row) => {
            console.log(row)
            updateGptDialogVisible.value = true
            assignRow2Form(row)
        }

        const dialogWidth = ref(isMobile() ? '90%' : '600px');
        const formLabelWidth = ref(isMobile() ? '80px' : '120px');
        const tableMaxHeight = ref(window.innerHeight - 300);

        const openBatchAddDialog = () => {
            batchAddDialogVisible.value = true;
        };

        const submitBatchForm = async () => {
            if (!batchForm.officialSession.trim()) {
                ElMessage.warning('请输入rt');
                return;
            }
            const loading = ElLoading.service({
                lock: true,
                text: '批量添加中...',
                background: 'rgba(0, 0, 0, 0.7)',
            });
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 30000);
                
                let config = {
                    headers: {
                        'Content-Type': 'text/plain'
                    },
                    signal: controller.signal
                };
                
                const res = await api.post("/api/chatGpt/session/addSessionBatch", batchForm.officialSession, config);
                clearTimeout(timeoutId);
                
                if (res.data.code === 0) {
                    ElMessage.success('批量添加任务已提交,请稍后刷新查看');
                    batchAddDialogVisible.value = false;
                    fetchData();
                    batchForm.officialSession = '';
                } else {
                    ElMessage.error(res.data.msg);
                }
            } catch (error) {
                if (error.name === 'AbortError') {
                    ElMessage.error('添加超时，请重试');
                } else {
                    ElMessage.error(error.message || '添加失败');
                }
            } finally {
                loading.close();
            }
        };

        return {
            tableData,
            handlePageChange,
            searchQuery,
            formatDate,
            addGptDialogVisible,
            updateGptDialogVisible,
            form,
            rules,
            editRules,
            submitForm,
            deleteAccount,
            fetchData,
            editRow,
            submitUpdateForm,
            sortCustomer,
            updateUser,
            Search,
            openAddgptDialog,
            handleSelectionChange,
            deleteBatch,
            isMobile,
            dialogWidth,
            formLabelWidth,
            tableMaxHeight,
            batchAddDialogVisible,
            batchForm,
            submitBatchForm,
            openBatchAddDialog,
        };
    },
};
</script>

<style scoped>
.pagination-container {
    padding: 20px;
}

.el-table {
    margin-bottom: 20px;
    text-align: center;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to

/* .fade-leave-active in <2.1.8 */
    {
    opacity: 0;
}

.toolbar {
    margin-bottom: 20px;
}

.toolbar-item {
    margin-bottom: 10px;
}

@media screen and (max-width: 768px) {
    .pagination-container {
        padding: 10px;
    }
    
    .el-table {
        margin-bottom: 10px;
    }
}
</style>