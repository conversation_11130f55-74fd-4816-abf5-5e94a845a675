<template>
    <el-tooltip content="在这里配置绘图的配置" placement="top">
        <el-alert title="绘图配置" type="info" :closable="false"></el-alert>
    </el-tooltip>
    <el-form :model="form"  label-width="auto" style="max-width: 500px;margin-top: 20px;">
        <el-form-item label="开启绘图">
            <el-switch v-model="form.system.enableDraw" placeholder="sk-xxx"></el-switch>
        </el-form-item>
         <el-form-item label="API令牌">
            <el-input v-model="form.system.draw_api_token" placeholder="sk-xxx"></el-input>
        </el-form-item>

        <el-form-item label="画图API BASE_URL">
            <el-input v-model="form.system.draw_api_base_url" placeholder="https://api.example.com"></el-input>
        </el-form-item>
        <el-form-item label="画图MODEL">
            <el-input v-model="form.system.draw_model" placeholder="gpt-image-1"></el-input>
        </el-form-item>
        <el-divider content-position="left">其他配置</el-divider>
        <el-form-item label="注册赠送积分">
            <el-input v-model="form.system.register_gift_points" placeholder="100"></el-input>
        </el-form-item>
        <el-form-item label="画图消耗积分">
            <el-input v-model="form.system.draw_consume_points" placeholder="10"></el-input>
        </el-form-item>
        <el-form-item label="自动清理绘图记录">
            <div style="display: flex; align-items: center; gap: 8px;"><el-input type="number" v-model="form.system.imageRetentionDays" placeholder="清理N天之前的绘图记录"></el-input><el-tag type="info" size="small">天前</el-tag></div>
        </el-form-item>
        <el-form-item label="编辑图片时的图片质量">
            <el-select v-model="form.system.imageQuality" placeholder="请选择图片质量,越高的质量意味着消耗token数量越多">
                <el-option label="高质量" value="high"></el-option>
                <el-option label="中等质量" value="medium"></el-option>
                <el-option label="低质量" value="low"></el-option>
            </el-select>
        </el-form-item>

        <!-- ...继续添加其他配置项... -->
        <el-form-item style="justify-content: flex-end;">
            <div style="justify-content: flex-end;">
                <el-button type="primary" @click="submitSystemConfig">保存</el-button>
            </div>
        </el-form-item>
    </el-form>
</template>

<script setup>
import { reactive, defineProps } from 'vue'
import api from '@/axios'
import { ElMessage } from 'element-plus'
const form = reactive({
    system: {
        draw_api_token: '',
        draw_api_base_url: '',
        draw_model: '',
        register_gift_points: '',
        draw_consume_points: '',
        imageRetentionDays: '',
        enableDraw: true,
    }
})
const submitSystemConfig = () => {
    const submitData = { ...form.system }

    api.post('/api/config/addOrUpdate', submitData).then(res => {
        ElMessage.success('保存成功')
    }).catch(err => {
        ElMessage.error('保存失败')
    })
}
const getSystemConfig = () => {
    api.post('/api/config/get', Object.keys(form.system)).then(res => {
        form.system = res.data.data
        form.system.showVersion = form.system.showVersion === 'true'
        form.system.enableDraw = form.system.enableDraw === 'true'
    }).catch(err => {
        ElMessage.error('获取系统配置失败')
    })
}
getSystemConfig()

</script>