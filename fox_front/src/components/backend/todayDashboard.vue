<template>
<div class="dashboard">
  <el-row :gutter="20">
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>总用户数量</span>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ summary.totalUserCount }}</span>
          <span class="label">人</span>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>今日新增用户</span>
            <el-icon><User /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ summary.todayNewUserCount }}</span>
          <span class="label">人</span>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>今日订单数</span>
            <el-icon><List /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ summary.todayOrderCount }}</span>
          <span class="label">单</span>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>今日未支付订单</span>
            <el-icon><Warning /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ summary.noPayTodayOrderCount }}</span>
          <span class="label">单</span>
        </div>
      </el-card>
    </el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>今日收入</span>
            <el-icon><Money /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ summary.todayIncome }}</span>
          <span class="label">元</span>
        </div>
      </el-card>
    </el-col>

  </el-row>

  <!-- 使用统计 -->
  <el-row :gutter="20">
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>在线用户数量</span>
            <el-icon><User /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ usageStats.onlineUserCount }}</span>
          <span class="label">人</span>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>今日活跃用户</span>
            <el-icon><User /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ usageStats.activeUsers }}</span>
          <span class="label">人</span>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>总使用次数</span>
            <el-icon><ChatDotRound /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ usageStats.totalUsage }}</span>
          <span class="label">次</span>
        </div>
      </el-card>
    </el-col>
    <el-col :span="6">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>人均使用次数</span>
            <el-icon><DataAnalysis /></el-icon>
          </div>
        </template>
        <div class="card-content">
          <span class="number">{{ usageStats.avgUsagePerUser }}</span>
          <span class="label">次/人</span>
        </div>
      </el-card>
    </el-col>
  </el-row>

  <!-- 模型使用统计 -->
  <el-row :gutter="20" v-if="usageStats.modelStats && usageStats.modelStats.length > 0">
    <el-col :span="24">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>模型使用统计</span>
            <el-icon><PieChart /></el-icon>
          </div>
        </template>
        <div class="model-stats">
          <div ref="modelChart" class="model-chart"></div>
        </div>
      </el-card>
    </el-col>
  </el-row>

  <!-- 用户使用排行榜 -->
  <el-row :gutter="20" v-if="userRanking.rankingData && userRanking.rankingData.length > 0">
    <el-col :span="24">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>今日用户使用排行榜（前10名）</span>
            <el-icon><DataAnalysis /></el-icon>
          </div>
        </template>
        <div class="user-ranking">
          <div v-for="(user, index) in userRanking.rankingData" :key="index" class="ranking-item">
            <div class="ranking-info">
              <div class="rank-number" :class="getRankClass(index)">
                {{ index + 1 }}
              </div>
              <div class="user-info">
                <div class="user-token">{{ user.userToken.substring(0, 12) }}...</div>
              </div>
            </div>
            <div class="usage-count">
              <span class="count-number">{{ user.usageCount }}</span>
              <span class="count-label">次</span>
            </div>
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
</div>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import api from '@/axios'
import { User, Plus, List, Warning, Money, ChatDotRound, DataAnalysis, PieChart } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const summary = ref({})
const usageStats = ref({
  activeUsers: 0,
  totalUsage: 0,
  avgUsagePerUser: '0.00',
  modelStats: [],
  totalModelUsage: 0
})

const userRanking = ref({
  rankingData: [],
  totalRankedUsers: 0
})

const modelChart = ref(null)
let chartInstance = null

const getSummary = async () => {
  try {
    const res = await api.get('/api/dashboard/summary')
    if (res.data.code === 0) {
      const data = res.data.data
      summary.value = data
    }
  } catch (error) {
    console.error('获取用户统计失败:', error)
  }
}

const getUsageStats = async () => {
  try {
    const res = await api.get('/api/dashboard/today-usage')
    if (res.data.code === 0) {
      usageStats.value = res.data.data
      // 等待DOM更新后初始化图表
      await nextTick()
      initModelChart()
    }
  } catch (error) {
    console.error('获取使用统计失败:', error)
  }
}

const getUserRanking = async () => {
  try {
    const res = await api.get('/api/dashboard/today-user-ranking')
    if (res.data.code === 0) {
      userRanking.value = res.data.data
    }
  } catch (error) {
    console.error('获取用户排行榜失败:', error)
  }
}

// 初始化模型使用统计饼图
const initModelChart = () => {
  if (!modelChart.value || !usageStats.value.modelStats || usageStats.value.modelStats.length === 0) {
    return
  }

  // 销毁之前的图表实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 初始化图表
  chartInstance = echarts.init(modelChart.value)

  // 准备数据
  const chartData = usageStats.value.modelStats.map((model, index) => ({
    name: model.model,
    value: model.count,
    itemStyle: {
      color: getProgressColor(index)
    }
  }))

  // 配置选项
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '模型使用统计',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
            formatter: '{b}\n{c}次'
          }
        },
        labelLine: {
          show: false
        },
        data: chartData
      }
    ]
  }

  // 设置配置项
  chartInstance.setOption(option)

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
}

// 获取进度条颜色
const getProgressColor = (index) => {
  const colors = [
    '#409EFF',
    '#67C23A',
    '#E6A23C',
    '#F56C6C',
    '#909399',
    '#36D1DC',
    '#FF6B6B',
    '#4ECDC4'
  ]
  return colors[index % colors.length]
}

// 获取排名样式类
const getRankClass = (index) => {
  if (index === 0) return 'rank-first'
  if (index === 1) return 'rank-second'
  if (index === 2) return 'rank-third'
  return 'rank-normal'
}

onMounted(() => {
  getSummary()
  getUsageStats()
  getUserRanking()
})

onUnmounted(() => {
  // 清理图表实例
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  // 移除窗口大小变化监听器
  window.removeEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
})
</script>

<style scoped>
.dashboard {
  padding: 12px;
}

.box-card {
  margin-bottom: 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.box-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
}

.card-header span {
  font-size: 13px;
  font-weight: 600;
  color: #606266;
}

.card-header .el-icon {
  font-size: 14px;
  color: #409EFF;
}

.card-content {
  text-align: center;
  padding: 12px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.number {
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(45deg, #409EFF, #36D1DC);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1.1;
}

.label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.el-col {
  animation: fadeInUp 0.5s ease forwards;
}

.el-col:nth-child(1) { animation-delay: 0.1s; }
.el-col:nth-child(2) { animation-delay: 0.2s; }
.el-col:nth-child(3) { animation-delay: 0.3s; }
.el-col:nth-child(4) { animation-delay: 0.4s; }
.el-col:nth-child(5) { animation-delay: 0.5s; }

.model-stats {
  padding: 16px;
}

.model-chart {
  width: 100%;
  height: 300px;
  min-height: 300px;
}

/* 用户排行榜样式 */
.user-ranking {
  padding: 16px;
}

.ranking-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.ranking-item:hover {
  background: #e3f2fd;
  transform: translateX(4px);
}

.ranking-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.rank-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  color: white;
}

.rank-first {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.rank-second {
  background: linear-gradient(45deg, #C0C0C0, #A9A9A9);
  box-shadow: 0 2px 8px rgba(192, 192, 192, 0.3);
}

.rank-third {
  background: linear-gradient(45deg, #CD7F32, #B8860B);
  box-shadow: 0 2px 8px rgba(205, 127, 50, 0.3);
}

.rank-normal {
  background: linear-gradient(45deg, #409EFF, #36D1DC);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-token {
  font-size: 12px;
  color: #909399;
  font-family: monospace;
}

.usage-count {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.count-number {
  font-size: 20px;
  font-weight: bold;
  background: linear-gradient(45deg, #409EFF, #36D1DC);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.count-label {
  font-size: 12px;
  color: #909399;
}

/* 排行榜动画 */
.ranking-item:nth-child(1) { animation-delay: 0.1s; }
.ranking-item:nth-child(2) { animation-delay: 0.2s; }
.ranking-item:nth-child(3) { animation-delay: 0.3s; }
.ranking-item:nth-child(4) { animation-delay: 0.4s; }
.ranking-item:nth-child(5) { animation-delay: 0.5s; }
.ranking-item:nth-child(6) { animation-delay: 0.6s; }
.ranking-item:nth-child(7) { animation-delay: 0.7s; }
.ranking-item:nth-child(8) { animation-delay: 0.8s; }
.ranking-item:nth-child(9) { animation-delay: 0.9s; }
.ranking-item:nth-child(10) { animation-delay: 1.0s; }
</style>
