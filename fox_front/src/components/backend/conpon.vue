<template>
    <div class="coupon-container" style="margin-bottom: 10px;">
        <!-- 工具栏部分 -->
        <el-row :gutter="20">
            <el-col :span="4">
                <el-input v-model="searchQuery.query" placeholder="请输入优惠码/备注"></el-input>
            </el-col>
            <el-col :span="2">
                <el-button type="primary" @click="fetchData" :icon="Search">刷新</el-button>
            </el-col>
            <el-col :span="4">
                <el-button type="primary" @click="openAddCouponDialog">添加优惠券</el-button>
            </el-col>
        </el-row>

        <el-table :data="tableData" border @sort-change="sortCoupons" style="margin-top: 10px;" max-height="500px"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection"/>
            <el-table-column prop="id" label="ID" width="50" />
            <el-table-column prop="couponCode" label="优惠券代码" width="150" />
            <el-table-column prop="discountAmount" label="折扣金额" width="100" />
            <el-table-column prop="discountPercentage" label="折扣百分比" width="100" />
            <el-table-column prop="remark" overflow-showtooltip label="备注" />
            <el-table-column prop="expirationTime" label="到期时间" width="150" :formatter="formatDate" />

            <el-table-column prop="createdAt" label="创建时间" sortable="custom" width="150" :formatter="formatDate" />
            <el-table-column prop="updatedAt" label="修改时间" sortable="custom" width="150" :formatter="formatDate" />
            <el-table-column label="操作" width="180">
                <template #default="scope">
                    <el-button size="small" type="primary" @click="editCoupon(scope.row)">编辑</el-button>
                    <el-button size="small" type="danger" @click="deleteCoupon(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination v-model:currentPage="searchQuery.pageNum" :page-size="searchQuery.pageSize"
            :total="searchQuery.totalItems" layout="total, prev, pager, next" :page-sizes="[10, 20, 50, 100]"
            @current-change="handlePageChange" background />

        <!-- 添加优惠券弹窗 -->
        <el-dialog title="添加优惠券" v-model="addCouponVisible" width="500px">
            <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
                <el-form-item label="优惠券代码" prop="couponCode">
                    <el-row :gutter="20">
                        <el-col :span="20">
                            <el-input v-model="form.couponCode" placeholder="请输入优惠券代码"></el-input>
                        </el-col>
                        <el-col :span="4">
                            <el-button type="primary" size="mini" @click="generateUUID">自动生成</el-button>
                        </el-col>
                    </el-row>

                </el-form-item>

                <el-form-item label="折扣金额" prop="discountAmount">
                    <el-input v-model="form.discountAmount" placeholder="请输入折扣金额" type="number"></el-input>
                </el-form-item>

                <el-form-item label="折扣百分比" prop="discountPercentage">
                    <el-input v-model="form.discountPercentage" placeholder="请输入折扣百分比" type="number"></el-input>
                </el-form-item>

                <el-form-item label="到期时间" prop="expirationTime">
                    <el-date-picker v-model="form.expirationTime" type="datetime" placeholder="选择到期时间" />
                </el-form-item>
                <el-form-item label="适用订阅" prop="subscriptionTypes">
                    <el-select v-model="form.salesPlans" multiple placeholder="选择适用订阅">
                        <el-option v-for="type in salesPlans" :key="type.id" :label="type.name" :value="type.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="描述" prop="remark">
                    <el-input type="textarea" v-model="form.remark" placeholder="请输入描述"></el-input>
                </el-form-item>
            </el-form>

            <span slot="footer" class="dialog-footer">
                <el-button @click="addCouponVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </span>
        </el-dialog>

        <!-- 修改优惠券弹窗 -->
        <el-dialog title="修改优惠券" v-model="updateCouponVisible" width="500px">
            <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">

                <el-form-item label="优惠券代码" prop="couponCode">
                    <el-input v-model="form.couponCode" placeholder="请输入优惠券代码"></el-input>
                </el-form-item>

                <el-form-item label="折扣金额" prop="discountAmount">
                    <el-input v-model="form.discountAmount" placeholder="请输入折扣金额" type="number"></el-input>
                </el-form-item>

                <el-form-item label="折扣百分比" prop="discountPercentage">
                    <el-input v-model="form.discountPercentage" placeholder="请输入折扣百分比" type="number"></el-input>
                </el-form-item>

                <el-form-item label="到期时间" prop="expirationTime">
                    <el-date-picker v-model="form.expirationTime" type="datetime" placeholder="选择到期时间" />
                </el-form-item>
                <el-form-item label="适用订阅" prop="subscriptionTypes">
                    <el-select v-model="form.salesPlan" multiple placeholder="选择适用订阅">
                        <el-option v-for="type in salesPlans" :key="type.id" :label="type.name" :value="type.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="描述" prop="remark">
                    <el-input type="textarea" v-model="form.remark" placeholder="请输入描述"></el-input>
                </el-form-item>
            </el-form>

            <span slot="footer" class="dialog-footer">
                <el-button @click="updateCouponVisible = false">取消</el-button>
                <el-button type="primary" @click="submitUpdateForm">确定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import { ref, reactive, toRefs, getCurrentInstance } from 'vue';
import api from '@/axios';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { v4 as uuidv4 } from 'uuid';
import { parseQuery } from 'vue-router';
export default {
    setup() {
        const { proxy } = getCurrentInstance();
        const addCouponVisible = ref(false);
        const updateCouponVisible = ref(false);
        const tableData = ref([]);
        const selected = ref([]);

        const handleSelectionChange = (val) => {
            selected.value = val.map(item => item.id);
        };
        const generateUUID = () => {
            form.couponCode = uuidv4()
                .replace('-', '')
                .toUpperCase()
                .substring(0, 12); // 生成 UUID 并赋值给 couponCode
        };

        const form = reactive({
            couponCode: '',
            discountAmount: 0,
            discountPercentage: 0,
            expirationTime: '',
            salesPlan: [],
            remark: ''
        });

        const searchQuery = ref({
            pageNum: 1,
            pageSize: 10,
            totalItems: 0,
        });

        const fetchData = async () => {
            const res = await api.post("/api/coupon/page", searchQuery.value);
            tableData.value = res.data.data;
            searchQuery.value.totalItems = res.data.total;
        };
        fetchData()

        const formatDate = (row, col, cellvalue) => {
            return proxy.$dateFormat(cellvalue)
        };

        const submitForm = async () => {
            await proxy.$refs.formRef.validate();
            const formCopy = { ...form };
            formCopy.salesPlan = formCopy.salesPlan && formCopy.salesPlan.join(",");
            const res = await api.post("/api/coupon/addOrUpdate", formCopy);
            if (res.data.code === 0) {
                ElMessage.success('添加成功');
                addCouponVisible.value = false;
                fetchData();
                Object.assign(form, { couponCode: '', discountAmount: 0, discountPercentage: 0, expirationTime: '' });
            } else {
                ElMessage.error(res.data.msg);
            }
        };

        const submitUpdateForm = async () => {
            await proxy.$refs.formRef.validate();
            const formCopy = { ...form };
            formCopy.salesPlan = formCopy.salesPlan && formCopy.salesPlan.join(",");
            const res = await api.post("/api/coupon/addOrUpdate", formCopy);
            if (res.data.code === 0) {
                ElMessage.success('修改成功');
                updateCouponVisible.value = false;
                fetchData();
            } else {
                ElMessage.error(res.data.msg);
            }
        };

        const deleteCoupon = async (row) => {
            ElMessageBox.confirm('确定删除该优惠券?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(async () => {
                const res = await api.post("/api/coupon/deleteById?id=" + row.id);
                if (res.data.code === 0) {
                    ElMessage.success('删除成功');
                    fetchData();
                } else {
                    ElMessage.error(res.data.msg);
                }
            });
        };

        const sortCoupons = ({ prop, order }) => {
            // 处理排序逻辑
            fetchData();
        };

        const openAddCouponDialog = () => {
            addCouponVisible.value = true;
        };

        const editCoupon = (row) => {
            Object.assign(form, row);
            form.salesPlan = row.salesPlan && row.salesPlan.split(',').map(item => parseInt(item));
            updateCouponVisible.value = true;
        };

        const rules = ref({
            couponCode: [{ required: true, message: '请输入优惠券代码', trigger: 'blur' }],

        });
        const salesPlans = ref([]);
        const fetchSalesPlans = async () => {
            const payload = {
                pageNum: 1,
                pageSize: 10000
            }
            const res = await api.post('/api/salesPlan/page', payload)

            salesPlans.value = res.data.data;
        };
        fetchSalesPlans();
        const handlePageChange = (page) => {
            searchQuery.pageNum = page;
            fetchData();
        };
        return {
            tableData,
            fetchData,
            searchQuery,
            formatDate,
            addCouponVisible,
            submitForm,
            deleteCoupon,
            sortCoupons,
            Search,
            openAddCouponDialog,
            updateCouponVisible,
            form,
            editCoupon,
            submitUpdateForm,
            rules,
            generateUUID,
            salesPlans,
            handlePageChange,
            handleSelectionChange
        };
    },
};
</script>

<style scoped>
.coupon-container {
    padding: 20px;
}

.el-table {
    margin-bottom: 20px;
    text-align: center;
}
</style>