<template>
  <el-dialog
    v-model="localVisible"
    :show-close="false"
    width="480px"
    :before-close="handleClose"
    class="exchange-dialog"
    :destroy-on-close="true"
    :append-to-body="true"
    :modal="true"
    :lock-scroll="true"
    align-center
  >
    <template #header>
      <span></span>
    </template>
    <div class="modern-exchange-container">
      <!-- 头部区域 -->
      <div class="exchange-header-modern">
        <div class="exchange-title">{{ $t('activation.tips.title') }}</div>
        <div class="exchange-input-row">
          <div class="modern-input-wrapper">
            <input
              v-model="activationCode"
              :placeholder="$t('activation.enterCode')"
              :maxlength="64"
              @keyup.enter="handleRedeem"
              class="modern-input"
            />
          </div>
          <div
            class="modern-exchange-button"
            :class="{ loading: exchangeLoading }"
            @click="handleRedeem"
          >
            <div v-if="!exchangeLoading" class="button-text">
              {{ $t('activation.exchangeNow') }}
            </div>
            <div v-else class="button-text">
              {{ $t('activation.exchanging') }}
            </div>
          </div>
        </div>
      </div>
      <!-- 底部提示区域 -->
      <div class="exchange-tips-modern">
        <div class="tips-text">
          {{ $t('activation.tips.timeLimit') }}<br />
          {{ $t('activation.tips.oneTime') }}<br />
          {{ $t('activation.tips.contact') }}
        </div>
      </div>
    </div>
    <template #footer>
      <span></span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'
import api from '@/axios'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
})
const emit = defineEmits(['update:visible', 'success'])
const { t } = useI18n()

const localVisible = ref(props.visible)
const activationCode = ref('')
const exchangeLoading = ref(false)

watch(() => props.visible, (val) => {
  localVisible.value = val
})
watch(localVisible, (val) => {
  emit('update:visible', val)
  if (!val) activationCode.value = ''
})

const handleRedeem = async () => {
  if (!activationCode.value) {
    ElMessage.warning(t('activation.enterCode'))
    return
  }
  try {
    exchangeLoading.value = true
    const res = await api.post('/api/activationCode/exchange?activationCode=' + activationCode.value)
    if (res.data.code === 0) {
      ElMessage.success(t('activation.exchangeSuccess'))
      activationCode.value = ''
      localVisible.value = false
      emit('success')
    } else {
      ElMessage.error(res.data.msg)
    }
  } catch (error) {
    ElMessage.error(t('activation.exchangeFailed'))
  } finally {
    exchangeLoading.value = false
  }
}

const handleClose = () => {
  localVisible.value = false
  activationCode.value = ''
}
</script>

<style scoped>
:global(.el-dialog.exchange-dialog) {
    background: transparent !important;
    border-radius: 16px !important;
    padding: 0 !important;
    margin: 0 !important;
    box-shadow: none !important;
    border: none !important;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin-top: 0 !important;
    margin-left: 0 !important;
}
:global(.el-dialog.exchange-dialog .el-dialog__header) {
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: none !important;
    display: none !important;
}
:global(.el-dialog.exchange-dialog .el-dialog__body) {
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}
:global(.el-dialog.exchange-dialog .el-dialog__footer) {
    padding: 0 !important;
    margin: 0 !important;
    border-top: none !important;
    display: none !important;
}
.modern-exchange-container {
    width: 480px;
    background: white;
    box-shadow: 0px 7px 22px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    border-radius: 16px;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    display: inline-flex;
    margin: 0;
    position: relative;
}
.exchange-header-modern {
    align-self: stretch;
    padding: 24px;
    background: linear-gradient(180deg, #1EAFFF 0%, #C8B2FF 100%), #5CC4FD;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 16px;
    display: flex;
}
.exchange-title {
    align-self: stretch;
    text-align: center;
    color: white;
    font-size: 24px;
    font-family: MiSans;
    font-weight: 450;
    line-height: 26.40px;
    word-wrap: break-word;
}
.exchange-input-row {
    align-self: stretch;
    height: 48px;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 12px;
    display: inline-flex;
}
.modern-input-wrapper {
    flex: 1 1 0;
    align-self: stretch;
    padding: 16px;
    background: rgba(246, 246, 246, 0.90);
    box-shadow: 0px 7px 22px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    border-radius: 12px;
    backdrop-filter: blur(40px);
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    display: flex;
}
.modern-input {
    width: 100%;
    border: none;
    outline: none;
    background: transparent;
    color: black;
    font-size: 15px;
    font-family: MiSans;
    font-weight: 330;
    word-wrap: break-word;
}
.modern-input::placeholder {
    opacity: 0.30;
    color: black;
    font-size: 15px;
    font-family: MiSans;
    font-weight: 330;
    word-wrap: break-word;
}
.modern-exchange-button {
    align-self: stretch;
    padding-left: 24px;
    padding-right: 24px;
    padding-top: 8px;
    padding-bottom: 8px;
    background: white;
    box-shadow: 0px 7px 22px rgba(0, 0, 0, 0.25);
    border-radius: 12px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    display: flex;
    cursor: pointer;
    transition: all 0.3s ease;
}
.modern-exchange-button:hover {
    transform: translateY(-2px);
    box-shadow: 0px 10px 25px rgba(0, 0, 0, 0.3);
}
.modern-exchange-button.loading {
    opacity: 0.7;
    cursor: not-allowed;
}
.modern-exchange-button .button-text {
    color: #5CC4FD;
    font-size: 16px;
    font-family: MiSans;
    font-weight: 520;
    line-height: 22.40px;
    word-wrap: break-word;
}
.exchange-tips-modern {
    align-self: stretch;
    padding: 24px;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    display: inline-flex;
}
.tips-text {
    opacity: 0.50;
    color: black;
    font-size: 16px;
    font-family: MiSans;
    font-weight: 380;
    line-height: 32px;
    word-wrap: break-word;
}
@media (max-width: 768px) {
    .modern-exchange-container {
        width: 90vw;
        max-width: 400px;
    }
    .exchange-header-modern {
        padding: 20px;
        gap: 12px;
    }
    .exchange-title {
        font-size: 20px;
        line-height: 22px;
    }
    .exchange-input-row {
        flex-direction: column;
        height: auto;
        gap: 8px;
    }
    .modern-exchange-button {
        padding: 12px 24px;
    }
    .exchange-tips-modern {
        padding: 20px;
    }
    .tips-text {
        font-size: 14px;
        line-height: 28px;
    }
}
/* 现代化兑换弹窗样式 - 使用全局样式穿透 */
@media (max-width: 768px) {
    :global(.el-dialog.exchange-dialog) {
        --el-dialog-width: 90% !important;
    }
}

/* 使用全局选择器确保样式穿透 */
:global(.el-overlay .el-overlay-dialog .el-dialog.exchange-dialog) {
    background: transparent !important;
    border-radius: 16px !important;
    padding: 0 !important;
    margin: 0 !important;
    box-shadow: none !important;
    border: none !important;
}

:global(.el-overlay .el-overlay-dialog .el-dialog.exchange-dialog .el-dialog__header) {
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: none !important;
    height: 0 !important;
    min-height: 0 !important;
}

:global(.el-overlay .el-overlay-dialog .el-dialog.exchange-dialog .el-dialog__body) {
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

:global(.el-overlay .el-overlay-dialog .el-dialog.exchange-dialog .el-dialog__footer) {
    padding: 0 !important;
    margin: 0 !important;
    border-top: none !important;
    height: 0 !important;
    min-height: 0 !important;
}

/* 备用方案：使用更高权重的全局选择器 */
:global(.el-dialog__wrapper .el-dialog.exchange-dialog) {
    background: transparent !important;
    border-radius: 16px !important;
    padding: 0 !important;
    margin: 0 !important;
    box-shadow: none !important;
    border: none !important;
}

:global(.el-dialog__wrapper .el-dialog.exchange-dialog .el-dialog__header) {
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: none !important;
    display: none !important;
}

:global(.el-dialog__wrapper .el-dialog.exchange-dialog .el-dialog__body) {
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

:global(.el-dialog__wrapper .el-dialog.exchange-dialog .el-dialog__footer) {
    padding: 0 !important;
    margin: 0 !important;
    border-top: none !important;
    display: none !important;
}

/* 最强力的备用方案 */
:global(.el-dialog.exchange-dialog) {
    background: transparent !important;
    border-radius: 16px !important;
    padding: 0 !important;
    margin: 0 !important;
    box-shadow: none !important;
    border: none !important;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin-top: 0 !important;
    margin-left: 0 !important;
}

:global(.el-dialog.exchange-dialog .el-dialog__header) {
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: none !important;
    display: none !important;
}

:global(.el-dialog.exchange-dialog .el-dialog__body) {
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

:global(.el-dialog.exchange-dialog .el-dialog__footer) {
    padding: 0 !important;
    margin: 0 !important;
    border-top: none !important;
    display: none !important;
}

.modern-exchange-container {
    width: 480px;
    background: white;
    box-shadow: 0px 7px 22px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    border-radius: 16px;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    display: inline-flex;
    margin: 0;
    position: relative;
}

.exchange-header-modern {
    align-self: stretch;
    padding: 24px;
    background: linear-gradient(180deg, #1EAFFF 0%, #C8B2FF 100%), #5CC4FD;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 16px;
    display: flex;
}

.exchange-title {
    align-self: stretch;
    text-align: center;
    color: white;
    font-size: 24px;
    font-family: MiSans;
    font-weight: 450;
    line-height: 26.40px;
    word-wrap: break-word;
}

.exchange-input-row {
    align-self: stretch;
    height: 48px;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 12px;
    display: inline-flex;
}

.modern-input-wrapper {
    flex: 1 1 0;
    align-self: stretch;
    padding: 16px;
    background: rgba(246, 246, 246, 0.90);
    box-shadow: 0px 7px 22px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    border-radius: 12px;
    backdrop-filter: blur(40px);
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    display: flex;
}

.modern-input {
    width: 100%;
    border: none;
    outline: none;
    background: transparent;
    color: black;
    font-size: 15px;
    font-family: MiSans;
    font-weight: 330;
    word-wrap: break-word;
}

.modern-input::placeholder {
    opacity: 0.30;
    color: black;
    font-size: 15px;
    font-family: MiSans;
    font-weight: 330;
    word-wrap: break-word;
}

.modern-exchange-button {
    align-self: stretch;
    padding-left: 24px;
    padding-right: 24px;
    padding-top: 8px;
    padding-bottom: 8px;
    background: white;
    box-shadow: 0px 7px 22px rgba(0, 0, 0, 0.25);
    border-radius: 12px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    display: flex;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modern-exchange-button:hover {
    transform: translateY(-2px);
    box-shadow: 0px 10px 25px rgba(0, 0, 0, 0.3);
}

.modern-exchange-button.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.modern-exchange-button .button-text {
    color: #5CC4FD;
    font-size: 16px;
    font-family: MiSans;
    font-weight: 520;
    line-height: 22.40px;
    word-wrap: break-word;
}

.exchange-tips-modern {
    align-self: stretch;
    padding: 24px;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    display: inline-flex;
}

.tips-text {
    opacity: 0.50;
    color: black;
    font-size: 16px;
    font-family: MiSans;
    font-weight: 380;
    line-height: 32px;
    word-wrap: break-word;
}

@media (max-width: 768px) {
    .modern-exchange-container {
        width: 90vw;
        max-width: 400px;
    }

    .exchange-header-modern {
        padding: 20px;
        gap: 12px;
    }

    .exchange-title {
        font-size: 20px;
        line-height: 22px;
    }

    .exchange-input-row {
        flex-direction: column;
        height: auto;
        gap: 8px;
    }

    .modern-exchange-button {
        padding: 12px 24px;
    }

    .exchange-tips-modern {
        padding: 20px;
    }

    .tips-text {
        font-size: 14px;
        line-height: 28px;
    }
}
</style>
