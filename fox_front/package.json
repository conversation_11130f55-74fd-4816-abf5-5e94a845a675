{"name": "front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.2", "@vueuse/core": "^12.3.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.7.7", "echarts": "^5.6.0", "element-plus": "^2.8.3", "moment": "^2.30.1", "pinia": "^2.2.2", "uuid": "^11.0.3", "vfonts": "github:07akioni/vfonts", "vue": "^3.4.37", "vue-i18n": "^11.1.5", "vue-router": "^4.4.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.2", "vite": "^5.4.1", "vite-plugin-externals": "^0.6.2", "vite-svg-loader": "^5.1.0"}}